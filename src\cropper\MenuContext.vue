<script setup lang="ts">
import { defineEmits } from 'vue';
import { directive as vTippy } from 'vue-tippy';
import { vLongPress } from './vLongPress';

const emit = defineEmits(['crop']);

import ArrowDown from './icon/arrow-down.svg';
import ArrowH from './icon/arrow-h.svg';
import ArrowLeft from './icon/arrow-left.svg';
import ArrowRight from './icon/arrow-right.svg';
import ArrowUp from './icon/arrow-up.svg';
import ArrowV from './icon/arrow-v.svg';
import RotateLeft from './icon/rotate-left.svg';
import RotateRight from './icon/rotate-right.svg';
import SearchMinus from './icon/search-minus.svg';
import SearchPlus from './icon/search-plus.svg';
import Reload from './icon/reload.svg';

const duration = '0:100';

interface IconAction {
  src: string;
  content: string;
  placement: string;
  action?: () => void;
  longPressAction?: () => void;
}

const icons: IconAction[] = [
  {
    src: ArrowUp,
    content: '上移（可长按）',
    placement: 'left-start',
    longPressAction: () => emit('crop', 'move', 0, -10),
  },
  {
    src: ArrowDown,
    content: '下移（可长按）',
    placement: 'right-start',
    longPressAction: () => emit('crop', 'move', 0, 10),
  },
  {
    src: ArrowLeft,
    content: '左移（可长按）',
    placement: 'left-start',
    longPressAction: () => emit('crop', 'move', -10, 0),
  },
  {
    src: ArrowRight,
    content: '右移（可长按）',
    placement: 'right-start',
    longPressAction: () => emit('crop', 'move', 10, 0),
  },
  {
    src: ArrowH,
    content: '水平翻转',
    placement: 'left-start',
    action: () => emit('crop', 'scaleX'),
  },
  {
    src: ArrowV,
    content: '垂直翻转',
    placement: 'right-start',
    action: () => emit('crop', 'scaleY'),
  },
  {
    src: RotateLeft,
    content: '逆时针旋转',
    placement: 'left-start',
    action: () => emit('crop', 'rotate', [-45]),
  },
  {
    src: RotateRight,
    content: '顺时针旋转',
    placement: 'right-start',
    action: () => emit('crop', 'rotate', [45]),
  },
  {
    src: SearchPlus,
    content: '放大（可长按）',
    placement: 'left-start',
    longPressAction: () => emit('crop', 'zoom', [0.1]),
  },
  {
    src: SearchMinus,
    content: '缩小（可长按）',
    placement: 'right-start',
    longPressAction: () => emit('crop', 'zoom', [-0.1]),
  },
  {
    src: Reload,
    content: '重置',
    placement: 'right-start',
    action: () => emit('crop', 'reset'),
  },
];

const handleIconEvent = (icon: IconAction, eventType: 'click' | 'longPress') => {
  const action = eventType === 'click' ? icon.action : icon.longPressAction;
  action?.();
};

</script>

<template>
  <div class="menu-content">
    <img
      v-for="(icon, index) in icons"
      :key="index"
      v-tippy="{
        content: icon.content,
        placement: icon.placement,
      }"
      v-long-press:[duration]="() => handleIconEvent(icon, 'longPress')"
      :src="icon.src"
      alt=""
      class="icon-item"
      @click="handleIconEvent(icon, 'click')"
    >
  </div>
</template>

<style scoped lang="less">
.icon-item {
  padding: 6px;
  height: 18px;
  width: 18px;
  outline: none;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background-color: rgba(0, 0, 0, 0.06);
  }
}

.menu-content {
  display: flex;
  flex-wrap: wrap;
  width: 60px;
  justify-content: space-between;
}
</style>
