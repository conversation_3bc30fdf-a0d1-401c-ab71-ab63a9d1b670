<template>
  <t-select
    v-model="value"
    class="activity-collaborator-select"
    :popup-props="{overlayClassName: 'activity-collaborator-select-popup'}"
  >
    <t-option
      v-for="collaboratorType in options"
      :key="collaboratorType.id"
      :value="collaboratorType.id"
      :label="collaboratorType.typeName"
    >
      <div class="flex justify-between">
        <span>{{ collaboratorType.typeName }}</span>
        <img
          v-if="collaboratorType.systemType === 'SysTypeDefault'"
          class="delete-btn cursor-pointer"
          src="@renderer/assets/activity/icon_clean.svg"
          alt=""
          @click.stop="deleteCollaboratorType(collaboratorType)"
        >
      </div>
    </t-option>

    <template #panelBottomContent>
      <div class="mt-2 px-8 pt-8 pb-6" style="border-top: 1px solid #ECEFF5">
        <div class="flex items-center gap-4 text-[#4D5EFF] cursor-pointer hover:text-[#707EFF]" @click="open">
          <iconpark-icon class="text-20" name="iconadd" />
          <span>添加类型</span>
        </div>
      </div>
    </template>
  </t-select>

  <t-dialog
    attach="body"
    class="activity-collaborator-type-form-dialog"
    :header="false"
    :visible="visible"
    :close-btn="false"
    :footer="false"
    :z-index="9999"
  >
    <div class="p-24 flex justify-between items-center">
      <div class="text-16 text-[#1A2139] leading-24 font-600">添加类型</div>
      <img
        class="cursor-pointer"
        src="@renderer/assets/activity/icon_dialog_close.svg"
        alt=""
        @click="close"
      >
    </div>

    <t-form
      ref="collaboratorTypeFormRef"
      class="activity-collaborator-type-form px-24 mr-2"
      :data="collaboratorTypeFormData"
      label-align="top"
      @submit="onSubmit"
    >
      <t-form-item name="typeName">
        <t-input v-model="collaboratorTypeFormData.typeName" :maxlength="8" show-limit-number />
      </t-form-item>
    </t-form>

    <div class="py-24 px-24 flex justify-end gap-8">
      <t-button class="w-80" theme="default" @click="close">取消</t-button>
      <t-button
        class="w-80"
        theme="primary"
        :disabled="_.isEmpty(collaboratorTypeFormData.typeName) || submitLoading"
        :loading="submitLoading"
        @click="collaboratorTypeFormRef?.submit"
      >
        确定
      </t-button>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, inject, reactive, ref } from 'vue';
import _ from 'lodash';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { addCollaboratorType, removeCollaboratorType } from '@/api/activity/collaborator';
import { getOpenid } from '@/utils/auth';

const props = defineProps({
  modelValue: {
    type: [Number, String, null, undefined],
    default: '',
  },
  change: {
    type: Function,
    required: true,
  },
  addSuccess: {
    type: Function,
    required: true,
  },
  deleteSuccess: {
    type: Function,
    required: true,
  },
  options: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue', 'change', 'addSuccess', 'deleteSuccess']);

const collaboratorTypeFormRef = ref(null);

const value = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit('update:modelValue', val);
    emit('change', val);
    props.change(val);
  },
});

const submitLoading = ref(false);

const activityFormData = inject('activityFormData');
const selectedTeam = inject('selectedTeam');

const isCreate = computed(() => !activityFormData.id);

const visible = ref(false);

const collaboratorTypeFormData = reactive(({
  typeName: null,
}));

const onSubmit = async () => {
  submitLoading.value = true;

  const [error, res] = await to(addCollaboratorType({
    collaboratorType: collaboratorTypeFormData,
    activityId: isCreate.value ? 0 : activityFormData.id,
    me: {
      openId: getOpenid(),
      cardId: selectedTeam.value.uuid,
      teamId: selectedTeam.value.teamId,
    },
  }));

  if (error) {
    submitLoading.value = false;
    return;
  }

  submitLoading.value = false;

  emit('addSuccess', res.data.data.collaboratorType);
  props.addSuccess(res.data.data.collaboratorType);

  MessagePlugin.success('新建成功');

  close();
};

// 删除协作人类型
const deleteCollaboratorType = (collaboratorType) => {
  const executeRemove = async () => {
    await removeCollaboratorType({
      collaboratorType: {
        typeId: collaboratorType.id,
      },
      activityId: isCreate.value ? 0 : activityFormData.id,
      me: {
        openId: getOpenid(),
        cardId: selectedTeam.value.uuid,
        teamId: selectedTeam.value.teamId,
      },
    });

    emit('deleteSuccess', collaboratorType);
    props.deleteSuccess(collaboratorType);
  };

  // 如果协作人类型已被选择，则弹出确认提示
  const isSelected = activityFormData.members.collaborators.some(
    (collaborator) => collaborator.collaboratorTypeId === collaboratorType.id,
  );

  if (isSelected) {
    const confirmDia = DialogPlugin.alert({
      header: '提示',
      body: '删除失败，当前类型已被使用',
      theme: 'info',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: () => {
        // activityFormData.members.collaborators.forEach((collaborator, index) => {
        //   if (collaborator.collaboratorTypeId === collaboratorType.id) {
        //     activityFormData.members.collaborators[index].collaboratorTypeId = null;
        //   }
        // });
        // executeRemove();

        confirmDia.destroy();
      },
      onCancel: () => {
        confirmDia.destroy();
      },
      onCloseBtnClick: () => {
        confirmDia.destroy();
      },
    });
  } else {
    executeRemove();
  }
};

const open = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;

  setTimeout(() => {
    collaboratorTypeFormData.typeName = null;
  }, 300);
};
</script>

<style lang="less">
.activity-collaborator-select-popup{
  z-index: 999 !important;

  .t-select__list{
    max-height: 165px;
    overflow: auto;
    padding-right: 2px !important;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .t-select-option{
    height: 36px !important;
    width: 100%;

    .delete-btn{
      display: none;
    }
    &:hover{
      .delete-btn{
        display: block;
      }
    }

    &>span{
      display: block;
      width: 100%;
    }
  }
}

.activity-collaborator-type-form-dialog {
  .t-dialog {
    padding: 0;
    width: 390px;

    .t-dialog__body {
      padding: 0;

      .activity-collaborator-type-form {
        max-height: 416px;
        overflow: auto;

        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
        }

        &::-webkit-scrollbar-track {
          background-color: transparent;
        }

        .t-form__label {
          line-height: 22px;
          min-height: 22px;
          padding: 0;

          &.t-form__label--top {
            margin-bottom: 8px;
          }
        }

        .t-form__controls {
          min-height: 22px;
          .t-form__controls-content {
            min-height: 22px;
          }
        }

        .t-form__controls-content {
          .t-input {
            padding-left: 12px;
          }

          .t-input__limit-number {
            color: #acb3c0;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>

<style lang="less" scoped>

</style>
