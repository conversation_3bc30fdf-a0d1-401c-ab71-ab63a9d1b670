import { JSBridgeBase, getEnv } from '../base';
import type { IpcResponse } from '../types';

export class BaseModule {
  protected readonly base: JSBridgeBase;

  constructor() {
    this.base = JSBridgeBase.getInstance();
  }

  protected async send<T = any>(
    action: string,
    data?: any,
    target?: Window | HTMLIFrameElement,
  ): Promise<IpcResponse<T>> {
    console.log('BaseModule: sending message:', { action, data });
    try {
      const response = await this.base.send<T>(action, data, target);
      if (getEnv().isRingkolDesktopApp) {
        console.log('BaseModule: received electron response:', response);
      }
      return response;
    } catch (error) {
      console.error('BaseModule: error sending message:', error);
      throw error;
    }
  }
}
