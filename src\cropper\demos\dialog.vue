/**
 * title: 弹窗裁剪
 * description: 裁剪复合组件，裁剪后可直接上传到 OSS 服务器
 */

<template>
  <div class="demo-wrap">
    <img
      v-if="imgCropper"
      :src="imgCropper"
      alt=""
      class="img"
    >

    <t-button @click="toUpload">
      上传
    </t-button>

    <RkCropperDialog
      ref="cropperRef"
      :size-limit="2"
      @confirm="cropperConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkCropperDialog } from '@rk/unitPark';
import { Button as TButton } from 'tdesign-vue-next';

const imgCropper = ref('');
const cropperRef = ref(null);

const toUpload = () => {
  cropperRef.value.open(imgCropper.value);
};

const cropperConfirm = async (url: string) => {
  imgCropper.value = url;
};
</script>

<style scoped lang="less">
.demo-wrap {
  display: flex;
  align-items: flex-start;
}

.cropper-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.img {
  display: block;
  margin: 0 auto;
  width: 200px;
  height: 200px;
  object-fit: cover;
  border: 1px solid lightgray;
}
</style>
