import type { SizeLimitObj } from 'tdesign-vue-next';

// 基础图片项类型
export interface ImageItem {
  url: string;
  uploading?: boolean;
  progress?: number;
  tempId?: string;
  failed?: boolean;
}

// 裁剪器配置
export interface CropperConfig {
  title?: string;
  outputWidth?: number;
  outputHeight?: number;
  options?: {
    aspectRatio?: number;
    viewMode?: number;
    guides?: boolean;
    autoCropArea?: number;
    [key: string]: any;
  };
}

// 上传配置
export interface UploadConfig {
  /** 上传根目录 */
  rootDir: string;
  /** 最大上传数量 */
  maxCount?: number;
  /** 接受的文件类型 */
  accept?: string;
  /** 图片文件大小限制 */
  sizeLimit?: number | SizeLimitObj;
  /** 是否只读 */
  readonly?: boolean;
}

// 功能配置
export interface FeatureConfig {
  /** 是否可拖拽排序 */
  sortable?: boolean;
  /** 是否自动排序 */
  autoSort?: boolean;
  /** 是否启用图片裁剪功能 */
  enableCrop?: boolean;
  /** 裁剪器配置参数 */
  cropperProps?: CropperConfig;
}

// 样式配置
export interface StyleConfig {
  /** 图片列表容器类名 */
  containerClass?: string;
  /** 是否显示下方错误提示 */
  showErrorMessage?: boolean;
}

// 事件类型
export interface UploadImageEvents {
  'update:modelValue': (value: string[]) => void;
  change: (urls: string[]) => void;
  preview: (payload: { images: string[]; index: number; url: string }) => void;
  click: (payload: { images: string[]; index: number; url: string }) => void;
  sort: (oldIndex: number, newIndex: number) => void;
  'crop-confirm': (url: string, originalUrl: string) => void;
}

// 完整的组件Props类型
export interface UploadImageProps
  extends UploadConfig,
    FeatureConfig,
    StyleConfig {
  /** 绑定的图片URL */
  modelValue?: string | string[];
}
