.ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 滚动条美化
.scrollbar(@size: 2px, @bg: var(--divider-kyy_color_divider_deep, #D5DBE4)) {
  // 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸
  ::-webkit-scrollbar {
    width: @size;
    height: @size;
    //background-color: #f5f5f5;
    cursor: pointer;
  }

  // 定义滚动条轨道 内阴影+圆角
  ::-webkit-scrollbar-track {
    //background-color: #e3e6eb;
  }

  // 定义滑块 内阴影+圆角
  ::-webkit-scrollbar-thumb {
    border-radius: 100px;
    //box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    //-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background: @bg;
    border: 0 solid transparent;
    background-clip: content-box;
  }
}