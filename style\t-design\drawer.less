.drawer-header {
  color: var(--text-kyy-color-text-1, #1A2139);
  height: 56px !important;
  border-bottom-color: transparent !important;
  font-size: 16px;
  font-weight: 600;
}

.drawer-content {
  padding-top: 0 !important;
}

// 打包后跟开发环境优先级顺序不一致
.t-drawer__header {
  color: var(--text-kyy-color-text-1, #1A2139) !important;
  border-bottom: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 24px !important; /* 150% */
}

.t-drawer__close-btn .t-icon {
  width: 24px;
  font-size: 24px;
  height: 24px;
  color: var(--text-kyy_color_text_2, #516082);
}

.t-drawer__footer {
  padding: 16px 24px;
}

.drawerNoShadow {
  .t-drawer__content-wrapper {
    box-shadow: none !important;
  }
}
// lss
.drawerSet {
  .t-drawer__header {
    border-bottom: 0 !important;
    color: var(--kyy_color_modal_title, #1A2139) !important;

  }
  .t-drawer__body {
    padding-top: 6px !important;

  }
  .t-drawer__close-btn {
    // padding-right: 24px;
    right: 24px !important;
    .iconerror {
      font-size: 22px;
      color: #516082;
    }
  }
}






.drawerSet_help {
  .t-drawer__header {
    border-bottom: 0 !important;
    -webkit-app-region: no-drag;
  }
  .t-drawer__body {
    padding-top: 6px !important;
  }
  .t-drawer__close-btn {
    // padding-right: 24px;
    .iconerrror {
      font-size: 22px;
      color: #516082;
    }
  }
}
.drawerSetForm {
  .t-form__controls {
    min-height:  0 !important;
  }
  .t-input__extra {
    font-size: 12px !important;
  }
  .t-drawer__header {
    border-bottom: 0 !important;
    padding: 16px 24px !important;
    min-height: 0 !important;
    color: var(--kyy_color_modal_title, #1A2139) !important;


  }
  .t-drawer__close-btn {
    color: #516082 !important;
    right: 24px !important;
  }
  .t-drawer__body {
    padding-top: 6px ;
    padding-left: 24px;
    padding-right: 24px;

  }
  .t-drawer__close-btn {
    // padding-right: 24px;
    .iconerrror {
      font-size: 22px;
      color: #516082;
    }
  }
  .t-drawer__footer {
    padding: 16px 24px !important
  }

  .t-form__label--top {
    min-height: 0 !important;
    margin-bottom: 8px;
    line-height: 22px;
  }
  .t-form__controls-content {
    min-height: 0!important;
  }
}

.drawerSetBodyNoPaddingBottom {
  .t-drawer__body {
    padding-bottom: 0 !important;

  }
}
.drawerSetBodyNoPadding {
  .t-drawer__body {
    padding-top: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
.drawerSetBodyNoBottomPadding {
  .t-drawer__body {
    padding-bottom: 0 !important;

  }
}

// 知行弹窗默认样式修改
#pop-ups {

  .t-drawer__content-wrapper {
    border-radius: 16px 16px 0 0 !important;

    .t-drawer__body {
      padding: unset;
    }
  }
}

#pop-ups-full {

  .t-drawer__mask {
    border-radius: 16px;
  }

  .t-drawer__content-wrapper {
    // border-radius: 16px !important;
    border-radius: 16px 16px 0 0 !important;

    .t-drawer__body {
      padding: unset;
    }
  }
}
