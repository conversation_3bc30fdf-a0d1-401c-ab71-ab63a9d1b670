.t-button--theme-default {
	border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #D5DBE4) !important;
	background: var(--color-button-border-kyy-color-button-border-bg-default, #FFF) !important;
	color: var(--color-button-border-kyy-color-button-border-text-default, #516082);

}
.t-button--variant-dashed{
	border-style: dashed !important;
}

.t-button--variant-outline.t-button--theme-primary {
  color: var(--brand-kyy_color_brand_default, #4D5EFF);
  border-color: var(--brand-kyy_color_brand_default, #4D5EFF);
}
.w600{
  .t-button__text{
    font-weight: 600 !important;
  }
}
.t-button--variant-text {
	border: none !important;
}

.t-button--variant-outline:hover {
	border: 1px solid var(--lingke-brand-hover, #707EFF) !important;
	background: var(--lingke-brand-12, rgba(76, 94, 255, 0.12)) !important;

	color: var(--color-button-border-kyy-color-button-border-text-hover, #707EFF) !important;
}

.t-date-picker__header {
	.t-button {
		// min-width: 24px;
	}
}

.t-button--variant-base {
	padding: 0 16px;
}

.t-button--variant-outline {
	// padding: 0 16px;

}

.t-button--variant-base.t-button--theme-default:hover {
	//border-radius: var(--radius-kyy-radius-button-s, 4px) !important;
	border: 1px solid var(--lingke-brand-hover, #707EFF) !important;
	background: var(--lingke-brand-12, rgba(76, 94, 255, 0.12)) !important;

	color: var(--color-button-border-kyy-color-button-border-text-hover, #707EFF) !important;


}

.t-button--variant-outline.t-button--theme-primary.t-is-disabled {
	// background: var(--color-button-primary-kyy-color-button-primary-bg-disabled, #C9CFFF);
	color: var(--color-button-secondary-brand-kyy-color-button-secondray-border-text-disabled, #C9CFFF);
	background-color: #fff;
	border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondray-brand-border-disabled, #C9CFFF);


}

.t-button--variant-outline {
	// background-color: var(--color-button-secondary-brand-kyy-color-button-secondray-border-bg-default, #EAECFF);
}

//.t-button--variant-outline.t-button--theme-primary {
//	color: var(--color-button-secondary-brand-kyy-color-button-secondray-border-text-default, #4D5EFF);
//	border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondary-brand-border-dedault, #4D5EFF);
//	background: var(--color-button-secondary-brand-kyy-color-button-secondray-border-bg-default, #EAECFF);
//}
//分页
.t-pagination__jump {
	background-color: #fff !important;
}

.t-pagination__btn:hover {
	border: 1px solid var(--kyy_color_pagination_border_active, #707EFF);
	background: var(--kyy_color_pagination_bg_active, #EAECFF);
	color: #4D5EFF
}

.t-pagination__btn.t-is-disabled,
.t-pagination__btn.t-is-disabled:hover,
.t-pagination__btn.t-is-disabled:active {
	border: 1px solid var(--kyy_color_pagination_border_disabled, #ECEFF5);
	background: var(--kyy_color_pagination_bg_disabled, #ECEFF5);
}

.t-pagination__btn.t-is-disabled {
	border: 1px solid var(--kyy_color_pagination_border_disabled, #ECEFF5);
	background: var(--kyy_color_pagination_bg_disabled, #ECEFF5);
}

.t-pagination__btn {
	border: 1px solid #D5DBE4;
}

.t-button--variant-outline.plain {
	&.t-button--theme-primary {
		background: var(--color-button-secondary-brand-kyy-color-button-secondray-border-bg-default, #EAECFF);
	}

	&.t-button--theme-danger {
		background: var(color-button-secondary-brand-kyy-color-button-secondray-border-text-default, #F7D5DB);
	}

	// ...
}

//按钮
.t-button--variant-base.t-button--theme-primary:hover {
	background: #707EFF !important;
	color: #fff !important;
}
.t-button--variant-base.t-button--theme-primary.t-is-disabled:hover {
	background: #EAECFF !important;
	color: #fff !important;
}

.t-button--variant-base.t-button--theme-primary {
	border: none !important;
}

.t-button--variant-base.t-button--theme-primary:not(.t-is-disabled):not(.t-button--ghost){
  border-radius: var(--radius-kyy-radius-button-s, 4px);
--ripple-color:var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);
font-weight: 600;
color: #fff !important;
}

.t-button .t-icon {
	font-size: 20px;
}

.t-button__text {
	font-size: 14px;
	//font-weight: 600;
}

.t-button--variant-base.t-button--theme-primary:hover {
	color: var(--color-button-secondary-brand-kyy-color-button-secondray-border-text-hover, #707EFF);
	border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondary-brand-border-hover, #707EFF);
	background: var(--color-button-secondary-brand-kyy-color-button-secondray-border-bg-hover, #DBDFFF);
}

.t-button--variant-base.t-button--theme-primary {
	background-color: var(--color-button-primary-kyy-color-button-primary-bg-default, #4D5EFF);

}

.t-button .t-icon+.t-button__text:not(:empty) {
	margin-left: 4px;
}

.t-input-number {
	.t-button {
		// min-width: 30px;
	}

}

.t-button {
	// min-width: 84px;
}

//.t-button--variant-base.t-button--theme-primary {
//	border-radius: 4px;
//}

.ossUpload {
	height: 0;

	.t-button--theme-default {
		display: none;

	}
}

//.t-button {
//	border-radius: 4px;
//}


// 次要
.second-btn {
  background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF);

}

