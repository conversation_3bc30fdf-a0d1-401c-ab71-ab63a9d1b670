# RForm

Form 组件

```tsx
import { ref, computed } from 'vue';
import { RForm } from '@rk/unitPark';
import { Button, Radio } from 'tdesign-vue-next';
import ImageUpload from './ImageUpload.vue';
import { FormItemType } from './type.ts';

import 'tdesign-vue-next/es/style/index.css';
import './style.css';

const formRef = ref(null);
const isStock = ref(false);
const rules = computed(() => {
  if (isStock.value) {
    console.log('rules1');
    return [{ required: true, message: '库存更改设置了，这里也修改了下' }];
  }
  console.log('rules2');
  return [{ required: true, message: '图片必填' }];
});
const formList = ref([
  {
    type: 'input',
    name: 'title',
    label: '标题',
    rules: [{ required: true, message: '标题必填' }],
  },
  {
    type: 'textarea',
    name: 'desc',
    label: '描述',
    attrs: {
      maxlength: 20,
      showWordLimit: true,
    },
    rules: [{ required: true, message: '描述必填' }],
  },
  {
    type: 'component',
    component: () => {
      return <ImageUpload onChange={imgChange} />;
    },
    name: 'pic',
    label: '图片',
    rules: rules.value,
  },
  {
    type: 'slot',
    name: 'stock',
    label: '库存',
    rules: [{ required: true, message: '图片必填' }],
  },
]);
const imgChange = (e) => {
  console.log('imgChange', e);
  formRef.value.setformData({
    pic: '123123',
  });
};

const radioChange = (e) => {
  console.log('radioChange', e);
  isStock.value = true;
  formRef.value.updateRules('pic', [{ required: true, message: '000000' }]);
  formRef.value.setformData({
    stock: true,
  });
  // formRef.value.validate(['stock']);
};

const submit = (e) => {
  console.log('submit', e, formRef.value);
  formRef.value.submit();
};
const onSubmit = (data) => {
  console.log('onSubmit =>', data);
};

export default () => (
  <>
    <RForm
      class="form-box"
      ref={formRef}
      list={formList}
      attrs={{
        // labelWidth: "800px",
        labelAlign: 'top',
      }}
      onSubmit={onSubmit}
      v-slots={{
        stock: (
          <>
            单选
            <Radio onChange={radioChange}>1</Radio>
          </>
        ),
      }}
    ></RForm>
    <Button class="submit" onClick={submit}>
      提交
    </Button>
  </>
);
```

### Props

<API id="RForm" type="props"></API>

### Events

<API id="RForm" type="events"></API>
