/**
 * 安全地解析 JSON 字符串
 * 本函数旨在避免因解析失败而导致的程序崩溃，通过返回一个默认值来保证程序的健壮性
 *
 * @param item 要解析的 JSON 字符串
 * @param defaultValue 解析失败时返回的默认值，默认为 undefined
 * @returns 解析成功则返回解析后的对象，否则返回默认值
 */
export const parseJsonSafely = <T>(
  item: string,
  defaultValue: T = undefined,
): T | undefined => {
  if (!item) return defaultValue;

  try {
    const parsed = JSON.parse(item);
    if (typeof parsed === 'object' && parsed !== null) return parsed;
  } catch (error) {
    console.error('解析 JSON 失败:', error, item);
  }

  return defaultValue;
};
