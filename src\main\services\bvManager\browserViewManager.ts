import { <PERSON><PERSON><PERSON><PERSON>ie<PERSON> } from 'electron';
import { getSDK } from '@lynker-desktop/electron-sdk/main';
import { ViewConfig, PoolConfig } from './types';
import { BrowserViewPool } from './browserViewPool';
import { getUrl } from "../../config/StaticPath";
import { defaultConfig } from './config';

export class BrowserViewManager {
  private static instance: BrowserViewManager;
  private pool: BrowserViewPool;
  private defaultConfig: ViewConfig;

  private constructor() {
    this.pool = BrowserViewPool.getInstance();
    // 默认使用 default 类型的配置
    this.defaultConfig = defaultConfig['default'];
  }

  static getInstance(): BrowserViewManager {
    if (!BrowserViewManager.instance) {
      BrowserViewManager.instance = new BrowserViewManager();
    }
    return BrowserViewManager.instance;
  }

  private async configureView(view: <PERSON>rowserView, options: ViewConfig['viewOptions'] = {}): Promise<void> {
    const {
      bounds,
      url,
      backgroundColor,
      transparent,
      autoResize,
      webPreferences,
      ...otherOptions
    } = options;

    // 批量设置 BrowserView 属性
    Object.entries(otherOptions).forEach(([key, value]) => {
      const setterName = `set${key.charAt(0).toUpperCase()}${key.slice(1)}`;
      if (typeof view[setterName] === 'function') {
        view[setterName](value);
      }
    });

    // 特殊处理的属性
    bounds && view.setBounds(bounds);
    backgroundColor && view.setBackgroundColor(backgroundColor);
    // 修正 autoResize 的处理
    if (typeof autoResize === 'object') {
      view.setAutoResize(autoResize);
    } else if (autoResize === true) {
      view.setAutoResize({ width: true, height: true });
    }
    url && await view.webContents.loadURL(url);
    webPreferences?.zoomLevel && view.webContents.setZoomLevel(webPreferences.zoomLevel);
  }

  hideBvs = (win, notBv?: any) => {
    const bvs = win.getBrowserViews();
    bvs.forEach((bv) => {
      if (bv?._name !== notBv?._name) {
        win.removeBrowserView(bv);
      }
    });
  };

  async attachToWindow(
    window: any,
    options?: ViewConfig & { type?: String },
    setType?: number
  ): Promise<boolean> {
    const type = options?.type || 'default';
    const view = this.pool.getView(type, true);
    console.log('attachToWindow:+++', view, type);
    if (!view) {
      console.warn(`没有可用的 ${type} 类型视图`);
      return false;
    }

    try {
      if (setType === 2) {
        window.addBrowserView(view);
        setTimeout(() => {
          this.hideBvs(window, view);
        }, 150)
      } else {
        window.setBrowserView(view);
      }

      await this.configureView(view, options?.viewOptions);

      // 使用视图后，自动预加载同类型的新视图
      if (this.defaultConfig.autoReload) {
        setTimeout(async () => {
          const typeConfig = defaultConfig[type];
          if (typeConfig) {
            await this.pool.addView({
              type,
              ...typeConfig,
              viewOptions: {
                ...typeConfig.viewOptions,
                ...options?.viewOptions
              }
            });
            console.log('autoReload, 窗口池内容：', this.pool.getAllPools(true));
          }
        }, 200);
      }

      return true;
    } catch (error) {
      console.error(`附加 ${type} 类型视图失败:`, error);
      console.log('窗口池内容：', this.pool.getAllPools(true));
      return false;
    }
  }

  setConfig(config: Partial<ViewConfig>): void {
    const type = config.type || 'default';
    const typeConfig = defaultConfig[type] || defaultConfig['default'];
    this.defaultConfig = { ...typeConfig, ...config };
  }

  setPoolConfigs(configs: PoolConfig[]): void {
    this.pool.setPoolConfig(configs);
  }

  detachFromWindow(window: any): void {
    let view = window.getBrowserView();
    if (!view) return;
    getSDK().windowManager.close(view?._name);
    if (!view.webContents?.isDestroyed()) {
      window.setBrowserView(null);  // 先移除
      view.webContents?.destroy();       // 释放资源
      view = null;
    }
  }


  async initializePool(): Promise<void> {
    // 初始化所有类型的池
    const configs = Object.values(defaultConfig);
    const poolConfigs = configs.map(config => ({
      type: config.type,
      maxSize: config.maxPoolSize || 1
    }));

    this.setPoolConfigs(poolConfigs);

    // 为每个类型创建视图
    for (const config of configs) {
      const size = config.maxPoolSize || 1;
      for (let i = 0; i < size; i++) {
        await this.pool.addView(config);
      }
    }

    console.log('所有视图池初始化完成');
  }

  getView(type: string = 'default', isShift?): BrowserView | null {
    return this.pool.getView(type, isShift);
  }

  async destroyAllViews(): Promise<void> {
    try {
      // 获取所有类型的视图池
      const pools = this.pool.getAllPools();

      // 遍历每个类型的视图池
      for (const [type, views] of Object.entries(pools)) {
        // 销毁该类型下的所有视图
        for (const view of views) {
          try {
            getSDK().windowManager.close(view?._name);
            if (!view.webContents?.isDestroyed()) {
              view.webContents?.destroy();       // 释放资源
            }
          } catch (error) {
            console.error(`销毁视图失败: ${error}`);
          }
        }

        // 清空该类型的视图池
        views.length = 0;
      }

      // 重新初始化视图池配置
      const configs = Object.values(defaultConfig);
      const poolConfigs = configs.map(config => ({
        type: config.type,
        maxSize: config.maxPoolSize || 1
      }));
      this.setPoolConfigs(poolConfigs);

      console.log('所有视图已销毁，视图池已清空');
    } catch (error) {
      console.error('销毁所有视图时发生错误:', error);
    }
  }
}
