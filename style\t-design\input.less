//input
.t-input {
  border-radius: var(--input-kyy-radius-input, 4px);
  // border-color: var(--input-kyy_color_input_border_default, #D5DBE4) !important;
  &.t-is-error {
    border-color: var(--kyy_color_tag_text_error, #D54941) !important;
  }
}

.t-range-input__inner .t-input:hover {
  background: #fff !important;

}
.t-input:focus{
  box-shadow: 0 !important;
}
.t-input:hover {}

.t-range-input-popup--visible .t-range-input .t-input.t-is-focused {
  background: #fff !important;
}

.t-range-input__inner-right {
  .t-input:hover {
    border: none !important;
  }
}

.t-range-input__inner-left {
  .t-input:hover {
    border: none !important;
  }
}

.t-input:hover {
  border: 1px solid var(--input-kyy-color-input-border-hover, #707EFF) !important;
}

.t-input__inner {
  color: var(--text-kyy-color-text-1, #1A2139);
}

::placeholder {
  color: var(--input-kyy-color-input-text-hover, #ACB3C0) !important;
}

//多行文本
.t-textarea__inner {
  border-radius: var(--textarea-kyy_radius_textarea, 4px);
  border: 1px solid var(--textarea-kyy_color_textarea_border_default, #D5DBE4);
  background: var(--textarea-kyy_color_textarea_bg_default, #FFF);
  &:hover {
    border-color: #707EFF;
  }
}

.t-range-input {
  border-color: #D5DBE4;

}

// input

.t-input.t-is-disabled {
  // color: @kyy_gray_14;
  background: var(--input-kyy-color-input-bg-disabled, #ECEFF5);
  color: var(--input-kyy-color-input-text-disabled, #ACB3C0);
  border: 1px solid var(--input-kyy-color-input-border-disabled, #D5DBE4);
}
.t-input--borderless {
  border-width: 0;
  border: none;
}
.t-input--borderless:hover, .t-input--borderless:focus{
  border-width: 0;
  border: none !important;
}