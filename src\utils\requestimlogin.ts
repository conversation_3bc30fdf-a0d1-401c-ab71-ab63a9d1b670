import axios from 'axios'; // 引入axios
import axiosRetry from 'axios-retry';
import { MessagePlugin, Message } from 'tdesign-vue-next';
import { getUserStore } from '@/store';

import {
	getAccesstoken,
	setAccesstoken,
	setRefreshtoken,
	removeAccesstoken,
	removeRefreshtoken,
	signature,
} from '@/utils/auth';
import router from '@/router';

const Env = import.meta.env.VITE_MANAGE_ENV;

type ApiModule = 'iam-srv' | 'ringkol';

type ApiConfig = {
	[key in ApiModule]: string;
};
function getApiConfig(env: string): ApiConfig {
	if (env === 'PRE') {
		return {
			'iam-srv': 'https://pre.ringkol.com/iam/api',
			ringkol: 'https://pre.ringkol.com/ringkol/api',
		};
	}
	if (env === 'PROD') {
		return {
			'iam-srv': 'https://ringkol.com/iam/api',
			ringkol: 'https://ringkol.com/ringkol/api',
		};
	}

	if (env === 'QA') {
		return {
			'iam-srv': 'https://qa.ringkol.com/iam/api',
			ringkol: 'https://qa.ringkol.com/ringkol/api',
		};
	}
	// 默认开发环境
	return {
		'iam-srv': 'https://dev.ringkol.com/iam/api',
		ringkol: 'https://dev.ringkol.com/ringkol/api',
	};
}
axios.defaults.headers['Content-Type'] = 'application/json';

axios.defaults.withCredentials = false; // 携带cookie
// 创建一个axios实例
export function createApiInstance(baseUrl: string) {
	const instance = axios.create({
		baseURL: baseUrl,
		timeout: 5000,
	});
	// 是否正在请求刷新token接口的标记
	let isRefreshing = false;
	// 请求队列
	let requests = [];
	// 刷新token方法
	function refreshTokens(form) {
		axios.defaults.headers.Authorization = `Bearer ${getAccesstoken()}`;

		// return axios.post('http://172.28.14.149:9877/api/security/oauth/token', form);
		return axios.post(`${baseurl}/v1/passport`, form);
	}
	// 请求结果拦截器
	instance.interceptors.response.use(
		// eslint-disable-next-line consistent-return
		(response) => {
			console.log(response, 'responseresponseresponse');

			// 接下来会在这里进行token过期的逻辑处理
			const { config } = response;
			const code = response.status || 200;
			if (code === 401) {
				// console.log(isRefreshing,'isRefreshingisRefreshing');
				if (!isRefreshing) {
					isRefreshing = true;

					const objs = JSON.parse(window.localStorage.getItem('TokenData'));
					return refreshTokens({
						openid: objs.openid,
						code: window.localStorage.getItem('refresh_token'),
						expire: objs.refreshToken.expire,
					})
						.then((res) => {
							console.log(res, 'imresqqqqqqqqqqssssssseees');
							MessagePlugin.error('登录信息已过期');
							// if (res.data.code === 'A0230') {
							// 	// Message({
							// 	// 	message: '登录信息已过期',
							// 	// 	type: 'error',
							// 	// });
							// 	removeAccesstoken();
							// 	removeRefreshtoken();
							// 	// location.href = 'admin/#/index';
							// 	router.push({ path: '/login' });

							// 	return;
							// }
							const accessToken = res.data.jwt || res.data?.secret;
							const refreshToken = res.data.refreshToken.code;
							window.localStorage.setItem('TokenData', JSON.stringify(res.data));
							setAccesstoken(accessToken);
							setRefreshtoken(refreshToken);
							config.headers.Authorization = `Bearer ${accessToken}`; // 让每个请求携带自定义token 请根据实际情况自行修改
							requests.forEach((cb) => cb(accessToken, refreshToken));
							requests = [];

							return instance(config);
						})
						.catch((err) => {
							console.log(config, 'imw88888888888');

							removeAccesstoken();
							removeRefreshtoken();
							//  location.href = 'admin/#/index';
							router.push({ path: '/login' });
						})
						.finally((e) => {
							isRefreshing = false;
						});
				}
				console.log('refreshelseokens');
				// 正在刷新token，返回一个未执行resolve的promise
				return new Promise((resolve) => {
					// 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
					requests.push((accessToken, refreshToken) => {
						config.headers.accessToken = accessToken;
						config.headers.refreshToken = refreshToken;
						resolve(instance(config));
					});
				});
			}

			// if (code === 'A0200') {
			// 	// Message({
			// 	// 	message: '用户信息错误',
			// 	// 	type: 'error',
			// 	// });
			// 	removeAccesstoken();
			// 	removeRefreshtoken();
			// 	// location.href = 'admin/#/index';
			// 	router.push({ path: '/login' });
			// } else if (code === 500) {
			// 	// Message({
			// 	// 	message: msg,
			// 	// 	type: 'error',
			// 	// });
			// } else if (code === 555) {
			// 	// Message({
			// 	// 	message: '账号已存在,请重新输入',
			// 	// 	type: 'error',
			// 	// });
			// } else if (code !== 200) {
			// 	// return MessagePlugin.error({
			// 	// 	title: msg,
			// 	// });
			// } else {
			return response;
			// }
		},
		(error) => {
			console.log(error, 'errorerrorerrorerror1111');
			if (error.response.data.code === 401) {
				const store = getUserStore();
				window.localStorage.removeItem('main_token');
				window.localStorage.removeItem('user');
				MessagePlugin.error({
					content: '登录状态过期,请重新登录',
					zIndex: 9100000001,
				});
				store.userInfo = { title: null, ID: null, avatar: null, telephone: null };
			}
			let msg = '';
			if (error.code === 'ERR_NETWORK') {
				MessagePlugin.error('当前网络不可用');
				return error;
			}
			switch (error.response.status) {
				case 301:
					msg = '资源已被移除';
					break;
				case 303:
					msg = '重定向';
					break;
				case 304:
					msg = '资源没有被修改';
					break;
				case 400:
					msg = false;
					break;
				// break;

				case 403:
					msg = '访问受限，授权过期';
					break;
				case 404:
					msg = '资源，服务未找到';
					break;
				case 405:
					msg = '不允许的http方法';
					break;
				case 409:
					msg = '资源冲突，或者资源被锁定';
					break;
				case 415:
					msg = '不支持的数据（媒体）类型';
					break;
				case 429:
					msg = '请求过多被限制';
					break;
				case 500:
					msg = '系统内部错误';
					break;
				default:
					msg = response.data.message;
					break;
			}
			if (error.response.data.reason === 'COMMON_ACCOUNT_BANNED') {
				MessagePlugin.error('你的账号处于封禁状态,无法正常使用');
			}
			if (msg) {
				console.log('走这里');

				MessagePlugin.error(msg);
			}

			return error.response;
		},
	);

	instance.interceptors.request.use(
		(config) => {
			// 是否需要设置 token
			Object.assign(config.headers, signature(config));

			console.log(getAccesstoken(), 'getAccess_tokengetAccess_token');

			if (getAccesstoken()) {
				config.headers.Authorization = `Bearer ${getAccesstoken()}`; // 让每个请求携带自定义token 请根据实际情况自行修改
			}
			if (config.url === '/v1/passport/me') {
				// config.headers.Authorization = '';
			}
			return config;
		},
		(error) => {
			Promise.reject(error);
		},
	);
	let flag = 0;
	axiosRetry(instance, {
		retries: 3, // 设置自动发送请求次数
		retryDelay: (retryCount) => {
			return retryCount * 1000; // 重复请求延迟
		},
		shouldResetTimeout: true, //  重置超时时间
		retryCondition: (error) => {
			flag += 1;
			if (flag === 3) {
				// Message({
				// 	message: 'timeout',
				// 	type: 'error',
				// 	duration: 3000,
				// });
				flag = 0;
			}
			// true为打开自动发送请求，false为关闭自动发送请求
			if (error.message.includes('timeout')) {
				return true;
			}
			return false;
		},
	});

	return instance;
}
export const ringkolRequest = createApiInstance(getApiConfig(Env).ringkol);

export default createApiInstance(getApiConfig(Env).ringkol);
