import { IMAGE_MIME_TO_EXTENSION } from '../constants';

/**
 * 将 Blob 对象转换为图片文件
 *
 * @param blob 要转换的 Blob 对象
 * @param filename 转换后文件的名称，不包括扩展名
 * @returns 返回一个表示转换后的 PNG 文件的 File 对象
 */
export const blobToImage = (blob: Blob, filename: string): File => {
  const { type } = blob;
  const ext = IMAGE_MIME_TO_EXTENSION[type] || 'png';
  return new File([blob], `${filename}.${ext}`, { type });
};

/**
 * 从文件名中获取文件扩展名
 *
 * @param filename - 文件名字符串
 * @returns 文件扩展名字符串，如果没有扩展名则返回空字符串
 */
export const getFileExtension = (filename: string): string => {
  if (!filename) return '';
  const parts = filename.split('.');
  return parts.length > 1 ? parts.pop()!.toLowerCase() : '';
};

/**
 * 将文件读取为 Data URL
 *
 * @param file 要读取的文件对象
 * @returns 返回一个包含文件 Data URL 的 Promise 对象
 */
export const readFileAsDataURL = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (e) => reject(e);
    reader.readAsDataURL(file);
  });
};
