<script lang="ts" setup>
import { computed, ref, defineProps, defineExpose } from 'vue';
import { TooltipProps, Tooltip as TTooltip } from 'tdesign-vue-next';

const props = defineProps({
  /** 要显示的文本内容 */
  text: {
    type: String,
    default: '',
  },
  /** tooltip 中要显示的内容，通常用于更详细的信息或辅助文本 */
  content: {
    type: String,
    default: '',
  },
  /** 文本的行数限制，用于控制文本的截断和溢出行为 */
  lineNumber: {
    type: Number,
    default: 1,
  },
  /** tooltip 内容的最大宽度（px） */
  maxWidth: {
    type: Number,
    default: 256,
  },
});

// 响应式状态
const showTooltip = ref(false);
const domRef = ref<HTMLElement | null>(null);

const maxWidthStyle = computed(() => ({ maxWidth: `${props.maxWidth}px` }));

/**
 * 判断元素是否溢出
 * 临时修改 overflow 样式以检测溢出
 * 参考：https://stackoverflow.com/a/143889
 */
const isOverflowing = computed(() => {
  if (!domRef.value) return false;
  const el = domRef.value;

  // 临时修改 overflow 样式以检测溢出
  const curOverflow = el.style.overflow;
  if (!curOverflow || curOverflow === 'visible') el.style.overflow = 'hidden';

  const isOverflowing = el.clientWidth < el.scrollWidth || el.clientHeight < el.scrollHeight;

  // 恢复原始样式
  el.style.overflow = curOverflow;
  return isOverflowing;
});

/**
 * 鼠标进入时检测是否显示 tooltip
 */
const onMouseEnter = () => {
  showTooltip.value = isOverflowing.value;
};

defineExpose({
  /**
   * @public
   * 是否存在文本溢出
   */
  isOverflowing,
});
</script>

<template>
  <t-tooltip
    :content="content"
    :disabled="!showTooltip"
    :overlay-inner-style="maxWidthStyle"
    v-bind="$attrs"
  >
    <template #content>
      <div
        v-if="content"
        class="rk-ellipsis-tooltip__content"
        :style="maxWidthStyle"
      >
        {{ content }}
      </div>
      <div
        v-else
        class="rk-ellipsis-tooltip__content"
        :style="maxWidthStyle"
      >
        <slot>
          {{ text }}
        </slot>
      </div>
    </template>

    <div
      ref="domRef"
      class="rk-ellipsis-tooltip"
      @mouseenter="onMouseEnter"
    >
      <slot>{{ text }}</slot>
    </div>
  </t-tooltip>
</template>

<style lang="less" scoped>
@import '../../style/mixins.less';

.rk-ellipsis-tooltip {
  .multi-ellipsis(v-bind('lineNumber'));
}
</style>
