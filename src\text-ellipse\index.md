# TextEllipsis

对长文本进行省略，支持展开/收起。

功能特性：

- 支持单行/多行文本省略显示
- 支持展开/收起功能
- 支持省略位置选择（开始/中间/结尾）
- 自适应窗口大小变化重新计算
- 动态计算最佳省略位置

## 基础用法

<code src="./demos/Basic.vue"></code>

## 自定义内容

<code src="./demos/Custom.vue"></code>

## Props

<API id="RkTextEllipsis" type="props"></API>

## Events

<API id="RkTextEllipsis" type="events"></API>

## Methods

通过 ref 可以获取到 TextEllipsis 实例并调用实例方法：

<API id="RkTextEllipsis" type="methods"></API>

:::warning{title=注意}

1. 组件需要在有明确宽度的容器中使用才能正确计算截断位置
2. 文本内容发生变化时会自动重新计算截断位置
3. 容器大小变化时会自动响应并重新计算（需要浏览器支持 `ResizeObserver`）
4. 建议为容器设置合适的 `line-height` 以获得更好的显示效果

:::
