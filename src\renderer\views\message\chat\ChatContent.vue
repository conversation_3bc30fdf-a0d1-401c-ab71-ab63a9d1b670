<template>
  <div style="flex: 1; overflow: hidden; position:relative; display: flex; flex-direction: column;z-index: 0;">
    <cakeRain v-if="msgStore.showCakeRain"></cakeRain>
    <orgAuthDialog v-model:visible="msgStore.orgAuthDialogVisible" v-if="msgStore.orgAuthDialogVisible" from="message"
      showType="detail" :region="msgStore.orgAuthDetail.region" :teamId="msgStore.orgAuthDetail.teamId"
      :orgType="msgStore.orgAuthDetail.orgType"></orgAuthDialog>
    <groupNoticeInvite/>
    <AntiFraud v-if="msgStore.chatingSession.relation === '9'" />
    <div ref="chatScroll" @click="contentClick" @scroll="onScrollEvt" class="chat-content" :data-tool="toolStore.toolVisible"
      :data-mt="!groupInviteStore.topGroupNotice" :data-mb="!msgStore.referMsg" :key="chatingSession?.localSessionId">
      <div ref="chatBoxScroll" class="chat-content-box">
        <div class="center-tips" v-if="!imHasHistoryMore">无更多历史记录</div>
        <template v-for=" msgWrapper in chatingMessages" :key="msgWrapper.msg.messageUId">

          <div v-if="msgWrapper.msg.contentExtra" class="msg-row" :key="msgWrapper.msg.messageUId"
            :style="{ 'padding-top': showAvatar(msgWrapper) && chatingSession.conversationType === 3 ? '36px' : '24px' }"
            :data-selected="msgStore.highlightedSearchedId === msgWrapper.msg.messageUId"
            :data-center="isCenterMsg(msgWrapper)" :data-msg-time="msgWrapper.msg.sentTime"
            :data-msg-align="getMsgAlign(msgWrapper)" :id="'msg-' + msgWrapper.msg.messageUId"
            @click.capture="onClickRow($event, msgWrapper)">
            <!-- && msgWrapper.msg?.contentExtra?.contentType != 'APP_MEETING_MSG' -->
            <div v-if="actionStore.isSelecting && !isCenterMsg(msgWrapper)">
              <t-checkbox v-show="msgWrapper.canSelect" v-model="msgWrapper.selected" size="small"></t-checkbox>
            </div>
            <div style="flex:1;display: block;">
              <div class="center-time" v-if="isShowTimestamp(msgWrapper.msg)">{{
                getSampleTimeText(msgWrapper.msg.sentTime, currentTime) }}</div>
              <div class="content-row" :class="showMsgContentCenter(msgWrapper.msg) ? 'msg-cont-center' : ''"
                :data-sender="isSender(msgWrapper.msg)">
                <template v-if="showAvatar(msgWrapper)">
                  <chat-avatar size="medium" v-if="isAssistantSession" :conversation="chatingSession"
                    :is-file-helper="isFileHelper" />
                  <chat-avatar size="medium" v-else :message="msgWrapper.msg"
                    @click="onClickMsgAvatar(msgWrapper.msg)" />
                </template>

                <div class="message-content" :data-sender="isSender(msgWrapper.msg)">
                  <div v-if="showAvatar(msgWrapper)" :class="['sender-info', { self: isSender(msgWrapper.msg) }]">
                    <template v-if="chatingSession.conversationType === 3">
                      <div class="name__text"> {{ getMsgSenderName(msgWrapper.msg) }}</div>
                      <div v-if="getIsFollow(msgWrapper.msg)" class="follow__text"> {{ t("im.msg.specialAttention") }}
                      </div>
                      <span v-if="msgStore.getSenderLabel(msgWrapper.msg)" class="label_tag">
                        {{ msgStore.getSenderLabelName(msgWrapper.msg) }}
                      </span>
                    </template>

                    <div class="sent-time__text">{{ moment(msgWrapper.msg.sentTime).format('HH:mm') }}</div>
                  </div>
                  <message-content class="msg-chat-text" :onShowPostDetailDialogId="onShowPostDetailDialogId"
                    :message="msgWrapper" @contextmenu="onRightClick($event, msgWrapper)"
                    :isMySelf="isSender(msgWrapper.msg)" @click="actionStore.onClickMessage(msgWrapper.msg)" />
                  <div class="send-status" v-if="!msgWrapper.merged?.length && showMsgSendStatus(msgWrapper.msg)">
                    <t-loading size="small" style="overflow: hidden;"></t-loading>
                  </div>
                  <div class="send-status" v-if="msgWrapper.msg.sentStatus === 20 && !msgWrapper.isMerged">
                    <t-tooltip placement="top" :content="t('im.public.resend')">
                      <svg class="svg-size20 send-error"
                        @click="sendStore.reSendMessaage(msgWrapper.msg, chatingSession.conversationID)">
                        <use href="#iconattention"></use>
                      </svg>
                    </t-tooltip>
                    <span class="msg-tips" v-if="msgWrapper.msg?.contentExtra?.errorInfo">{{
                      errInfo[msgWrapper.msg?.contentExtra?.errorInfo]}}</span>
                  </div>
                  <div class="msg-actions-root"
                    v-if="!isAssistantSession && !actionStore.isSelecting && showAvatar(msgWrapper) && msgWrapper.msg.sentStatus >= 30">
                    <div class="msg-actions">
                      <t-tooltip placement="top" :content="t('im.public.reply')">
                        <IconBtn name="reply" @click="msgStore.onReplyEditing(msgWrapper.msg)" />
                      </t-tooltip>
                      <div class="action-sep"></div>
                      <IconBtn name="iconmore" @click="onRightClick($event, msgWrapper)" />
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="isSender(msgWrapper.msg) && showReadStatus(msgStore.chatingSession, msgWrapper)"
                class="read-status">
                <span v-show='msgWrapper.msg.sentStatus === 30' @click="showMsgReadInfo(msgWrapper.msg)"
                  :class="{ read: getReadText(msgWrapper.msg).isRead }">
                  {{ getReadText(msgWrapper.msg).text }}
                </span>
              </div>
            </div>
          </div>
        </template>
        <div v-if="isConsultInfo" class="consultInfo">
          <div class="info">
            <img class="img" :src="consultInfo.images[0]" />
            <div class="desc">
              <div class="name">{{ consultInfo.title }} </div>
              <div class="content" v-html="consultInfo.content"></div>
            </div>
          </div>
          <div class="send" @click="consultInfoSend()">{{ t("im.msg.send") }}</div>
          <div class="close" @click="consultInfoClose()">
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
              <path d="M1.00195 1L8.99859 8.99936" stroke="#828DA5" stroke-width="1.4" stroke-linecap="round" />
              <path d="M8.99463 1L0.997993 8.99936" stroke="#828DA5" stroke-width="1.4" stroke-linecap="round" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <div v-if="msgStore.unredInfo.count > 0 || msgStore.unredInfo.mention > 0" class="chat-msg-go">
      <div v-if="msgStore.unredInfo.mention > 0" class="go-item"
        @click="msgStore.loadMessageMoreToMentionMe('mention')">
        {{ msgStore.chatingSession.conversationType === 3 && contactStore.isFollow(chatingSession.targetCardId) ?
          t("im.msg.specialAttention") : t("im.msg.atYou") }}
      </div>
      <div v-if="msgStore.unredInfo.count" class="go-item" @click="msgStore.loadMessageMoreToMentionMe('unread')">
        {{ msgStore.chatingSession.conversationType === 3 && contactStore.isFollow(chatingSession.targetCardId) ?
          t("im.msg.specialAttention") : `${msgStore.unredInfo.count}${t("im.msg.newMsg")}` }}
      </div>
    </div>

    <edit-refer-message v-if="msgStore.referMsg" :message="msgStore.referMsg" @close="msgStore.onCleanReferMsg" />
  </div>
  <context-menu :target="contextTarget" :placement="placement" :menus="contextMenus" @selected="onMenuSelected" />
  <Remind />
  <move-file v-if="showNewMoveFile" ref="newMoveFile" :left-data="fistFileData" :group-list="groupList"
    :title-flag="moveFileTitleFlag" :act-group="actGroup" @get-file-list-main-add-file-falg="getFileListMainAddFileFalg"
    @set-move-act-item="setMoveActItem" @get-move-left-list="getMoveLeftList"
    @im-to-save-cloud-disk="imToSaveCloudDisk" />



  <ChatMsgReadInfo v-if="readMsgVisible" :msg="readMsgItem" @close="hideMsgReadInfo" />

  <t-dialog width="672px" v-if="groupInviteStore.visible" v-model:visible="groupInviteStore.visible"
    :header='t("im.msg.inviteApply")' :footer="false" class="group-invite-dialog">
    <template #body>
      <div style="height:420px;overflow: auto;">
        <div v-for="item in groupInviteStore.inviteList">
          <div v-for="member in item.members" class="invite-item">
            <ChatAvatarComponent :src="member.attachments.avatar" :alt="member.attachments.staffName" />
            <div class="invite-info">
              <div>
                {{ member.attachments.staffName }}
                <span v-if="member.attachments?.departmentName" class="team">
                  @{{ member.attachments?.departmentName }}
                </span>
              </div>
              <div>
                <span class="invitor">{{ `${getGroupMemberByCard(item.gid, item.applicant)}` }}</span>
                {{ t("im.msg.inviteGroup") }}
              </div>
              <div class="remark">{{ t("im.msg.remark") }}:{{ item.content }}</div>
            </div>
            <div class="btns">
              <t-button variant="outline" theme="danger" @click="groupInviteStore.rejectInvite(item)">{{
                t("im.msg.refuse") }}</t-button>
              <t-button @click="groupInviteStore.approveInvite(item)">{{ t("im.msg.pass") }}</t-button>
            </div>
          </div>
        </div>
      </div>
    </template>
  </t-dialog>
  <PostDetail v-if="!!showPostDetailDialogId" :id="showPostDetailDialogId" v-model="showPostDetailDialogId"
    class="mt-48" from-outer top="68px" />
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick, shallowRef, onActivated, onDeactivated, onUnmounted, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash'
import { useI18n } from 'vue-i18n';
import AntiFraud from '@renderer/views/message/components/AntiFraud.vue';
import { msgEmit } from "@renderer/views/message/service/msgEmit";
import { useMessageStore } from '../service/store';
import { useChatActionStore } from '../service/actionStore';
import { useUploadStore } from '../service/upload';
import useChatSendStore from '../service/sendStore';
import { ContextAction, getMsgContextMenus } from '../service/contextMenuUtils';

import ChatAvatar from './ChatAvatar.vue';
import ChatAvatarComponent from '../components/ChatAvatar.vue';
import MessageContent from './MessageContent.vue';
import ChatMsgReadInfo from './ChatMsgReadInfo.vue';
import IconBtn from '@/components/IconBtn.vue';
import orgAuthDialog from "@renderer/components/orgAuth/orgAuth.vue";
import PostDetail from "@/views/square/components/post/PostDetail.vue";//广场转发

import EditReferMessage from './EditReferMessage.vue'
import MsgWrapper from '../service/message';
import { isCenterMsg, showReadStatus, getSampleTimeText, getMsgSenderName, getIsSender, getMsgSender } from '../service/msgUtils';

import ContextMenu from '../components/ContextMenu.vue';
import Remind from '@/views/zhixing/components/Remind.vue'
import MoveFile from '@renderer/views/clouddisk/clouddiskhome/components/MoveFile.vue';
import { DiskList, DiskFileList, AddDiskFile } from "@renderer/api/cloud";

import { MessagePlugin } from 'tdesign-vue-next';
import { useContactsStore } from '@renderer/store/modules/contacts';
import LynkerSDK from "@renderer/_jssdk";
import { useGroupInviteStore } from '../service/groupInvite';
import moment from 'moment';
import { MsgShareType, sendApplicationMsg } from "@/utils/share";
import groupNoticeInvite from '@renderer/views/message/components/groupNoticeInvite.vue';
import cakeRain from '../components/cakeRain.vue';
import { useImToolStore } from '../tool/service/tool';
const showPostDetailDialogId = ref(undefined);
const { ipcRenderer } = LynkerSDK;

const errInfo = {
  client_blacklist: '对方隐私设置，无法发送消息',
  uploadError: '上传失败'
};
const contactStore = useContactsStore();
const groupInviteStore = useGroupInviteStore();

const placement = ref('left');
const chatScroll = ref(null);
const chatBoxScroll = ref(null);
let resizeObserver = null; // 监听容器高度变化
let boxHeight = 0;// 记录消息容器高度。
let boxWidth = 0;// 记录消息容器宽度。
const contextTarget = shallowRef<{ ele: HTMLElement }>(null);
const contextMenus = shallowRef([]);

const { t } = useI18n();
const newMoveFile = ref(null);
const actGroup = ref({});
const moveFileTitleFlag = ref(t('clouddisk.selectSaveDirectory'));
const groupList = ref([]);
const fistFileData = ref([]);
const contextMsgTemp = ref<MessageToSave>(); // 暂存右键数据
const isConsultInfo = ref(false);
const consultInfo = ref({});

// 群消息，未读用户列表展示
const readMsgItem = ref<MessageToSave>();
const readMsgVisible = ref(false);

const msgStore = useMessageStore();
const sendStore = useChatSendStore();
const actionStore = useChatActionStore();
const toolStore = useImToolStore();

const { chatingMessages, chatingSession, isAssistantSession, consultInfoPop, isFirstPageForChatingMessages, currentTime, imHasHistoryMore, shouldScroll } = storeToRefs(msgStore);

ipcRenderer.removeAllListeners('add-video-remind');
ipcRenderer.on('add-video-remind', (event, value) => {
  let props = {
    title: value?.title || '',
    content: value?.content || null
  }
  actionStore.onAddRemind(props)
})

const openMonitor = () => {
  // 创建 ResizeObserver 实例
  resizeObserver = new ResizeObserver(entries => {
    // entries 是一个 ResizeObserverEntry 对象数组，包含目标元素的信息
    handleResize(entries);
  });

  // 开始监听目标元素的大小变化
  chatBoxScroll.value && resizeObserver.observe(chatBoxScroll.value);
};

const handleResize = debounce((entries: any) => {
  // 处理大小变化的回调函数
  console.log('====>处理大小变化的回调函数', shouldScroll.value, entries, chatScroll.value, msgStore.highlightedSearchedId);
  /**
   * fix: https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001045972
   */
  if (!shouldScroll.value) return;
  for (const entry of entries) {
    const { width, height } = entry.contentRect;
    console.log('元素大小已变化', width, height, chatScroll.value.scrollHeight, boxHeight, boxWidth);
    // 只是高度变化，宽度变化引起的高度变化不处理
    if (boxHeight !== height && (boxWidth === 0 || boxWidth === width)) {
      chatScroll.value.scrollTop = height;
      boxHeight = height;
      boxWidth = width;
      console.log('====>scrollTop', chatScroll.value.scrollTop);
    }
  }

}, 100);

// 当chatingSession.value没变化内容高度变化
// 或者当高度变化 没有增加消息, 并且条数不是减少(删除)，不是宽度变化引起
watch(() => chatingMessages.value, async (newVal, oldVal) => {
  // shouldScroll = (!oldVal?.[0]?.messageUId || newVal?.[0]?.messageUId === oldVal[0].messageUId) && (newVal.length >= oldVal.length);
  // console.log('====>shouldScroll', shouldScroll, newVal.length, oldVal.length);
  nextTick(() => {
    // 当第一次拉取的时候内容置底
    if (isFirstPageForChatingMessages.value) {
      distanceFromBottom.value = 0;
      boxWidth = 0;
      // console.log('====>isFirstPageForChatingMessages', shouldScroll, isFirstPageForChatingMessages.value);
      chatScroll.value.scrollTop = chatScroll.value.scrollHeight;
      if (resizeObserver) {
        resizeObserver.disconnect();
        resizeObserver = null;
      }
      openMonitor();
      isFirstPageForChatingMessages.value = false;
    }

  });
});

// 鼠标拖选复制只能复制一条消息，不能跨区拖选
let isMouseDown = false;
const mousedownSelection = (e) => {
  const target = e.target.closest('.msg-chat-text');
  if (target) {
    isMouseDown = true;
    const allElements = document.querySelectorAll('.msg-chat-text');
    allElements.forEach(el => {
      el !== target && el.classList.add('no-select');
    });
  }
}
const mouseupSelection = (e) => {
  if (isMouseDown) {
    isMouseDown = false;
    setTimeout(() => {
      document.querySelectorAll('.msg-chat-text.no-select').forEach(el => {
        el.classList.remove('no-select');
      })
    }, 100);
  }
}
// 新增双击处理逻辑
const handleDoubleClick = (e) => {
  const target = e.target.closest('.msg-chat-text');
  if (target) {
    // 清除所有选区
    window.getSelection()?.removeAllRanges();
    // 创建新选区仅包含当前消息
    const range = document.createRange();
    range.selectNodeContents(target);
    window.getSelection()?.addRange(range);
  }
  e.preventDefault();
};

// 修改后的点击处理逻辑
const handleClick = (e: MouseEvent) => {
  if (e.detail >= 3) { // 通过点击次数判断三击
    preventMultiClickSelection(e);
  }
};
const preventMultiClickSelection = (e) => {
  window.getSelection()?.removeAllRanges();
  e.preventDefault();
};
const contentClick = (e) => {
  // 判断是否点击消息内容区域
  const isMsgClick = e.target.closest('.msg-chat-text');
  if (!isMsgClick) {
    window.getSelection()?.removeAllRanges();
  }
}
onMounted(() => {
  msgEmit.on("chatScrollDown", () => {
    chatScroll.value.scrollTop = chatScroll.value.scrollHeight;
  });
  document.addEventListener('dblclick', handleDoubleClick);
  document.addEventListener('mousedown', mousedownSelection);
  document.addEventListener('mouseup', mouseupSelection);
  document.addEventListener('click', handleClick); // 改为监听 click 事件


});
onUnmounted(() => {
  // 关闭ResizeObserver监听器
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  msgEmit.off("chatScrollDown");
  document.removeEventListener('dblclick', handleDoubleClick);
  document.removeEventListener('mousedown', mousedownSelection);
  document.removeEventListener('mouseup', mouseupSelection);
  document.removeEventListener('click', handleClick);

});
// 是否文件助手
const isFileHelper = computed(() => {
  return chatingSession.value?.localSessionId === 'assistant8app8file8helper';
})
const showConsultInfo = (newVal) => {
  // 这里为了解决首次数据延迟问题，暂时用setTimeout延迟接收
  setTimeout(() => {
    const _info = newVal.value[chatingSession.value?.localSessionId]
    if (chatingSession.value.relation !== "CONSULT" || !_info) {
      isConsultInfo.value = false;
    } else {
      consultInfo.value = _info;
      isConsultInfo.value = true;
    }
  }, 10);
}

watch(consultInfoPop.value, async (newVal, oldVal) => {
  showConsultInfo(newVal);
}, { immediate: true, deep: true });

watch(() => chatingSession.value?.localSessionId, (val, old) => {
  if (val) {
    showConsultInfo(consultInfoPop.value);
  }
})
// 发送咨询信息
const consultInfoSend = () => {
  try {
    sendApplicationMsg(
      MsgShareType.service_content_share,
      consultInfo.value,
      [{
        conversationType: chatingSession.value.conversationType,
        // 选中人的openId
        openId: chatingSession.value.targetId,
        // 选中人的身份卡
        cardId: chatingSession.value.targetCardId,
        attachment: {
          // 包含我和对方的身份卡
          member: [{
            cardId: chatingSession.value.targetCardId,
          }, {
            cardId: chatingSession.value.myCardId,
          }]
        }
      }]
    );
    consultInfoClose();
  } catch (e) {
    console.log('consultInfoSend', e);
  }
}
// 关闭咨询信息
const consultInfoClose = () => {
  consultInfo.value = '';
  msgStore.delConsultInfo(chatingSession.value?.localSessionId);
}

// 存入云盘
const imToSaveCloudDisk = (obj) => {
  const objs = {
    disk_id: actGroup.value.id,
    title: '',
    is_folder: 0,
    size: null,
    url: '',
    type: "folder",
    pid: obj.pid
  };
  const data = contextMsgTemp.value?.contentExtra?.data;
  let types = data?.type;
  if (contextMsgTemp.value?.contentExtra?.contentType === 'file') {
    objs.url = data?.fileUrl;
    objs.title = data?.fileName;
    objs.size = data?.size / 1024;
    objs.type = data?.type;

  } else if (contextMsgTemp.value?.contentExtra?.contentType === 'image') {
    objs.url = data?.imgUrl;
    objs.title = data?.name || `${new Date().getTime()}.${data?.type}`;
    objs.size = data?.size / 1024;
    objs.type = data?.type;

  } else if (contextMsgTemp.value?.contentExtra?.contentType === 'video') {
    // 数据中没有type和name，设置默认名称格式
    let temp1 = data?.videoUrl?.split('/');
    let temp2 = temp1[temp1.length - 1]?.split('.');
    let type = temp2[temp2.length - 1] ?? 'mp4';
    objs.url = data?.videoUrl;
    objs.title = data?.videoName || `${new Date().getTime()}.${type}`;
    objs.size = data?.size / 1024;
    objs.type = data?.type;
  } else {
    objs.url = data?.fileUrl;
    objs.title = data?.fileName;
    objs.size = data?.size / 1024;
    objs.type = data?.type;
  }

  if (
    types === "png" ||
    types === "svg" ||
    types === "jpg" ||
    types === "gif"
  ) {
    objs.type = "image";
  } else if (!types) {
    objs.type = "other";
  } else {
    objs.type = types;
  }
  AddDiskFile(objs).then(res => {
    if (res?.status === 200) {
      MessagePlugin.success(t("im.msg.success"));

    } else {
      MessagePlugin.error(res.data.message);

    }
  }).catch((err) => {
    console.log(err, '这里呢啊啊啊errerr')
    MessagePlugin.error(err.response.data.message)
  });
}

const setMoveActItem = (item) => {
  actGroup.value = item;
};

const getMoveLeftList = (id) => {
  DiskFileList(id).then((res) => {
    fistFileData.value = res.data.data.list;
  });
};

const getFileListMainAddFileFalg = () => {
  DiskFileList(actGroup.value.id).then((res) => {
    fistFileData.value = res.data.data.list;
  });
};
const showNewMoveFile = ref(false);
const openCloudDisk = async () => {
  contextMsgTemp.value = actionStore.contextMsg
  getGroupList();
  showNewMoveFile.value = true;
  setTimeout(() => {
    newMoveFile.value.openWin();
  }, 100);
}

const getGroupList = async () => {
  const res = await DiskList();
  groupList.value = res.data.data.list.map((ele) => ({
    id: ele.id,
    teamId: ele.team,
    region: ele.type === 0 ? window.localStorage.getItem("region") : ele.region,
    permission: ele.permission ?? 0,
    lock: ele.lock,

    name: ele.type === 0 ? t("clouddisk.personaLisk") : ele.team.fullName,
    icon: ele.type === 0 ? t("clouddisk.personaLisk") : ele.team.logo
  }));
  actGroup.value = groupList.value?.[0]
  getFileListMainAddFileFalg();
}

const uploadStore = useUploadStore();
const onRightClick = (evt, msgWrapper: MsgWrapper) => {
  console.log("[context]", evt, msgWrapper);
  actionStore.selectedText = window.getSelection().toString() || '';
  try {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const fragment = range.cloneContents();
      const div = document.createElement('div');
      div.appendChild(fragment);
      actionStore.selectedHtml = div.innerHTML;
    }
  } catch (e) {
    console.log('====>dgz error', e);
  }
  const { msg, merged } = msgWrapper;
  if ((!isFileHelper.value && isAssistantSession.value) || actionStore.isSelecting || merged?.length || isCenterMsg(msgWrapper)) {
    return;
  } else if (msg.sentStatus === 10) {
    setTimeout(() => {
      if (uploadStore.uploadings.get(msg.messageUId)) {
        return;
      } else {
        actionStore.onShowContext(msg);
        placement.value = isSender(msg) ? 'left' : 'right';
        contextTarget.value = { ele: evt.target };
        contextMenus.value = getMsgContextMenus(msg, msgStore.chatingSession);
      }
    }, 500);
  } else {
    actionStore.onShowContext(msg);
    placement.value = isSender(msg) ? 'left' : 'right';
    contextTarget.value = { ele: evt.target };
    contextMenus.value = getMsgContextMenus(msg, msgStore.chatingSession);
  }

}

const isSender = (msg: MessageToSave) => {
  if ((!isAssistantSession.value || isFileHelper.value) && msg.contentExtra?.contentType !== 'server_message_middle') {
    return getIsSender(msg, chatingSession.value);
  }
  return false;
};

const getReadText = (msg: MessageToSave) => {
  if (msg.conversationType === 1) {
    let result
    if (msg.hasOwnProperty('isRead')) {
      result = {
        text: msg.isRead ? t("im.msg.read") : t("im.msg.unread"),
        isRead: msg.isRead
      }
    } else {
      result = {
        text: t("im.msg.read"),
        isRead: true
      }
    }

    return result

  } else {
    let unreadCount = 0;

    if (!msg.receipts) {
      // 本地没绘会话历史，远端历史记录不会存receipts字段，默认全部已读
      // 统计非本人的群成员
      // const openId = getOpenid();
      // const groupMembers = msgStore.allMembers.get(msg.targetId);
      // groupMembers?.forEach(member => {
      //   if(member.openId !== openId && msg.sentTime > member.joined ) {
      //     unreadCount++;
      //   }
      // });
    } else {
      unreadCount = msg.receipts.unReadCount || 0
    }
    const result = {
      text: unreadCount > 0 ? `${unreadCount}${t("im.msg.unread1")}` : t("im.msg.readAll"),
      isRead: unreadCount == 0
    }
    return result
  }
}

const showMsgReadInfo = (msg: MessageToSave) => {
  if (msg.conversationType === 3) {
    readMsgItem.value = msg;
    readMsgVisible.value = true;
  }
}

const hideMsgReadInfo = () => {
  readMsgVisible.value = false;
  readMsgItem.value = null;
}

watch(() => msgStore.highlightedSearchedId, (val) => {
  if (val !== '-1') {
    setTimeout(() => {

      // 获取子元素和父元素的边界信息
      const parentRect = chatBoxScroll.value.getBoundingClientRect()
      const el = document.getElementById(`msg-${val}`);
      // 【【PC】【生产】缩放另可窗口后 点击“x条新消息”会导致窗口展示问题（展示不全）https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001050654
      const params = ((parentRect?.bottom - el.getBoundingClientRect()?.bottom) < 200) ? { behavior: "smooth" } : { behavior: "smooth", block: 'center' }
      el?.scrollIntoView(params);
    }, 150);
  }
}, { immediate: true })


const debounceScroll = async (evt) => {
  if (evt.target?.scrollTop < 100) {
    const beforeHeight = evt.target.scrollHeight;
    if (actionStore.isSelecting) { msgStore.getselectedIDList() }
    await msgStore.loadOpenIMMsg(msgStore.chatingSession, false);
    await nextTick();
    if (actionStore.isSelecting) { msgStore.setselectedIDList() }
    const afterHeight = evt.target.scrollHeight;
    if (afterHeight > beforeHeight) {
      evt.target.scrollTop = afterHeight - beforeHeight;
    }
  }

}
const saveScrollPosition = (scrollPosition, distanceFromBottom) => {
  if (chatScroll.value) {
    msgStore.setScrollHeight(distanceFromBottom, scrollPosition);
  }
};
const restoreScrollPosition = () => {
  console.log('====>restoreScrollPosition');
  if (chatScroll.value) {
    chatScroll.value.scrollTop = msgStore.scrollPosition;
  }
};

const distanceFromBottom = ref(0)
const onScrollEvt = debounce((event) => {
  debounceScroll(event);
  setTimeout(() => {
    // 记录滑动的位置，从其他BV/BW回来要重置
    const { scrollTop, clientHeight, scrollHeight } = event.target;
    distanceFromBottom.value = scrollHeight - (scrollTop + clientHeight);
    console.log('====>distanceFromBottom', distanceFromBottom.value);
    saveScrollPosition(scrollTop, distanceFromBottom.value);
  }, 500);

  // 计算距离底部的距离
  if (contextTarget.value) {
    contextTarget.value = null;
  }
  // if (handleScroll) {
  // }
}, 200);

const onMenuSelected = (menu) => {
  const action = menu.id as ContextAction
  switch (action) {
    case ContextAction.Recall: actionStore.onRecallMessage(); break;
    case ContextAction.Copy: actionStore.onCopyMessage(); break;
    case ContextAction.Reply: msgStore.onReplyEditing(actionStore.contextMsg); break;
    case ContextAction.Select: actionStore.onStartSelecting(); break;
    case ContextAction.Forward: actionStore.onContextForward(); break;
    case ContextAction.VoiceTextShow: actionStore.onVoiceTextShow(); break;
    case ContextAction.VoiceTextHide: actionStore.onVoiceTextHide(); break;
    case ContextAction.AddRemind: actionStore.onAddRemind(); break;
    case ContextAction.AddLater: actionStore.onAddLaterEvent(); break;
    case ContextAction.Delete: actionStore.onDeleteMessage(); break;
    case ContextAction.Download: actionStore.onDownloadMessageFile(); break;
    case ContextAction.CloudDisk: openCloudDisk(); break;
    case ContextAction.SaveCommon: actionStore.onSaveCommon(); break;
  }

  actionStore.onHideContext();
  contextTarget.value = null;
};
const onClickRow = (evt: MouseEvent, msg: MsgWrapper) => {
  if (actionStore.isSelecting && !isCenterMsg(msg)) {
    evt.stopPropagation();
    if (msg.canSelect) {
      msg.selected = !msg.selected;
    }
  }
  msgStore.cancelHighlightedMsg();
};

const onClickMsgAvatar = (msg: MessageToSave) => {
  let senderInfo: ConversationMemberToSave = null;
  let myCardId = chatingSession.value?.myCardId;
  let comment = '';
  if (msg.conversationType === 1) {
    const members = msgStore.allMembers.get(msg.localSessionId)
    senderInfo = members?.get(msg.contentExtra?.senderId);
  } else if (msg.conversationType === 3) {
    const group = msgStore.allGroups.find(item => item.group === msg.targetId);
    // 助手群另外处理
    if ([0, 1, 2, 3, 10, 15, 20, 22].includes(group.type)) {
      const groupMembers = msgStore.allMembers.get(msg.targetId);
      senderInfo = groupMembers?.get(msg.contentExtra?.senderId);
      comment = senderInfo?.nickname || senderInfo?.staffName || msg.senderNickname;
    } else {
      // const info = msgStore.allMembers.get(msg.targetId)?.get(msg.contentExtra?.senderId);
    }
  }
  // if (senderInfo) {
  const cardId = senderInfo?.cardId || msg.contentExtra?.senderId;
  ipcRenderer.invoke("identity-card", {
    cardId,
    myId: myCardId,
    comment
  });
  // }
}

const showAvatar = (msg: MsgWrapper) => {
  if (msg.isMerged) {
    return false;
  }
  if (showMsgContentCenter(msg.msg)) {
    return false;
  }
  return !isCenterMsg(msg);
}
// 需要居中展示的消息
const showMsgContentCenter = (msg: MessageToSave) => {
  const { type, scene } = msg.contentExtra;
  if (['APP_SECRETARY'].includes(type) && [30].includes(scene)) {
    return true;
  }

  return false;
};

const getMsgAlign = (msg: MsgWrapper) => {
  if (msg.isMerged) {
    return '';
  }

  if (isCenterMsg(msg)) {
    return 'center';
  }

  return '';
}

const showMsgSendStatus = (msg: MessageToSave) => {
  if (!isSender(msg)) {
    return false;
  }

  if (msg.sentStatus !== 10) {
    return false;
  }

  // TODO: 弱网环境下，发送中的消息，均显示发送状态
  // const isWeakNet = false;
  // if(isWeakNet ) {
  //   return true;
  // }

  const msgType = msg.messageType;
  if (msgType === 'text' || msgType === 114) {
    return true;
  }

  const contentType = msg.contentExtra?.contentType;
  return ['image', 'video'].includes(contentType);

};

const showInviteApplys = () => {
  if (groupInviteStore.inviteList.length) {
    groupInviteStore.visible = true;
  }
}

const getGroupMemberByCard = (gid: string, card: string) => {
  const member = msgStore.allMembers?.get(gid)?.get(card);
  return member?.nickname || member?.staffName || '';
}

// 显示聊天时间,名称相关
let lastDisplayedTimestamp = 0;

/**
 * 判断是否显示时间戳
 * @param {MessageToSave} msg - 要判断的消息对象
 * @returns {boolean} - 是否显示时间戳
 */
const isShowTimestamp = (msg: MessageToSave): boolean => {
  const timestamp = moment(msg.sentTime).startOf('minute').valueOf();
  // 10分钟内不重复显示
  if (Math.abs(timestamp - lastDisplayedTimestamp) < 10 * 60 * 1000) {
    return false;
  }

  lastDisplayedTimestamp = timestamp;
  return true;
}

/**
 * 判断是否特别关注
 * @param msg - 要判断的消息对象
 */
const getIsFollow = (msg: MessageToSave) => {
  if (!isSender(msg) && contactStore.isFollow(msg?.contentExtra?.senderId)) {
    return true;
  }
  return false;
}

const onShowPostDetailDialogId = (id: string) => {
  showPostDetailDialogId.value = id;
};

// 回到消息重置到当时离开的位置
onActivated(restoreScrollPosition);
</script>

<style lang="less" scoped>
::-webkit-scrollbar {
  width: 0;
}

.chat-content {
  background-color: #F5F8FE;
  overflow: scroll;
  flex: 1;
  padding-bottom: 10px;
}

.chat-content[data-mt="false"]::before {
  content: '';
  display: block;
  padding-bottom: 58px;
}

.chat-content[data-mb="false"]::after {
  content: '';
  display: block;
  padding-top: 48px;
}

.chat-content::before {
  content: '';
  display: block;
  height: 16px;
  background-color: #F5F8FE;
}

.chat-content-mt .msg-row:first-child {
  margin-top: 50px;
}

.msg-row {
  width: 100%;
  padding: 0 16px 0px 16px;
  // margin: 22px 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  &[data-selected="true"] {
    transform: all 0.3s;
    background-color: @kyy_gray_2;
  }

  &[data-center="true"] {
    justify-content: center;
    padding: 12px 16px 12px 16px !important;
    -webkit-user-select: none;
    /*webkit浏览器*/
    user-select: none;

    .center-time {
      padding-bottom: 12px;
      margin-top: 0;
    }
  }

  .center-time {
    display: flex;
    justify-content: center;
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 24px;
    color: @kyy_font_3;
    -webkit-user-select: none;
    /*webkit浏览器*/
    user-select: none;
  }

  .msg-tips {
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: red;
  }
}

.content-row {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;

  &[data-sender="true"] {
    flex-direction: row-reverse;
  }

  .message-content {
    flex: 1;
    display: flex;
    flex-direction: inherit;
    gap: 4px;

    .no-select {
      -webkit-user-select: none;
      /* 新增Safari/Chrome支持 */
      -moz-user-select: none;
      /* 新增Firefox支持 */
      -ms-user-select: none;
      /* 新增IE10+支持 */
      user-select: none;
    }

    .sender-info {
      position: absolute;
      top: -24px;
      color: @kyy_font_3;
      display: flex;
      justify-content: flex-start;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      max-width: 90%;
      gap: 4px;
      margin-bottom: 4px;

      .name__text {
        line-height: 20px;
        height: fit-content;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .follow__text {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 4px;
        height: fit-content;
        border-radius: 4px;
        background-color: var(--bg-kyy_color_bg_light, #FFF);
        user-select: none;
        white-space: nowrap;

        &::before {
          content: '';
          background: url('@renderer/assets/identity/0.icon_icon-star-fill.png');
          background-size: 16px;
          width: 16px;
          height: 16px;
        }
      }

      .label_tag {
        height: fit-content;
        color: var(--kyy_color_tag_text_success, #499D60);
        padding: 0px 4px;
        border-radius: 4px;
        text-align: center;
        white-space: nowrap;
        background: var(--kyy_color_tag_bg_success, #E0F2E5);
      }

      .sent-time__text {
        display: block;
        visibility: hidden;
        line-height: 20px;
      }

      &.self {
        justify-content: flex-end;
        flex-direction: row-reverse;
      }
    }

    .msg-actions-root {
      position: relative;
      width: 0;
      display: flex;
      flex-direction: inherit;
      overflow: visible;
      align-self: flex-end;
    }

    .msg-actions {
      position: absolute;
      height: 28px;
      width: 57px;
      bottom: 0;
      visibility: hidden;
      display: flex;
      flex-direction: inherit;
      align-items: center;
      border-radius: 8px;
      border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      background: var(--bg-kyy_color_bg_light, #FFF);
      overflow: hidden;

      .action-sep {
        width: 1px;
        height: 16px;
        background: var(--divider-kyy_color_divider_light, #ECEFF5);
      }

      .action-btn:hover {
        background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      }
    }
  }
}

.msg-cont-center {
  justify-content: center;

  .message-content {
    flex: none;
  }
}

.center-tips {
  text-align: center;
  color: rgb(130, 141, 165);
}

.msg-row[data-center="false"]:hover {

  .msg-actions,
  .sent-time__text {
    visibility: visible !important;
  }
}

.msg-row[data-msg-align='center'] .message-content {
  justify-content: center;
  padding: 0 48px;
  width: 0;
}

.read-status {
  height: 22px;
  padding: 0 48px;
  margin-top: 4px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: flex-start;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  -webkit-user-select: none;
  /*webkit浏览器*/
  user-select: none;

  & span {
    color: #4D5EFF;
  }

  & .read {
    color: @kyy_font_3;
  }
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
  }
}

.send-status {
  align-self: stretch;
  display: flex;
  justify-content: center;
  align-items: end;
  left: 52px;
}

.send-error {
  color: @kyy_red_5;
  align-self: flex-end;
  margin-bottom: 2px;

  &:hover {
    opacity: 0.8;
  }
}

.re-edit {
  margin-left: 4px;
  color: @kyy_brand_6;
  cursor: pointer;

  &:hover {
    color: @kyy_brand_7;
  }
}

.chat-msg-go {
  position: absolute;
  top: 72px;
  right: 0px;
  z-index: 100;
  font-size: 12px;
  color: @kyy_brand_6;
  display: flex;
  flex-direction: column;
  gap: 10px;
  user-select: none;

  .go-item {
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    background-color: #fff;
    border-radius: 16px 0 0 16px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  }
}

.group-invite-tips {
  display: flex;
  flex-direction: row;
  line-height: 22px;
  align-items: center;
  padding: 16px;
  font-size: @kyy_fontSize_2;
  background-color: @kyy_brand_2;
  color: @kyy_brand_6;

  & span {
    color: @kyy_font_1;
    margin-left: 8px;
    margin-right: 4px;
  }
}

.group-invite-dialog {
  .invite-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid @kyy_gray_3;
    padding: 8px;
  }

  .invite-info {
    flex: 1;
    margin-left: 6px;
    font-size: @kyy_fontSize_1;
    color: @kyy_font_1;
  }

  .invitor {
    color: @kyy_brand_6;
  }

  .team {
    color: @kyy_orange_6;
  }

  .remark {
    color: @kyy_font_2;
  }

  .btns {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }
}

.consultInfo {
  position: absolute;
  z-index: 99;
  right: 11px;
  bottom: 11px;
  width: 400px;
  // height: 76px;
  border-radius: 16px;
  border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-default, #FFF);

  /* kyy_shadow_m */
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  vertical-align: middle;
  padding: 16px 24px;

  .info {
    margin-right: 12px;
    overflow: hidden;
    display: inline-block;
    vertical-align: middle;

    .img {
      width: 44px;
      height: 44px;
      display: inline-block;
      vertical-align: middle;
      margin-right: 12px;
    }

    .desc {
      overflow: hidden;
      display: inline-block;
      vertical-align: middle;
      width: 200px;

      .name {
        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        font-size: 14px;
        line-height: 22px;
        font-weight: 400;
        color: var(--text-kyy-color-text-1, #1A2139);
      }

      .content {
        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        font-size: 14px;
        line-height: 22px;
        font-weight: 400;
        color: var(--text-kyy-color-text-3, #828DA5);

      }
    }

  }

  .send {
    display: inline-block;
    vertical-align: middle;
    color: var(--color-button-primary-kyy-color-button-primary-text, #FFF);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    height: 24px;
    width: 52px;
    padding: 0px 12px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4D5EFF);
  }

  .close {
    display: inline-block;
    vertical-align: middle;
    margin-left: 18px;
  }
}

.msg-chat-text {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
</style>
