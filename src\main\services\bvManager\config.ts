import { winURL } from '../../config/StaticPath';
import { ViewConfig } from './types';

export interface WebContentsViewConfigs {
  [key: string]: ViewConfig;
}

export const defaultConfig: WebContentsViewConfigs = {
  'default': {
    type: 'default',
    baseURL: winURL,
    url: `${winURL}#/loading`,
    maxPoolSize: 2,
    autoReload: true,
    viewOptions: {
      bounds: { x: 0, y: 0, width: 1200, height: 800 },
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    }
  },
  // 'pop': {
  //   type: 'pop',
  //   url: `${winURL}#/loading`,
  //   maxPoolSize: 1,
  //   autoReload: true,
  //   viewOptions: {
  //     bounds: { x: 0, y: 0, width: 800, height: 600 },
  //     webPreferences: {
  //       nodeIntegration: true,
  //       contextIsolation: false
  //     }
  //   }
  // },
};
