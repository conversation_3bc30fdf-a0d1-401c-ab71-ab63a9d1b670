<template>
  <div
    ref="RTableRef"
    class="RTable"
  >
    <!-- 头部 -->
    <div
      v-show="tabs || (top && top.btns && top.btns.length) || filter || $slots.topContent || $slots.toolbarContent"
      ref="headerRef"
      :class="{'header':true, 'sticky': sticky }"
    >
      <div
        ref="topWrapRef"
        class="top-wrap"
      >
        <Tabs
          v-if="props?.tabs && props?.tabs?.list && props?.tabs?.list.length > 0"
          class="tabs"
          :default-info="props?.tabs.defaultInfo"
          :list="props?.tabs?.list"
          :attrs="props?.tabs?.attrs"
          @change="tabsChange"
        />
        <div
          v-if="$slots.topContent"
          class="topContent"
        >
          <slot name="topContent" />
        </div>
        <div
          v-if="top && top.btns && top.btns.length > 0"
          class="top-btns"
        >
          <TButton
            v-for="(item, index) in top.btns"
            v-bind="item.attrs"
            :key="index"
            :content="item.value"
            @click="item.click"
          />
        </div>
      </div>
      <div
        v-if="filter || $slots.toolbarContent"
        ref="toolbarWrapRef"
        class="toolbar-wrap"
      >
        <div class="toolbar-box">
          <Filter
            v-if="filter"
            ref="filterRef"
            :attrs="filter?.attrs"
            :advanced="filter?.advanced"
            :default-info="filter?.defaultInfo"
            @change="filterChange"
          />
        </div>
        <div
          v-if="$slots.toolbarContent"
          class="toolbarContent"
        >
          <slot name="toolbarContent" />
        </div>
      </div>
    </div>
    <div
      v-show="$slots.customContent"
      ref="customContentWrapRef"
      class="custom-content-wrap"
    >
      <slot name="customContent" />
    </div>
    <!-- 表格 -->
    <div
      v-if="table"
      ref="tableWrapRef"
      class="table-wrap"
    >
      <Table
        :row-key="table.rowKey ?? 'id'"
        :attrs="attrs || {}"
        :list="table?.list || []"
        :columns="table?.columns || []"
        :pagination="pagination"
        @change="tableChange"
      >
        <template #empty>
          <slot
            v-if="$slots.empty"
            name="empty"
          />
          <Empty
            v-else
            :name="isNoData ? 'no-result' : 'no-data-new'"
          >
            <template #tip>
              <div class="tip">
                {{ isNoData ? '无搜索结果' : emptyData }}
              </div>
            </template>
          </Empty>
        </template>
      </Table>
    </div>
  </div>
</template>

<script setup lang="tsx" name="RTable">
import {ref, onMounted, watch, computed, onUnmounted} from 'vue';
import { Button as TButton } from 'tdesign-vue-next';
import Empty from '../../components/Empty/index.vue';
import Tabs from '../../components/Tabs/index.vue';
import Table from '../../components/Table/index.vue';
import Filter from '../../components/Filter/index.vue';
import { TabsType } from '../../components/Tabs/type';
import { TableType } from '../../components/Table/type';
import { FilterType } from '../../components/Filter/type';

export interface RTableType {
  tabs?: TabsType,
  table?: TableType,
  filter?: FilterType,
  top?: {
    btns?: Array<Record<string, any>>
  },
  isNoData?: boolean, //是否无搜索条件
  isSticky?: boolean, // 头部吸顶
  isStickyHeader?: boolean, // 头部吸顶
  change?: Function, // 筛选条件变化
  isChangeClean?: boolean, // 是否清空筛选条件
  emptyData?: string, // 无数据文案
}
const emit = defineEmits(['change']);
const props = withDefaults(defineProps<RTableType>(),{
  isChangeClean: true,
  isSticky: true,
  isNoData: false,
  emptyData: '暂无数据'
})

const RTableRef = ref(null);
const headerRef = ref(null);
const topWrapRef = ref(null);
const toolbarWrapRef = ref(null);
const customContentWrapRef = ref(null);
const tableWrapRef = ref(null);
const filterRef = ref({});
const pagination = ref(null)
const sticky = ref(props.isSticky)
const isNoData = ref(props.isNoData)
const emptyData = ref(props.emptyData)
const attrs = ref(props.table.attrs);
const tabs = ref(props.tabs);
const table = computed(() => props.table);
const top = ref(props.top);
const tabsData = ref({});
const filterData = ref({});
const tableData = ref({
  pageInfo: props.table?.pagination,
  sortInfo: {},
});
const tableHeight = ref(0);
const searchResult = ref(null);
const isChangeClean = ref(typeof props.isChangeClean !== 'undefined' ?  props.isChangeClean : true);
let resizeObserver: ResizeObserver | null = null;

watch(() => props.isNoData, (val) => {
  isNoData.value = val;
});
watch(() => props.isSticky, (val) => {
  sticky.value = val;
});
watch(() => props.table.pagination, (val) => {
  console.log('props.table.pagination', val);
  if (val && val?.total > val?.pageSize) {
    pagination.value = val
  } else {
    pagination.value = null;
  }
}, { immediate: true, deep: true });
const init = () => {
  // 目前按照默认高度显示 无高度计算
  // 延迟执行，防止获取不到dom
  // setTimeout(setHeight, 10);
}

const setHeight = () => {
  tableHeight.value = RTableRef.value?.clientHeight - tableWrapRef.value?.offsetTop;
  const tableAttrs = {
      ...props.table.attrs,
  };

  tableAttrs.height = table.value.height ? table.value.height : tableHeight.value - 64
  table.value.attrs = tableAttrs;

  console.log('tableHeight', tableAttrs.height, table.value.height)
}
// window.addEventListener('resize', init);
const filterChange = (data) => {
  filterData.value = data;
  onChange('filter');
}

const tabsChange = (data) => {
  tabsData.value = data;
  onChange('tab');
}

const tableChange = (data) => {
  // tableData.value = data;
  console.log('tableChange',data);
  if(data.name === 'pageChange'){
    tableData.value.pageInfo = {
      ...tableData.value?.pageInfo,
      ...data.pageInfo,
    };
  } else if(data.name === 'sortChange'){
    tableData.value.sortInfo = data.sortInfo;
  }
  onChange('table');
}

const onChange = (name) => {
  console.log('onChange', tableData.value, table.value);
  const data = {
    tabs: tabsData.value,
    filter: { ...filterData.value },
    pageInfo: { ...tableData.value?.pageInfo },
    sortInfo: { ...tableData.value?.sortInfo }
  }
  // 检查是否是搜索类型 通过 isNoData 判断
  // if (filterData.value) {
  //   searchResult.value = Object.values(filterData.value).find(v => v !== '') ? true : false;
  // }

  // tab类型切换 过滤数据清空
  if (name === 'tab' && props.filter && isChangeClean.value) {
    filterRef.value.clear()
    data.filter = {};
  }

  emit('change', data, name);
}

onMounted(() => {
  init();
  window.addEventListener('resize', adjustEmptyRowHeight);

  // 创建 ResizeObserver 监听元素高度变化，isStickyHeader为 false 时，不监听header和custom-content-wrap高度变化
  if (props.isStickyHeader) {
    resizeObserver = new ResizeObserver(() => {
      updateTableHeight();
      adjustEmptyRowHeight();
    });
    // 监听 header 高度变化
    if (headerRef.value) {
      resizeObserver.observe(headerRef.value);
    }
  
    // 监听 custom-content-wrap 高度变化
    if (customContentWrapRef.value) {
      resizeObserver.observe(customContentWrapRef.value);
    }
  }

});

onUnmounted(() => {
  window.removeEventListener('resize', adjustEmptyRowHeight);

  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
})

// 根据RTable高度和表头高度计算空状态行的高度
const adjustEmptyRowHeight = () => {
  setTimeout(() => {

    const rtableElem = document.querySelector('.RTable');
    const headerElem = document.querySelector('.RTable .header');
    const customContentWrapElem = document.querySelector('.RTable .custom-content-wrap');
    const tableHeaderElem = document.querySelector('.RTable .t-table__header');
    const emptyRowElem = document.querySelector('.RTable .t-table__empty-row');

    if (rtableElem && tableHeaderElem && emptyRowElem) {
      const rtableHeight = rtableElem?.clientHeight || 0;
      const tableHeaderHeight = tableHeaderElem?.clientHeight || 0;
      const headerHeight = headerElem?.clientHeight || 0;
      const customContentWrapHeight = customContentWrapElem?.clientHeight || 0;
      const emptyHeight = rtableHeight - tableHeaderHeight - headerHeight - customContentWrapHeight; // 减去10px留出一点空间

      // 设置空状态行的高度
      emptyHeight > 0 && ((emptyRowElem as HTMLElement).style.height = `${emptyHeight}px`);
    }
  });
};

// 获取 table 的高度
const getTableHeight = () => {
  const rtableElem = document.querySelector('.RTable');
  const headerElem = document.querySelector('.RTable .header');
  const customContentWrapElem = document.querySelector('.RTable .custom-content-wrap');

  if (rtableElem) {
    const rtableHeight = rtableElem?.clientHeight || 0;
    const headerHeight = headerElem?.clientHeight || 0;
    const customContentWrapHeight = customContentWrapElem?.clientHeight || 0;

    const tableHeight = rtableHeight - customContentWrapHeight - headerHeight;
    console.error('tableHeigh', tableHeight);

    return tableHeight > 0 ? tableHeight : 'auto';
  }
  return 'auto';
}

// 更新表格高度
const updateTableHeight = () => {
  console.error('updateTableHeight');
  setTimeout(() => {
    const newHeight = getTableHeight();
    attrs.value = {
      ...props.table.attrs,
      maxTableHeight: newHeight
    };
  });
}

watch(() => props.table.attrs, (val) => {
  if (val) {
    attrs.value = {
      ...attrs.value,
      ...val,
    }
  }
}, { immediate: true, deep: true })

// 监听表格数据变化，调整空行高度
watch(() => props.table?.list, () => {
  if(!props.table?.list?.length){
    adjustEmptyRowHeight();
  }
}, { immediate: true, deep: true });

defineExpose({
  init
})
</script>

<style lang="less" scoped>
.RTable{
  height: 100%;
  .header{
    padding-bottom: 16px;
    &.sticky{
      position: sticky;
      top: 0;
      z-index: 100;
      background: #fff;
    }
  }
  .top-wrap .topContent,
  .toolbar-wrap{
    padding-top: 16px;
  }
  .custom-content-wrap{
    padding-bottom: 16px;
  }
  .top-wrap{
    position: relative;
    .top-btns{
      z-index: 100;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
  }
  .toolbar-wrap{
    display: flex;
    justify-content: space-between;
    .toolbar-box{
      flex:1;
      align-items: center;
      text-align: left;
    }
  }
  :deep(.t-table) {
    .t-table__pagination-wrap {
      .t-pagination__number.t-is-current {
        background-color: #4d5eff;
      }
    }
  }
}
</style>
<style lang="less">
.RTable{
  .toolbar-wrap{
    .filter-input {
      .t-input {
        height: 32px;
      }
    }
  }
  .RK-Table {
    td {
      > span {
        word-break: break-all;
        white-space: normal;
        overflow: hidden;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
    }
    // .t-text-ellipsis{
    //   word-break: break-all;
    //   white-space: normal;
    //   overflow: hidden;
    //   -webkit-line-clamp: 2;
    //   display: -webkit-box;
    //   -webkit-box-orient: vertical;
    // }
  }
}
.t-popup {
  &.t-select__dropdown{
    .t-select-option.t-is-selected:not(.t-is-disabled) {
      color: #4d5eff;
    }
  }
}
</style>
