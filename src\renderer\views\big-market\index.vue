<template>
  <div class="flex flex-col h-full bg-[#fff]" v-lkloading="{ show: lkloadingFlag }">
    <div class="flex-shrink-0">
      <work-area-head
        :tab-list="tabList"
        :active-index="activeIndex"
        :group-list="groupList"
        :activation-group-item="activationGroupItem"
        :not-red="true"
        @open-refresh="openRefresh"
        @deltab-item="deltabItem"
        @upt-work-bench-tab-item="uptWorkBenchTabItem"
        @set-work-bench-tab-item="setWorkBenchTabItem"
        @get-group-list-api="getGroupListApi"
        @setactivation-group-item="setactivationGroupItem"
        @set-active-index="setActiveIndex"
      />
    </div>
    <div class="h-0 flex-grow-2 relative">
      <router-view
        v-slot="{ Component }"
        :tab-list="tabList"
        :activation-group-item="activationGroupItem"
        :group-list="groupList"
        :tab-index="tabIndex"
        class="work-bench-index"
        :cloud-disk-type="activationGroupItem"
        @open-refresh="openRefresh"
        @get-group-list-api="getGroupListApi"
        @deltab-item="deltabItem"
        @add="add"
        @set-active-index="setActiveIndex"
        @update-title="updateTitle"
        @upt-work-bench-tab-item="uptWorkBenchTabItem"
        @set-work-bench-tab-item="setWorkBenchTabItem"
      >
        <keep-alive :key="pageKey" :exclude="data.exc">
          <component
            :is="Component"
            v-if="data.showCompoent"
            :key="Component.key"
            ref="routerViewRef"
            :exclude="data.exc"
          />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { escape, isEmpty } from "lodash";
import { useRoute, useRouter } from "vue-router";
import WorkAreaHead from "./components/WorkAreaHead.vue";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import LynkerSDK from "@renderer/_jssdk";
import { destroyNode, getShowNode, refreshNode } from "@renderer/_jssdk/components/iframe/iframePool";
import { DialogPlugin } from "tdesign-vue-next";
const { ipcRenderer } = LynkerSDK;

const pageKey = ref(1);

const { t } = useI18n();
const lkloadingFlag = ref(false);
const router = useRouter();
const route = useRoute();
const routerViewRef = ref(null);
const tabList = ref([]);
const groupList = ref([]);
const activationGroupItem = ref({});
const tabIndex = ref(0);
const data = ref({
  showCompoent: true,
  exc: "",
});
const emits = defineEmits(["add"]);

const add = () => {
  emits("add");
};

const activeIndex = ref(0);
const deltabItem = async (index, flag, notTip) => {
  const tab = tabList.value?.[index]
  if (tab?.beforeCloseOptions) {
    const res = await handleBeforeClose(tab.beforeCloseOptions.title, tab.beforeCloseOptions.content);
    if (!res) {
      return false;
    }
  }
  // 根据审批模块需求变更为数组循环
  let nameArr = tabList.value[index]?.name?.split(",") || [];
  console.log(nameArr, "nameArr");
  nameArr.forEach((e) => {
    setTimeout(() => {
      data.value.exc = e;
      data.value.showCompoent = false;
      setTimeout(() => {
        data.value.showCompoent = true;
        data.value.exc = "";
      });
    });
  });
  if (tab?.path_uuid) {
    getShowNode()?.forEach((i) => {
      if (i.getAttribute('data-id') === tab?.path_uuid) {
        destroyNode(i.getAttribute('data-id'));
      }
    });
  } else {
    getShowNode()?.forEach((i) => {
      destroyNode(i.getAttribute('data-id'));
    });
  }
  tabList.value.splice(index, 1);
  console.log(tabList.value, "啊塞擦声");
  if (flag) {
    const tabItemNext = tabList.value[tabList.value.length - 1];
    router.push({
      path: tabItemNext.path || tabItemNext.fullPath,
      query: isEmpty(tabItemNext.query) ? route.query : tabItemNext.query,
    });
  }
};

onMountedOrActivated(() => {});
const openRefresh = () => {
  // pageKey.value++;
  const currentRoute = router.currentRoute.value;
  console.log(currentRoute, "当前路由aseawdaw");
  currentRoute.matched.forEach((r) => {
    // fullPath替换成了path
    if (r.path === currentRoute.path) {
      // 获取到当前页面的name
      const comName = r.components.default.name;
      console.log(comName, "拿到的name");
      if (comName !== undefined) {
        data.value.exc = comName;
        data.value.showCompoent = false;
      }
    }
  });
  getShowNode()?.forEach((i) => {
    refreshNode(i);
  });
  setTimeout(() => {
    data.value.showCompoent = true;
    data.value.exc = "";
  });
};

const setActiveIndex = (index) => {
  activeIndex.value = index;
};
ipcRenderer.on("set-big-market-tab-item", (e, val) => {
  console.log(val, "vaset-work-bench-tab-iteml");
  setWorkBenchTabItem(val);
  if (val.gopath) {
    router.push(val.path);
  }
});

// 更新tab
const uptWorkBenchTabItem = (item) => {
  if (item.updateKey) {
    const index = tabList.value.findIndex((v) => v?.updateKey === item.updateKey);
    tabList.value[index] = item;
  }
};
const updateTitle = (item) => {
  // if (item.title) {
  //   const index = tabList.value.findIndex((v) => v?.name === item.name);
  //   tabList.value[index].title = item.title;
  // } 7.24交互说放弃这个更新页签标题功能
};

const setWorkBenchTabItem = (item) => {
  if (item.addNew) {
    const index = tabList.value.findIndex(
      (v) => v?.path === item.path && JSON.stringify(v?.query) === JSON.stringify(item.query),
    );
    console.log("index", tabList.value);
    console.log("item", item);
    console.log("index", index);
    if (index === -1) {
      tabList.value.push(item);
    } else {
      tabList.value[index] = item;
    }
  } else {
    const index = tabList.value.findIndex((e) => e.path === item.path);
    const hasDuplicate = index !== -1;
    if (hasDuplicate) {
      tabList.value[index] = item;
    } else {
      tabList.value.push(item);
    }
  }
};

const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); } }); });

onMounted(() => {
  LynkerSDK.ipc.handleRenderer('big-market-is-inited', async () => {
    console.log('big-market-is-inited');
    return true;
  });
  LynkerSDK.ipc.handleRenderer('big-market-reload', async () => {
    console.log('big-market-reload');
    openRefresh();
    return true;
  });
  LynkerSDK.ipc.handleRenderer('big-market-get-active-team-id', async () => {
    console.log('big-market-get-active-team-id');
    return '123';
  });
  LynkerSDK.ipc.handleRenderer('big-market-set-active-team-id', async (query) => {
    console.log('big-market-set-active-team-id', query);
  });
  LynkerSDK.ipc.handleRenderer('get-big-market-tab-list', async () => {
    console.log('get-big-market-tab-list');
    return JSON.parse(JSON.stringify(tabList.value));
  });
  LynkerSDK.ipc.handleRenderer('open-big-market-tab-item', async (query) => {
    console.log('open-big-market-tab-item', query);
    // {
    //   "fullPath": "/bigMarketIndex/big_market_webview/demo-唯一uuid",
    //   "label": "b站",
    //   "icon": "workshop",
    //   "activeIcon": "workshop",
    //   "path_uuid": "demo-唯一uuid",
    //   "query": {
    //       "url": "https://www.bilibili.com/",
    //       "isClose": true
    //   }
    // }

    const index = tabList.value.findIndex(
      (v) => v?.path_uuid && v?.path_uuid === query.path_uuid,
    );
    console.error('index', tabList.value, index);
    if (index === -1) {
      tabList.value.push(query);
    } else {
      tabList.value[index] = Object.assign(tabList.value[index], query);
    }
    if (query.fullPath) {
      router.push({
        path: query.fullPath,
        query: query.query,
      });
    }
    // console.log("index", tabList.value);
    // console.log("item", item);
    // console.log("index", index);
    // if (index === -1) {
    //   tabList.value.push(item);
    // } else {
    //   tabList.value[index] = item;
    // }
    return true;
  });
  LynkerSDK.ipc.handleRenderer('close-big-market-tab-item', async (query) => {
    console.log('close-big-market-tab-item', query);
    const tab = tabList.value.find((i) => i?.path_uuid && i.path_uuid === query.path_uuid);
    if (tab?.beforeCloseOptions) {
      const res = await handleBeforeClose(tab.beforeCloseOptions.title, tab.beforeCloseOptions.content);
      if (!res) {
        return false;
      }
    }
    const index = tabList.value.findIndex(
      (v) => v?.path_uuid && v?.path_uuid === query.path_uuid,
    );
    if (index !== -1) {
      tabList.value.splice(index, 1);
      const tabItemNext = tabList.value[tabList.value.length - 1];
      router.push({
        path: tabItemNext.path || tabItemNext.fullPath,
        query: isEmpty(tabItemNext.query) ? route.query : tabItemNext.query,
      });
    }
    if (tab?.path_uuid) {
      getShowNode()?.forEach((i) => {
        if (i.getAttribute('data-id') === tab?.path_uuid) {
          destroyNode(i.getAttribute('data-id'));
        }
      });
    } else {
      getShowNode()?.forEach((i) => {
        destroyNode(i.getAttribute('data-id'));
      });
    }
    return true;
  });
  LynkerSDK.ipc.handleRenderer('update-big-market-tab-item', async (query) => {
    console.log('update-big-market-tab-item', query);
    const index = tabList.value.findIndex(
      (v) => v?.path_uuid && v?.path_uuid === query.path_uuid,
    );
    console.error('index', tabList.value, index);
    if (index === -1) {
      tabList.value.push(query);
    } else {
      tabList.value[index] = Object.assign(tabList.value[index], query);
    }
    return true;
  });
});
</script>
<style>
.work-bench-index {
  background: #fff;
  overflow: auto;
  height: calc(100%) !important;
}
::-webkit-scrollbar {
  width: 4px;
  background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color: #e3e6eb;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #c8c8c8;
}
</style>
<style lang="less" scoped>
:deep(.appAuthTipDialog) {
  .t-dialog__body__icon {
    padding: 16px 0 24px;
  }
  .t-button--theme-default {
    font-weight: 600;
  }
}
</style>
