<template>
  <div
    ref="draggableRef"
    class="draggable"
    @mousedown="handleMouseDown"
  >
    <slot></slot>
  </div>
</template>

<script lang="ts" setup name="Draggable">
import { ref, computed, onMounted, nextTick } from 'vue';
const props = defineProps({
  isDrag: {
    type: Boolean,
    default: true
  },
  styles: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['change']);
const draggableRef = ref<HTMLElement>();
const isDragging = ref(false);
const startX = ref(0);
const startY = ref(0);
const startTransformX = ref(0);
const startTransformY = ref(0);
const transformX = ref(0);
const transformY = ref(0);
const isDrag = ref(props.isDrag);
onMounted(() => {
  nextTick(() => {
    console.log('onMounted====', props.styles);
    if (props.styles?.transform) {
      draggableRef.value.style.setProperty('transform', props.styles.transform);
    }
  })
});
const style = (x:number, y:number) => {
  // console.log('style====', x, y);
  emit('change', { x, y });
  draggableRef.value.style.setProperty('transform', `translate3d(${x}px, ${y}px, 0)`);
};


const handleMouseDown = (e: MouseEvent) => {
  e.stopPropagation();
  if (!props.isDrag) return;
  isDragging.value = true;
  startX.value = e.clientX;
  startY.value = e.clientY;

  // console.log(e, e.target, draggableRef.value);
  // 获取当前元素的 transform 值
  const element = draggableRef.value as HTMLElement;
  const style = window.getComputedStyle(element);
  const transform = style.transform || style.webkitTransform;
  const matrix = new DOMMatrix(transform);
  startTransformX.value = matrix.m41;
  startTransformY.value = matrix.m42;
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const handleMouseMove = (e: MouseEvent) => {
  if (!props.isDrag) return;
  if (!isDragging.value) return;
  draggableRef.value.style.pointerEvents = 'none';
  const deltaX = e.clientX - startX.value;
  const deltaY = e.clientY - startY.value;
  let newTransformX = startTransformX.value + deltaX;
  let newTransformY = startTransformY.value + deltaY;

  // 获取元素的尺寸和位置信息
  const element = draggableRef.value as HTMLElement;
  const rect = element.getBoundingClientRect();
  const elementWidth = rect.width;
  const elementHeight = rect.height;

  // 主窗口有titleBar 和 leftBar需要判断下
  const titleBar = document.querySelector('.main-box-shadow .win-title') as HTMLElement;
  const leftBar = document.querySelector('.main-box-shadow .left-bar') as HTMLElement;
  const top = (titleBar ? titleBar.offsetHeight : 0) - window.innerHeight + elementHeight;
  const left =  (leftBar ? leftBar.offsetWidth : 0) - window.innerWidth + elementWidth;

  // console.log('handleMouseMove', newTransformY, top,  titleBar.offsetHeight, window.innerHeight, elementHeight);
  // 计算边界限制
  if (newTransformX <= left ) {
    newTransformX = left;
  } else if (newTransformX >= 0) {
    newTransformX = 0;
  }

  if (newTransformY <= top) {
    newTransformY = top;
  } else if (newTransformY >= 0) {
    newTransformY = 0 ;
  }

  transformX.value = newTransformX;
  transformY.value = newTransformY;

  style(transformX.value, transformY.value);
};

const handleMouseUp = () => {
  isDragging.value = false;
  draggableRef.value.style.pointerEvents = null;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  return ;
};
</script>

<style scoped>
.draggable {
  cursor: pointer;
  user-select: none;
}
</style>
