.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

.t-dialog__ctx.t-dialog__ctx--fixed.move-files {
  z-index: 9999999 !important;
}

.t-dialog__header-content {
  color: var(--kyy_color_modal_title, #1A2139);

}
.t-dialog__close {
  width: 24px;
  font-size: 24px;
  height: 24px;
  color: var(--text-kyy_color_text_2, #516082);
  padding: 0;
}
.colorsssssss {
  .t-dialog__footer {
    .t-button {
      min-width: 80px;
    }
  }
}

.t-dialog__footer {
  padding-top: 8px;
  .t-button {
    min-width: 80px;
  }
}



.t-dialog--default {
  padding: 24px;
}
.remark-dialog .t-dialog--default {
  padding: 16px;
}

.t-dialog__body {
  overflow: hidden;
  .footer .t-button {
    min-width: 80px;
  }
}

.t-dialog {
  border: 0;
  border-radius: 16px !important;
  box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.12);
}

.t-dialog__close .t-icon-close{
  //transform: scale(1.4);
}
.t-dialog__ctx .t-dialog__position.t-dialog--top {
  padding-top: 0;
  // IMPORTANT: 此处改变了弹窗的默认行为（应该设置 placement="center"），还原以不影响当前代码。手动使用下方 .dialog-top 类以重置
  align-items: center;
}

// 重置被污染的样式
.dialog-top.t-dialog__ctx .t-dialog__position.t-dialog--top {
  padding-top: 16px;
  padding-bottom: 16px;
  align-items: flex-start;
}

// .t-dialog--default {
//   padding-top: 24px;
// }


.t-dialog .t-icon.t-is-info {
  color: var(--kyy_color_brand_default, #4D5EFF)
}

.t-dialog__body__icon {
  margin-right: 10px;
  color: var(--font-kyy-font-gy-2, #516082);
  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.t-dialog__ctx .t-dialog__position {
  height: 100% !important;
}

.delmode,
.backfile {
  .t-dialog__body__icon {
    margin-right: 10px;
    color: var(--font-kyy-font-gy-2, #516082);
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }
}

// lss
.dialogSetRadius {
  .t-dialog__body {
    border-bottom-right-radius: 20px;
  }

}
.dialogSetDefault32 {
  .t-dialog--default {
    padding: 32px !important;
  }
}

// lss
.dialogSetLimitHeight {
  .t-dialog__body {
    max-height: 500px !important;
    overflow: auto;
  }
  .t-dialog__close {
    padding: 0 !important;
  }
}
.dialogSetLimitHeight560 {
  .t-dialog__body {
    max-height: 560px !important;
    overflow: auto;
  }

}

.dialogSet-noBodyPadding {
  .t-dialog__body {
    padding: 0 !important;
  }
}
.dialogSetNP {
  .t-dialog--default {
    padding: 0 !important;
  }
}

.dialogSetNLeftRIght {
  .t-dialog--default {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.dialogSet {
  .t-dialog__body {
    // max-height: 500px !important;
    // overflow: auto;
  }
  .t-dialog__close {
    padding: 0 !important;
  }
  .t-input__extra {
    font-size: 12px;
  }
}

.dialog-add{
  .t-dialog__body {
    padding: 24px 8px 16px !important;
  }
}
.t-dialog__footer{
  .t-button__text{
    padding:0 4px;
  }
}

.dialogSet20240801 {
  .t-dialog__body {
      padding: 0 !important;
  }
}

.dialogNoDefault {
  .t-dialog--default {
    padding: 0!important;
  }
}

.dialogHeight560 {
  .t-dialog {
      padding: 0 !important;
      max-height: 560px;
  }
}

.dialogSetHeader{
  .t-dialog__header {
    padding: 24px !important;
  }
}


.dialogSetFooter_top_0{
  .t-dialog__footer {
    padding-top: 0;
  }
}


.dialogSetHeaderInvite{
  .t-dialog__header {
    padding: 24px !important;
    padding-bottom: 0px !important;
  }
}

.dialogSetForm-noPadding {
  .t-dialog {
      padding: 0 !important;
      max-height: 560px;
  }
  .t-dialog__header {
      border-bottom: 0 !important;
      padding: 16px 24px !important;
      min-height: 0 !important;
  }
  .t-dialog__close-btn {
      color: #516082 !important;
      right: 24px !important;
  }
  .t-dialog__body {
      // padding-top: 6px !important;
      padding: 0 24px;
      overflow: overlay;
      max-height: 424px;
  }
  .t-dialog__close-btn {
      // padding-right: 24px;
      .iconerrror {
      font-size: 22px;
      color: #516082;
      }
  }
  .t-dialog__footer {
      padding: 24px;
  }
}
