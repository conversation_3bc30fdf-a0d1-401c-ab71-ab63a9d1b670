<template>



  <div class="container container-pc" v-if="optionType === 1">
    <!-- 验证码登录或注册 -->
    <div class="login-tab-content" v-show="loginType === 1">
      <img v-if="route.query.hasClose" @click="router.go(-1)" class="closeback" src="@/assets/closeback.svg">
      <div class="login-welcome">
        <div class="wel-text">欢迎使用另可 </div>
        <div class="wel-text1">未注册手机号验证后自动创建另可账号 </div>
      </div>
      <div v-show="loginType === 1" class="content-item password mt12">
        <div class="region-box">
          <span class="region-text"><span @click="descVisible=true">所在地区</span> <img @click="regionTipFn"
              src="@/assets/icon_help.svg"
              style="vertical-align: middle; width: 24px; height: 24px;font-size: 24px;"></span>
          <span class="region-text" @click="changeRegionAddress"><span>{{changeRegionCode?.name}}</span> <img
              src="@/assets/icon_down_one.svg"
              style="vertical-align: middle; width: 24px; height: 24px;font-size: 24px;"></span>
        </div>
        <div class="region-box" style="margin-top: 16px;">
          <span class="region-iphone-code"><span class="iphone-code">{{'+'+changeRegionCode?.code}}</span>
            <div class="lin"></div>
            <t-input @compositionstart="handleCompositionStart" @compositionend="handleCompositionEnd" :maxlength="13"
              v-model="iphoneCode" @input="handleCodeInput" @keydown="handleKeyDown" placeholder="请输入手机号"></t-input>
          </span>
        </div>
        <div style="margin-top: 33px;font-size: 14px;color: #1A2139;">
          <t-checkbox style="border-radius: 50%;" v-model="agreeProto">
            我已阅读并同意
            <span style="color: #4D5EFF;">《<a target="_blank"
                :href="`${getBaseUrl('website')}/privacy?uuid=PlatformServices`">{{
                t('account.agreement1')
                }}</a>》</span>和<span style="color: #4D5EFF;">《<a target="_blank"
                :href="`${getBaseUrl('website')}/privacy?uuid=PrivacyPolicy`">{{
                t('account.privacy')
                }}</a>》
            </span>
          </t-checkbox>
        </div>
        <div class="login-act">
          <t-button v-if="timer<=0" class="login-btn" :disabled="ma.mobile" block theme="primary" variant="base"
            @touchstart.stop.prevent="getCode" @click="getCode">获取验证码</t-button>
          <t-button v-else class="login-btn" disabled block theme="primary" variant="base"
            @touchstart.stop.prevent="getCode">获取验证码({{countdown}})</t-button>

        </div>
        <div @click="changeTab(loginType === 1 ? 2 : 1)" style="color:#1A2139;font-size:15px;margin-top:12px">
          {{ loginType === 1 ? t('account.passwordLogin') : t('account.captcha') }}
        </div>
      </div>
    </div>
    <!-- 密码登录 -->
    <div class="login-tab-password" v-show="loginType === 2">
      <img @click="passwordBack" src="@/assets/icon_left.svg">
      <div class="witcode" style="margin-top: 120px;">
        密码登录
      </div>
      <div v-show="loginType === 2" class="content-item password" style="margin-top: 16px !important">

        <div>
          <van-field v-model="ma.mobile" placeholder="请输入手机号/邮箱/另可号" type="text"
            style="border-radius: 8px;margin-bottom: 16px;padding: 12px 16px;">
            <template #button style="display: flex; align-items: center;gap: 12px;">
              <div style="display: flex; align-items: center;gap: 12px;">
                <img v-if='ma.mobile' @click="ma.mobile=''" src="@/assets/svg/icon_close_teansparent.svg">
              </div>
            </template>
          </van-field>
          <van-field v-model="ma.password" placeholder="请输入密码" :type="maPasswordType"
            style="border-radius: 8px;padding: 12px 16px;">
            <template #button style="display: flex; align-items: center;gap: 12px;">
              <div style="display: flex; align-items: center;gap: 12px;">
                <img v-if='ma.password' @click="ma.password=''" src="@/assets/svg/icon_close_teansparent.svg">
                <img v-if="maPasswordType==='password'" @click="maPasswordType='text'"
                  src="@/assets/svg/icon_preview-close.svg">
                <img v-else @click="maPasswordType='password'" src="@/assets/svg/icon_preview-open.svg">
              </div>
            </template>
          </van-field>
        </div>
        <div v-if="errorMsg" class="error-msg">
          {{errorMsg}}
        </div>
        <div style="margin-top: 33px;font-size: 14px;color: #1A2139;">
          <t-checkbox style="border-radius: 50%;" v-model="agreeProto">
            我已阅读并同意
            <span style="color: #4D5EFF;">《<a target="_blank"
                :href="`${getBaseUrl('website')}/privacy?uuid=PlatformServices`">{{
                t('account.agreement1')
                }}</a>》</span>和<span style="color: #4D5EFF;">《<a target="_blank"
                :href="`${getBaseUrl('website')}/privacy?uuid=PrivacyPolicy`">{{
                t('account.privacy')
                }}</a>》
            </span>
          </t-checkbox>
        </div>
        <div class="is-ok-stting-box" :class="!ma.mobile||!ma.password?'disabled':''" style="margin-top: 12px;"
          @click="passwordLogin">
          确定
        </div>

      </div>

    </div>

  </div>
  <!-- 选择国家地区 -->
  <div class="change-region" v-if="optionType === 2">
    <div class="header-box">
      <img src="@/assets/icon_left_black.svg" @click="optionType = 1">
      <span style="color:#1A2139;font-size: 18px;font-weight: 600">选择国家和地区</span>
      <div></div>
    </div>
    <div class="region-list">
      <div class="region-item" v-for="(item, index) in regionList" :key="index" @click="changeRegionFn(item)">
        <span class="region-name">{{item.name}}</span>
        <span class="region-code">+{{item.code}}</span>
      </div>
    </div>

  </div>
  <!-- 输入验证码 -->
  <div class="upd-code" v-if="optionType === 3">
    <img @click="optionType =1" src="@/assets/icon_left.svg">
    <div class="witcode">
      输入验证码
    </div>
    <div class="codenum-box">
      <div class="code-to">验证码已发送至</div>
      <div class="code-phone">{{'+'+changeRegionCode?.code}} {{ma?.mobile}}</div>
      <van-password-input :length="6" :value="captchaNum" :mask="false" :focused="showKeyboard"
        @focus="showKeyboard = true" />
      <div class="agin" v-if="countdown<=0" @click="getCode()"> 重新获取</div>
      <div class="agin" v-else style="color:#828DA5"> 重新获取({{countdown}})</div>

    </div>
  </div>
  <!-- 设置密码 -->
  <div class="upd-code" v-if="optionType === 4">
    <img @click="optionType =1;loginType = 1" src="@/assets/icon_left.svg">
    <div class="witcode">
      设置密码
    </div>
    <div class="codenum-box">
      <van-field v-model="sttingPasswordData.sttingPassword" placeholder="请设置密码" :type="sttingPasswordSee"
        style="border-radius: 8px;margin-bottom: 16px;">
        <template #button style="display: flex; align-items: center;gap: 12px;">
          <div style="display: flex; align-items: center;gap: 12px;">
            <img v-if='sttingPasswordData.sttingPassword' @click="sttingPasswordData.sttingPassword=''"
              src="@/assets/svg/icon_close_teansparent.svg">
            <img v-if="sttingPasswordSee==='password'" @click="sttingPasswordSee='text'"
              src="@/assets/svg/icon_preview-close.svg">
            <img v-else @click="sttingPasswordSee='password'" src="@/assets/svg/icon_preview-open.svg">
          </div>
        </template>
      </van-field>
      <van-field v-model="sttingPasswordData.sttingPassword1" placeholder="请再次确认密码" :type="sttingPasswordSee1"
        style="border-radius: 8px;">
        <template #button>
          <div style="display: flex; align-items: center;gap: 12px;">
            <img v-if='sttingPasswordData.sttingPassword1' @click="sttingPasswordData.sttingPassword1=''"
              src="@/assets/svg/icon_close_teansparent.svg">
            <img v-if="sttingPasswordSee1==='password'" @click="sttingPasswordSee1='text'"
              src="@/assets/svg/icon_preview-close.svg">
            <img v-else @click="sttingPasswordSee1='password'" src="@/assets/svg/icon_preview-open.svg">
          </div>
        </template>
      </van-field>
      <div class="stting-password-text" :class="isRed?'isRed':''">
        {{sttingPasswordText }}
      </div>
      <div style="width: 100%;">
        <div class="is-ok-stting-box" @click="sttingPasswordFn">
          确定
        </div>
        <div class="jump-stting-box" @click="jumpStting">
          跳过
        </div>
      </div>
    </div>
  </div>
  <!-- 用户所在地 -->
  <van-popup v-model:show="descVisible" round position="bottom" :style="{ height: '270px' }">
    <div class="title">
      用户所在地
    </div>
    <div class="desc-region">
      <div class="desc-region-item" v-for="(item, index) in regionCodeList" :key="index"
        @click="regionCodeIndex=index;">
        <div>{{item.name}}</div><img v-if="regionCodeIndex===index" src="@/assets/icon_radioButton.svg">
      </div>
    </div>
    <div class="foot">
      <div style="background:  #F5F8FE;width: 100%;height: 8px;"></div>
      <div class="btn" @click="changeUseRegion">确定</div>
    </div>
  </van-popup>
  <!-- 隐私政策 -->
  <van-popup v-model:show="agreementVisible" round>
    <div class="agreement-box">
      <div class="agreement-text">
        用户协议及隐私政策
      </div>
      <div class="ckd-agreement">
        我已阅读并同意
        <span style="color: #4D5EFF;">《<a target="_blank"
            :href="`${getBaseUrl('website')}/privacy?uuid=PlatformServices`">{{
            t('account.agreement1')
            }}</a>》</span>和<span style="color: #4D5EFF;">《<a target="_blank"
            :href="`${getBaseUrl('website')}/privacy?uuid=PrivacyPolicy`">{{
            t('account.privacy')
            }}</a>》
        </span>
      </div>

      <div class="agreement-foot">
        <div class="btn" @click="agreementVisible=false,agreeProto=false">拒绝</div>
        <div class="btn isok" @click="agreementFn">确定</div>
      </div>


    </div>
  </van-popup>
  <!-- tip -->
  <van-popup v-model:show="tipVisible" round>
    <div class="tip-box">
      <div class="tip-text">
        {{tipText}}
      </div>
      <div v-if="regionTipDis" class="tip-dis-text">
        {{regionTipDis}}
      </div>
      <div class="agreement-foot">
        <div class="btn isok" style="width: 248px;" @click="tipVisible=false">我知道了</div>
      </div>
    </div>
  </van-popup>
  <!-- 数字键盘 -->
  <van-number-keyboard @input="inputFn" maxlength="6" v-model="captchaNum" :show="optionType === 3" @blur="showKeyboard = false" />
</template>

<script setup lang="ts">
  import { Field as VanField, Popup as VanPopup, PasswordInput as VanPasswordInput, NumberKeyboard as VanNumberKeyboard, showToast } from 'vant';
  import { useRouter, useRoute } from 'vue-router';
  import { onMounted, ref, watch } from 'vue';
  import { getBaseUrl } from '@/api/requestApi';
  import { MessagePlugin } from 'tdesign-vue-next';
  import { err_reason, handleTrim, checkPhoneAndMatch } from './util';
  import { loginOrRegisterByMobile, getIdentifyCode, loginAccount, updatePassword, loginAccountV2, listAreaCodes, getProfile } from '@/api/account/login';
  import { useI18n } from 'vue-i18n';
  import {
    setAccesstoken,
    setLang, setOpenimToken,
    getLang,
    setOpenid,
    removeOpenid,
    setSMDeviceId,
    removeSMDeviceId,
  } from '@/utils/auth';
  import { useAccountStore } from '@/stores/account';
  import { setLanguage } from '@/i18n'
  import { encrypt } from '@/utils/myUtils';
  import { initSM, dealSmDeviceId, getSMCaptcha, getSMCaptchaResult } from '@/utils/shumei';
  const maPasswordType = ref('password');
  const changeRegionCode = ref({
    name: '中国大陆',
    code: '86'
  })
  const SMCaptcha = ref(null);
  const iphoneCode = ref('')
  const options = ref([
    {
      content: '简体中文',
      value: 'zh-hans-CN',
      active: false,
    },
    {
      content: '繁體中文',
      value: 'zh-hant-MO',
      active: false,
    },
  ]);
  const useLang = ref('');
  const optionType = ref(1);
  const sttingPasswordData = ref({
    sttingPassword: '',
    sttingPassword1: ''
  });
  const { t } = useI18n();

  const loginType = ref(1);
  const loginDisabled = ref(true);
  const ma = ref({
    mobile: '',
    region: '86',
    code: '',
    password: '',
  });
  const ea = ref({
    mail: '',
    password: '',
  });
  const agreeProto = ref(false);
  const checkPhoneTip = ref('');
  const countdown = ref(-2);
  const timer: any = ref(null);
  const qrcCode = ref(''); // 二维码解析结果
  const qrCodeExpire = ref(); // 二维码过期时间
  const scanSuc = ref(false);
  let checkRegion: any = 0;
  const isComposing = ref(false);
  const sttingPasswordSee = ref('password')

  const sttingPasswordSee1 = ref('password')
  const descVisible = ref(false);
  const showKeyboard = ref(false);
  const tipVisible = ref(false);
  const tipText = ref('');
  const sttingPasswordText = ref('至少8位字符，包含字母与数字')
  const captchaNum = ref('')
  const router = useRouter();
  const route = useRoute();
  const regionList = ref([])

  const regionCodeIndex = ref(null);
  const agreementVisible = ref(false);
  const isRed = ref(false);
  const regionCodeList = [{
    name: '中国大陆',
    code: '86'
  }, {
    name: '中国澳门',
    code: '853'
  }, {
    name: '其他区域',
    code: '86'
  }]

  const accountStore = useAccountStore();
  const redirect = route.query.redirect as string;
  // 携带来源参数
  const errorMsg = ref('')
  const sourceParams = route.query.link as string;
  const regionTipVisible = ref(false)
  const regionTipText = ref('')
  const regionTipDis = ref('')
  const regionTipFn = () => {
    tipVisible.value = true
    tipText.value = '用户所在地'
    regionTipDis.value = '注册之后不可更改,请谨慎选择'

  }
  const inputFn=(val)=>{
    console.log(val,'vallllllllllllll');


  }
  const agreementFn = () => {

    agreementVisible.value = false;
    agreeProto.value = true;
    if (optionType.value === 1 && loginType.value === 1) {
      checkSM()
    }
  }
  const passwordLogin = () => {
    if (!ma.value.mobile || !ma.value.password) {
      return
    }
    if (!agreeProto.value) {
      agreementVisible.value = true
      return
    }
    dealSmDeviceId(async (deviceId) => {
      setSMDeviceId(deviceId);
      loginAccountV2({
        info: {
          encode: true,
          app: 'RINGKOL',
          region: 'CN',
          platform: 'H5'
        },
        account: {
          account: encrypt(ma.value.mobile),
          password: encrypt(ma.value.password),
        }
      }).then(res => {
        loginSuc(res.data);
        setIframData(res.data,true);

        console.log(res, 'asdasdasdasda');

      }).catch(err => {
        console.log(err.response.data.message, 'asdasdads11');

        errorMsg.value = err.response.data.message
      })
    })




  }
  const changeUseRegion = () => {
    console.log(regionCodeIndex.value, regionCodeList, 'aaaaaaaa')
    changeRegionCode.value = regionCodeList[regionCodeIndex.value]
    descVisible.value = false

  };

  const changeRegionAddress = () => {
    if (regionCodeIndex.value === null) {
      optionType.value = 2
    }
    if (regionCodeIndex.value !== null && changeRegionCode.value.name === '其他区域') {
      optionType.value = 2
    }

  }
  const handleKeyDown = (event) => {
    if (event.key === 'Process' || event.key === 'Unidentified') {
      event.preventDefault(); // 阻止中文输入法的输入
    }
  };
  const jumpStting = () => {
          setIframData(null,true)
    getUserInfo()
  }
  const sttingPasswordFn = () => {
    if (sttingPasswordData.value.sttingPassword.length < 8 || sttingPasswordData.value.sttingPassword1.length < 8) {
      isRed.value = true;
      sttingPasswordText.value = '至少8位字符，包含字母与数字'
      return

    }
    if (sttingPasswordData.value.sttingPassword !== sttingPasswordData.value.sttingPassword1) {
      isRed.value = true;
      sttingPasswordText.value = '密码不一致，请重新输入'
      return
    }
    if (sttingPasswordData.value.sttingPassword === sttingPasswordData.value.sttingPassword1) {
      isRed.value = false;
      sttingPasswordText.value = ''
      updatePassword({ new: encrypt(sttingPasswordData.value.sttingPassword) }).then(res => {
        console.log(res, '设置密码')
        if (res.code === 0) {
          getUserInfo();
          setIframData(null,true)
        }
      })
    }
  }
  const setIframData = (data,flag) => {
    if (iframeStatus.value.isInIframe) {
      // 在 iframe 内部
      if(data){
        window.parent.postMessage({
        type: 'login_success',
        data: {
          loginData: JSON.stringify(data),
        }
      }, 'http://192.168.0.115:3002') // 父页面的域名
      }
      if (flag) {
        window.parent.postMessage({
          type: 'close',
        }, '*') // 父页面的域名
      }
    }
  }

  const handleCompositionStart = () => {
    isComposing.value = true;
  };
  watch(captchaNum, (newVal) => {
    if (newVal.length === 6) {
      dealSmDeviceId(async (deviceId) => {
        console.log('回调执行成功，设备标识为：' + deviceId);
        setSMDeviceId(deviceId);

        loginOrRegisterByMobile({
          mobile: {
            mobile: ma.value.mobile,
            code: newVal,
            password: "",
            region: ma.value.region
          },
          info: {
            app: 'RINGKOL',
            region: 'CN',
            encode: false,
            deviceId,
            paltform: 'H5'
          }
        }).then(res => {
          if (!res.data.hasPassword) {
            optionType.value = 4;
            showKeyboard.value = false;
            setAccesstoken(res.data.jwt);
            setOpenid(res.data.openid)
            setOpenimToken(res.data.openimToken);
            setIframData(res.data);
            // getUserInfo();
          } else {
            loginSuc(res.data)
            setIframData(res.data,true);

          }
          console.log(res, '213强吻')
          // res.data.jwt

        }).catch((err) => {
          const reason = err.response.data.reason;
          showToast(err_reason[reason] || '获取验证码失败');
        });
      })
      // tipVisible.value = true;
      // tipText.value = '验证码错误'
      console.log(newVal, 'newValnewValnewVal');
    } else {
      console.log(newVal, 'newValnewValnewVal11');
    }
  });
  const handleCodeInput = (event) => {
    const input = event.target.value;
    // codeTip.value = '';
    const cleaned = input.replace(/[^a-zA-Z0-9]/g, '');

    let formatted = '';
    if (cleaned.length === 8) {
      // 8位格式: XXXX XXXX
      formatted = cleaned.replace(/(\d{4})(\d{4})/, '$1 $2');
    } else if (cleaned.length === 11) {
      // 11位格式: XXX XXXX XXXX
      formatted = cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
    } else {
      // 其他长度保持原有逻辑，但使用空格分隔
      formatted = cleaned.match(/.{1,4}/g)?.join(' ') || '';
    }

    setTimeout(() => {
      // formattedCode.value = formatted;
      // formData.value.code = cleaned;
      ma.value.mobile = cleaned;
      iphoneCode.value = formatted;
      console.log(formatted, cleaned, 'formattedCodeformattedCode');
    });

  };
  const handleCompositionEnd = () => {
    isComposing.value = false;
  };
  const checkSM = () => {
    if (!SMCaptcha.value) {
      return;
    }
    getSMCaptchaResult(SMCaptcha.value, codeSussess);
  };
  const passwordBack = () => {
    loginType.value = 1;
  }




  const detectIframeStatus = () => {
    const status = {
      isInIframe: false,
      canAccessParent: false,
      isSameOrigin: false
    }

    try {
      // 基本检查
      status.isInIframe = window.self !== window.top

      if (status.isInIframe) {
        try {
          // 尝试访问父窗口，检查是否同源
          const parentOrigin = window.parent.location.origin
          status.canAccessParent = true
          status.isSameOrigin = parentOrigin === window.location.origin
        } catch (e) {
          // 跨域情况
          status.canAccessParent = false
          status.isSameOrigin = false
        }
      }
    } catch (e) {
      status.isInIframe = true // 如果出错，很可能在跨域 iframe 中
    }
    return status
  }
  const iframeStatus = ref(null)
  onMounted(async () => {
    iframeStatus.value = detectIframeStatus()


    let res = await listAreaCodes();
    console.log(res, '啊实打实大师大多');
    regionList.value = res.data.areaCodes;
    try {

      SMCaptcha.value = await getSMCaptcha({ width: 300 });
      console.error(SMCaptcha.value);
    } catch (error) {
      console.error(error);
    }
    initSM();
    removeOpenid();
    removeSMDeviceId();
    useLang.value = getLang() ? options.value.find((v) => v.value === getLang()).content : '简体中文';
    options.value.forEach((v) => (v.active = v.value === (getLang() ?? 'zh-cn')));
    setLanguage(getLang() ?? 'zh-cn');
  });


  const getLanguage = () => {
    return navigator.language || navigator.userLanguage;
  }
  let language = getLanguage();

  const changeLanguage = (data) => {
    if (language === 'zh-TW' || language === 'zh-HK' || language === 'zh-MO') {
      setLanguage('zh-hant-MO');
      setLang('zh-hant-MO');
    } else {
      setLanguage('zh-hans-CN');
      setLang('zh-hans-CN');
    }


  };
  changeLanguage();
  const changeTab = (type: number) => {
    loginType.value = type;
    countdown.value = -2;
    checkLoginDisabled();
    timer.value && (clearInterval(timer.value), (timer.value = null));
  };

  const changeRegionFn = (item) => {
    console.log(item, 'itemmmmmmmm');
    changeRegionCode.value = item
    optionType.value = 1
  }
  const codeSussess = (data) => {
    ma.value = handleTrim(ma.value);
    if (!checkPhone()) return;
    const params = {
      mobile: { mobile: ma.value.mobile, region: changeRegionCode?.value.code },
      typ: 'LOGIN',
    };
    if (data) {
      params.captcha = {
        code: data?.rid,
        mode: 'slide',
      };
    }
    getIdentifyCode(params)
      .then((res: any) => {
        console.log(res, 'getIdentifyCode');
        countdown.value = 60;
        optionType.value = 3;
        timer.value = setInterval(() => {
          countdown.value--;
          if (countdown.value <= 0) {
            clearInterval(timer.value);
            timer.value = null;
          }
        }, 1000);
      })
      .catch((err) => {
        const reason = err.response.data.reason;
        MessagePlugin.error({
          content: err_reason[reason] || '获取验证码失败',
          duration: 3000,
        });
      });
  }
  const getCode = (data?) => {
    if (!checkPhone()) return;
    if (!agreeProto.value) {
      agreementVisible.value = true
      return
    }

    checkSM()


  };

  const checkPhone = () => {
    checkRegion = checkPhoneAndMatch(Number(changeRegionCode?.value.code), ma.value.mobile);
    if (!checkRegion) {
      MessagePlugin.error({
        content: t('zx.account.phoneIllegal'),
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  const loginSuc = (jwtParams: any) => {
    setAccesstoken(jwtParams.jwt);
    setOpenid(jwtParams.openid);
    getUserInfo();
  };



  const getUserInfo = () => {
    getProfile()
      .then((res: any) => {
        accountStore.setUserInfo(res);
        console.log('hah', res);
        localStorage.setItem('profile', JSON.stringify(res));
        localStorage.setItem('userInfo', JSON.stringify(res));
        if (redirect) {
          if (sourceParams) {
            router.push({ path: decodeURIComponent(redirect), query: { link: sourceParams } });
          } else {
            router.push(decodeURIComponent(redirect));
          }
          return;
        }
        router.push({ name: 'accountJoin' });
      })

  };




  const checkLoginDisabled = () => {
    if (loginType.value === 1 && ma.value.code && ma.value.mobile) {
      loginDisabled.value = false;
    } else if (loginType.value === 2 && ma.value.password && ma.value.mobile) {
      loginDisabled.value = false;
    } else {
      loginDisabled.value = true;
    }
  };

</script>

<style lang="less" scoped>
  @import url('../css/base.less');

  .stting-password-text {
    width: 100%;
    margin-top: 4px;
    color: #D54941;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .tip-box {
    width: 280px;
    padding: 24px 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .tip-text {
    width: 170px;
    color: #1A2139;
    text-align: center;
    font-size: 17px;
    font-style: normal;
    font-weight: 600;
  }

  :deep(.van-password-input) {
    margin: 0;
    border-color: transparent;
    box-shadow: none;
  }

  :deep(.van-hairline--surround) {
    gap: 8px;
    border-color: transparent;
    box-shadow: none;
  }

  :deep([class*=van-hairline]:after) {
    border: none !important;
  }

  :deep(.van-password-input__item) {
    border-radius: 8px;
    width: 44px;
    height: 44px;
    border-color: transparent;
    box-shadow: none;
    flex: none;
  }

  .jump-stting-box {
    text-align: center;
    color: #1A2139;
  }

  .isRed {
    color: #D54941;
  }

  .is-ok-stting-box {
    border-radius: 4px;
    background: #4D5EFF;
    color: #FFF;
    text-align: center;
    margin-top: 32px;
    margin-bottom: 13px;
    height: 40px;
    font-size: 17px;
    width: 100%;
    font-weight: 600;
    line-height: 40px;
  }

  .witcode {
    color: #FFF;
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
    /* 133.333% */
    margin: 32px 8px 16px;
  }

  .upd-code {
    background-image: url('@/assets/updcode.png'), linear-gradient(193deg, #5832D9 19.27%, #4B2ABA 58.96%) !important;
    background-position: right top, center !important;
    background-repeat: no-repeat, no-repeat !important;
    background-size: 100% !important;
    height: 100%;
    padding: 10px 16px;


  }

  .codenum-box {
    background: rgba(255, 255, 255, 0.88);
    border-radius: 16px;
    padding: 24px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    .code-to {
      color: #516082;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px;
      width: 100%;
      text-align: center;
      margin-bottom: 4px;
    }

    .code-phone {
      width: 100%;
      text-align: center;
      margin: 4px 0 16px;
    }

    .agin {
      color: #4D5EFF;
      text-align: right;
      font-size: 15px;
      margin-top: 10px;
      margin-right: 10px;
      font-weight: 400;
      line-height: 24px;
      width: 100%;

    }
  }

  .agreement-foot {
    display: flex;
    gap: 12px;
    margin-top: 24px;

    .isok {
      background: #4D5EFF !important;
      color: #fff !important;
    }

    .btn {
      width: 118px;
      height: 40px;
      border-radius: 4px;
      border: 1px solid #D5DBE4;
      background: #FFF;
      color: #516082;
      text-align: center;
      font-size: 17px;
      font-weight: 600;
      line-height: 40px;
      /* 152.941% */
    }
  }

  .agreement-box {
    display: flex;
    width: 280px;
    padding: 24px 16px;
    flex-wrap: wrap;
    justify-content: center;

    .agreement-text {
      color: #1A2139;
      text-align: center;
      font-size: 17px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
      margin-bottom: 12px;
    }

    .ckd-agreement {
      color: #516082;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }

  .btn {
    height: 50px;
    color: #4D5EFF;
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 50px;
    text-align: center;
  }

  .title {
    height: 58px;
    line-height: 58px;
    color: #1A2139;
    text-align: center;
    font-size: 17px;
    font-weight: 600;
    border-bottom: 1px solid #ECEFF5;
  }

  .desc-region-item {
    display: flex;
    padding: 12px;
    overflow: hidden;
    color: #1A2139;
    font-size: 17px;
    font-style: normal;
    align-items: center;
    justify-content: space-between;
    font-weight: 400;
    line-height: 26px;
    border-bottom: 1px solid #ECEFF5;
  }

  .region-code {
    color: #828DA5;
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
  }

  .region-name {
    color: #1A2139;
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
  }

  .region-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #ECEFF5;
  }

  .change-region {
    height: 100%;
    background: #fff;
    padding: 9px 16px 0px;
  }

  .region-list {
    margin-top: 9px;
    overflow: overlay;
    height: calc(100% - 40px);
  }

  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .error-msg {
    color: #D54941;
    font-size: 14px;
    margin-top: 4px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .password-input {
    padding: 12px 16px;
    border-radius: 8px;
    background: #FFF;
  }

  .login-tab-password {
    padding: 10px 16px;
  }

  .region-iphone-code {
    display: flex;
    align-items: center;
  }

  :deep(.t-checkbox__input) {
    border-radius: 50%;
  }

  :deep(.t-input) {
    border-bottom: none !important;
    padding: 0 !important;
  }

  .iphone-code {
    color: #1A2139;
    line-height: 18px;
    font-size: 17px;
  }

  .lin {
    width: 1px;
    height: 16px;
    margin: 2px 12px 0;
    background: #D5DBE4;
  }

  .region-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #fff;
    border-radius: 8px;
  }

  .content-item {
    height: auto !important;
    margin-top: 24px !important;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.88);
    backdrop-filter: blur(10px);
    padding: 32px 16px;
  }

  .region-text {
    display: flex;
    align-items: center;

    span {
      color: #1A2139;
      font-size: 17px;
    }
  }

  .tip-dis-text {
    color: #516082;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-top: 12px;
  }

  .login-welcome {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 0 8px;
    color: #fff;
    margin-top: 164px;

    .wel-text {
      font-size: 28px;
      font-style: normal;
      font-weight: 600;
      line-height: 36px;
    }

    .wel-text1 {
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
  }

  .disabled {
    background: #C9CFFF !important;
  }

  .container-pc {
    background-image: url('@/assets/lkloginbg.png'), linear-gradient(193deg, #5832D9 19.27%, #4B2ABA 58.96%) !important;
    background-position: right top, center !important;
    background-repeat: no-repeat, no-repeat !important;
    background-size: 100% !important;

  }

  .closeback {
    width: 32px;
    height: 32px;
    position: fixed;
    top: 6px;
    right: 16px;
  }

  :global(.t-dropdown) {
    border: none;
  }

  :global(.t-dropdown .t-dropdown__menu) {
    padding: 8px;
  }

  :global(.t-dropdown .t-dropdown__item) {
    height: 42px;
    padding: 12px;
  }

  :deep(.tel-code-style) {
    &>div {
      width: 100%;
      border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
    }

    .kyy-cell {
      width: 93px !important;
      padding-top: 0;
      padding-bottom: 0;
      padding-left: 8px;
      margin-bottom: 0;

      .van-field__control {
        height: 66px;
        font-size: 15px;
        color: var(--input-kyy_color_input_text_completed, #1a2139);
      }
    }

    .van-bottom {
      width: calc(100% - 93px) !important;
      padding-top: 0;
      padding-bottom: 0;
      margin-bottom: 0;

      .van-field__control {
        height: 66px;
        font-size: 18px;
        color: var(--input-kyy_color_input_text_completed, #1a2139);
      }
    }
  }

  .container {
    min-height: 600px;
    height: 100vh;
    position: relative;
    overflow: hidden;

    :deep(.t-input) {
      box-shadow: none;
    }
  }

  .mt12 {
    margin-top: 12px;
  }

  .f-c {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .pointer {
    cursor: pointer;
  }

  .logo {
    margin-top: 24px;
    text-align: center;

    img {
      width: 80px;
      height: 80px;
      vertical-align: bottom;
    }
  }


  .login-tab-content {
    margin: auto;
    padding: 0 16px;
    margin-top: 24px;

    .content-item {
      height: 132px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;


      :deep(.t-input) {
        font-size: 17px !important;
      }

      :deep(.t-input-adornment__prepend) {
        background: #fff;
      }

      :deep(.codeBox .t-input__clear) {
        position: absolute;
        right: 120px;
      }
    }

    .login-btn {
      margin-top: 12px;
      height: 40px;
      font-size: 16px;
      font-family: MicrosoftYaHei, MicrosoftYaHei-MicrosoftYaHei;
      color: #ffffff;
      line-height: 24px;
      border-radius: 4px;
    }
  }

  :deep(.t-input) {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
</style>
