# RTabs

tab 组件

```jsx
import { ref } from 'vue';
import { RTabs } from '@rk/unitPark';
import { TabItemType, TabsType } from './type';
import './style.css';
import 'tdesign-vue-next/es/style/index.css';

const tabs = ref({
  attrs: {},
  defaultInfo: {
    value: '3',
  },
  list: [
    {
      label: 'tab1111',
      value: '1',
    },
    {
      label: 'tab222222',
      value: '2',
    },
    {
      label: 'tab3333 (3)',
      value: '3',
    },
  ],
});
const tabsChange = (info) => {
  console.log('tabsChange', info);
};

setTimeout(() => {
  tabs.value.defaultInfo.value = '2';
}, 2000);

export default () => (
  <>
    <RTabs
      defaultInfo={tabs.value?.defaultInfo}
      list={tabs.value?.list}
      attrs={tabs.value?.attrs}
      onChange={tabsChange}
    />
  </>
);
```

### Props

<API id="RTabs" type="props"></API>

### Events

<API id="RTabs" type="events"></API>
