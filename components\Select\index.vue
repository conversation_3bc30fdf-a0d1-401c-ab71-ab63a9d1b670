<template>
  <div :class="{
    'RK-Select': true,
    'themecolor': !!dClass,
    [dClass]: !!dClass
  }">
    <TSelect
      class="rk-select"
      v-bind="props?.attrs"
      v-model="selectVal"
      @change="onChange"
      :options="list"
      autoWidth
    ></TSelect>
  </div>
</template>

<script setup lang="ts" name="RSelect">
import { onMounted, ref, watch } from 'vue';
import { Select as TSelect } from 'tdesign-vue-next';
import type { ItemType, NameEnum } from './type'


const props = defineProps<{
  list: ItemType[];
  defaultInfo?: { value: string };
  attrs?: Record<string, any>;
}>();
const emit = defineEmits(['change']);
const list = ref(props.list);
const selectVal = ref(props.defaultInfo?.value || '');
const dClass = ref('');
watch(() => props.list, (newVal) => {
  list.value = newVal;
});


// 监听外部 defaultInfo.value 的变化，动态更新 selectVal
watch(
  () => props.defaultInfo,
  (newVal) => {
    if (newVal && typeof newVal.value !== 'undefined') {
      if (newVal.value !== selectVal.value) {
        setInfo({ value: newVal.value });
      }
    }
  }, {
    deep: true,
    immediate: true
  }
);


const setInfo = (option:any) => {
  let name = option.name || '';
  if (!name) {
    list.value.map((item, k) => {
      if (item.value === option.value) {
        name = item?.name
      }
    })
  }
  dClass.value = name;
  selectVal.value = option.value;
}

const onChange = (val: any, item: any) => {
  setInfo({ value: val, name: item.option.name })
  emit('change', { value: val , option: item.option });
};

onMounted(() => {
  const val = props?.defaultInfo?.value
  // 初始化
  if (val) {
    setInfo({
      value: val
    })
  }
})
</script>

<style lang="less" scoped>
@import (reference) '../../style/commonComp.less';
:deep(.t-popup){
  &.t-select__dropdown {
    .t-select__dropdown-inner {
      .t-select__list {
        padding: 4px;
      }
    }
  }
}
.RK-Select{
  margin-right: 10px;
  display: inline-block;
  border-radius: 4px;
  color: #516082;
  background-color: #eceff5;
  .t-select__wrap {
    line-height: 15px;
    height: 20px;
  }
  .theme-color();
  // &.themecolor {
    :deep(.t-select) {
      color: inherit; 
      .t-input{
        background-color: inherit;
        color: inherit;
        height: 20px;
        line-height: 20px;
        padding-left: 6px;
        border-color: var(--td-bg-color-container);
        &:hover {
          border-color: var(--td-bg-color-container)!important;
        }
        &:focus,
        &.t-input--focused {
          box-shadow: none;
        }
        .t-input__inner {
          color: inherit;
          font-size: 12px;
          &::placeholder {
            color: inherit; 
            font-size: 12px;
          }
        }
        .t-input__suffix {
          margin-left: 0;
            > svg {
              display: none;
            }
           &::before {
            content: '';
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid currentColor;
            display: inline-block;
            transition: transform 0.3s;
          }
        }
      }
      &.t-select-input--popup-visible {
        .t-input__suffix {
          &::before {
            transform: rotate(180deg);
          }
        }
      }
    }
  //}
}
</style>
