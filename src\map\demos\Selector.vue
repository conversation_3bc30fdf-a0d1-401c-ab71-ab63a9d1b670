/**
 * title: 百度地图选择器
 * description: 选择地图位置
 */

<template>
  <div>
    <RkBaiduMapSelector
      v-if="mapVisible"
      v-model:visible="mapVisible"
      :loc="location"
      @confirm="locationConfirm"
    />

    <t-button @click="mapVisible = true">
      选择地图
    </t-button>

    <t-link
      v-if="address"
      theme="primary"
      class="reset-btn"
      @click="address = ''"
    >
      重置
    </t-link>

    <div
      v-if="address"
      class="location-result"
    >
      <pre>
        {{ addressStr }}
      </pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button as TButton, Link as TLink } from 'tdesign-vue-next';
import { RkBaiduMapSelector } from '@rk/unitPark';
import { computed, ref } from 'vue';

const mapVisible = ref(false);
const address = ref();
const addressStr = computed(() => JSON.stringify(address.value, null, '  '));

const location = computed(() => {
  if (!address.value?.location) return null;
  const { lat: latitude, lng: longitude } = address.value.location;
  return { latLng: { longitude, latitude } }
});

const locationConfirm = (result: any) => {
  address.value = result;
  mapVisible.value = false;
};
</script>

<style scoped>
.location-result {
  margin-top: 16px;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.reset-btn {
  margin-left: 16px;
}
</style>