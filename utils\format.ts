/**
 * 文本高亮
 * @param text 原始文本
 * @param search 高亮文本
 * @param style 高亮样式
 * @example
 * <pre><div class="line-1" v-html="highlight(item.name, keyword)" /></pre>
 */
export const highlight = (
  text: string,
  search: string,
  style = 'color:#4D5EFF',
) => {
  if (!text) return '';
  if (!search) return text;

  const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  return text
    .replace(/\s/g, '&nbsp;')
    .replace(
      new RegExp(`(${escapedSearch})`, 'gi'),
      `<span style="${style}">$1</span>`,
    );
};
