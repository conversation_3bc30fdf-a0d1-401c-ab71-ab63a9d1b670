import { IpcResponse, WindowOptions } from '../types';
import { BaseModule } from './base';

export class WindowModule extends BaseModule {
  private static readonly DEFAULT_WINDOW_CONFIG = {
    titleBarStyle: 'hidden',
    width: 728,
    height: 720,
    resizable: false,
    movable: true,
    useContentSize: true,
    autoHideMenuBar: true,
    frame: false,
    show: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false,
    },
  } as const;

  public async closeWindow(): Promise<void> {
    if (this.isElectron() && window.LynkerSDK?.closeWindow) {
      await window.LynkerSDK.closeWindow();
      return;
    }
    await this.send('closeWindow');
  }

  public async openWindow(options: WindowOptions): Promise<IpcResponse> {
    try {
      if (this.isElectron() && window.LynkerSDK?.openNewWindow) {
        const result = await window.LynkerSDK.openNewWindow({
          ...options,
          browserWindow: {
            ...WindowModule.DEFAULT_WINDOW_CONFIG,
            ...options.browserWindow,
          },
        });
        return { code: 0, data: result, message: 'success' };
      }
      return this.send('openWindow', options);
    } catch (error) {
      return {
        code: -1,
        data: null,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public windowControl(
    action: 'minimize' | 'maximize' | 'restore',
  ): Promise<IpcResponse> {
    return this.send(`${action}Window`);
  }
}
