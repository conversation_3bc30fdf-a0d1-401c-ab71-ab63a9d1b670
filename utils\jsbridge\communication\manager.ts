/**
 * @file 通信管理器
 */

import { getEnv } from '../base';
import { IpcResponse, MessageData } from '../types';
import { ElectronCommunicator } from './electron';
import { WebCommunicator } from './web';
type MessageHandler = (messageData: MessageData) => Promise<void>;
export class CommunicationManager {
  private readonly electronCommunicator: ElectronCommunicator;
  private readonly webCommunicator: WebCommunicator;
  private messageHandlers: Set<MessageHandler> = new Set();

  constructor() {
    this.electronCommunicator = new ElectronCommunicator();
    this.webCommunicator = new WebCommunicator();
  }

  /**
   * 获取 SDK 实例
   */
  public sdk<T = any>(data?: any): T {
    return this.electronCommunicator.sdk(data);
  }
  /**
   * 设置消息处理器
   */
  public onMessage(handler: MessageHandler): void {
    if (handler === null) {
      this.messageHandlers.clear();
      if (getEnv().isRingkolDesktopApp) {
        this.electronCommunicator.onMessage(null);
      } else {
        this.webCommunicator.onMessage(null);
      }
      return;
    }
    this.messageHandlers.add(handler);

    const messageListener = async (messageData: MessageData) => {
      if (!messageData?.action) return;
      try {
        await handler(messageData);
      } catch (error) {
        console.error('[CommunicationManager] Handler error:', error);
      }
    };

    if (getEnv().isRingkolDesktopApp) {
      this.electronCommunicator.onMessage(messageListener);
    } else {
      this.webCommunicator.onMessage(messageListener);
    }
  }

  public removeMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.delete(handler);
  }

  /**
   * 统一的消息发送方法
   */
  public async send<T = any>(
    action: string,
    data?: any,
    target?: Window | HTMLIFrameElement,
  ): Promise<IpcResponse<T>> {
    const messageData: MessageData = {
      type: this.determineMessageType(target),
      action,
      data,
      callbackId: this.generateCallbackId(),
      source: window.name || 'unknown',
      target: target instanceof Window ? target : undefined,
    };

    try {
      if (target) {
        // 1. 优先处理指定目标的情况
        if (target instanceof HTMLIFrameElement && target.contentWindow) {
          return await this.webCommunicator.send<T>(messageData, {
            window: target.contentWindow,
            origin: '*',
          });
        }

        if (target instanceof Window) {
          return await this.webCommunicator.send<T>(messageData, {
            window: target,
            origin: '*',
          });
        }
      }

      if (getEnv().isRingkolDesktopApp) {
        return await this.electronCommunicator.send<T>(messageData);
      }

      // 3. 默认 Web 环境处理
      return await this.webCommunicator.send<T>(messageData);
    } catch (error) {
      console.error('CommunicationManager: Error sending message:', error);
      return {
        code: -1,
        data: null as T,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private determineMessageType(
    target?: Window | HTMLIFrameElement,
  ): MessageType {
    if (getEnv().isRingkolDesktopApp) return 'electron';
    if (!target) return 'child-to-parent';
    if (target instanceof HTMLIFrameElement) return 'iframe';
    return 'parent-to-child';
  }

  private generateCallbackId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
