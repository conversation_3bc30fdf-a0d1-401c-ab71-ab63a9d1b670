// TODO 此文件即将废弃，请使用 ./local.ts

// 保存openid到本地
export const saveOpenid = (openid: string) => {
  localStorage.setItem('openid', openid);
};

// 获取openid
export const getOpenid = () => {
  return localStorage.getItem('openid') || '';
};

// 保存组织id
export const saveTeamid = (teamid: string) => {
  localStorage.setItem('teamId', teamid);
};

// 获取组织id
export const getTeamid = () => {
  return localStorage.getItem('teamId') || '';
};

// 保存header
export const saveHeader = (header: string) => {
  localStorage.setItem('header', header);
};

// 获取header
export const getHeader = () => {
  return localStorage.getItem('header') || '';
};
