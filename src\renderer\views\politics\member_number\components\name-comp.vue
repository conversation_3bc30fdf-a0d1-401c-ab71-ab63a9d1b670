<template>
  <!-- <t-loading
    size="medium"
    class="memberLoading"

    :loading="isLoading"
    show-overlay
    text="加载中..."
  > -->
  <div class="page">
    <div class="change-box">
      <img class="logo" src="@renderer/assets/member/photo.svg" />
      <template v-if="userItem?.staffId">
        <t-popup overlay-class-name="change-box-popup" placement="bottom-right" :z-index="1000" destroy-on-close>
          <template #content>
            <div class="list-box">
              <template v-for="item in userList" :key="item.staffId">
                <!-- <div class="item" :class="item.staffId === userItem?.staffId ? 'item-active' : ''" @click="changeUser(item)"> -->
                <div
                  class="item"
                  :class="item.staffId === userItem?.staffId ? 'item-active' : ''"
                  @click="changeUser(item)"
                >
                  <div class="item-av">
                    <avatar avatar-size="24px" :image-url="item.avatar" :user-name="item.name" shape="circle"></avatar>
                  </div>
                  <div class="item-name">
                    <t-tooltip :content="item.name">
                      {{ item.name }}
                    </t-tooltip>
                  </div>
                  <div class="item-tag">{{ item.type === 1 ? "员工" : "平台" }}</div>
                  <!-- <div class="item-tag">{{ item.flag === 'staff' ? '员工' : '平台' }}</div> -->
                </div>
              </template>
            </div>
          </template>
          <div class="show-item">
            <div class="item-av">
              <avatar
                avatar-size="24px"
                :image-url="userItem.avatar"
                :user-name="userItem.name"
                shape="circle"
              ></avatar>
            </div>
            <div class="item-name">{{ userItem.name }}</div>
            <!-- <div class="item-tag">{{ userItem.flag === 'staff' ? '员工' : '平台' }}</div> -->
            <div class="item-tag">{{ userItem.type === 1 ? "员工" : "平台" }}</div>
            <div class="icon">
              <iconpark-icon style="font-size: 20px" name="iconarrowdown" class="iconarrowdown"></iconpark-icon>
            </div>
          </div>
        </t-popup>
      </template>
      <template v-else>
        <div class="show-item">
          <div class="item-tag"></div>
        </div>
      </template>
    </div>
    <div class="square">
      <div class="search">
        <div class="search-box">
          <div class="titleName">
            <span class="name">
              名录<span class="count">（{{ contactCount }}位）</span>
            </span>
          </div>
          <div class="in-box">
            <t-input
              v-model="keyword"
              :placeholder="$t('member.second.n')"
              :maxlength="50"
              clearable
              style="width: 304px"
              @change="onSearch"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
              </template>
            </t-input>
            <div :class="{ 'f-icon': true, factive: paramsSuper }" @click="showFilter">
              <iconpark-icon name="iconscreen" class="iconscreen"></iconpark-icon>
            </div>
          </div>
        </div>
        <div v-if="paramsSuper" class="filter-res">
          <div class="tit">{{ t("approval.approval_data.sures") }}</div>

          <div v-if="formData.type" class="stat te">
            <span> 类型： {{ formData.type_text }}</span>
            <span class="close2" @click="clearType">
              <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
            </span>
          </div>
          <!-- <div v-if="formData.level" class="ov-time te">
            <span class="line-1 limit">{{ t("member.svip.member_level") }}：
              {{ formData.level_text }}</span>
            <span class="close2" @click="clearlevel">
              <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
            </span>
          </div> -->
          <div v-if="formData.label_value_id_platform" class="ov-time te">
            <span>平台标签： {{ formData.label_value_id_platform_text }}</span>
            <span class="close2" @click="clearPlatform">
              <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
            </span>
          </div>
          <div v-if="formData.group" class="ov-time te">
            <span>分组： {{ formData.group_text }}</span>
            <span class="close2" @click="clearGroup">
              <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
            </span>
          </div>
          <div v-if="formData.activate" class="ov-time te">
            <span>{{ t("member.regular.active_status") }}： {{ formData.activate_text }}</span>
            <span class="close2" @click="clearActivate">
              <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
            </span>
          </div>
          <div v-if="formData.connect" class="ov-time te">
            <span>连接状态： {{ formData.connect_text }}</span>
            <span class="close2" @click="clearConnect">
              <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
            </span>
          </div>
          <div class="icon" style="height: 26px; padding: 2px 0" @click="clearFilters">
            <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
            <a>{{ t("approval.approval_data.clearFilters") }}</a>
          </div>
        </div>
      </div>

      <div
        class="scroll"
        :class="{ scrollTwoHeight: paramsSuper }"
        v-infinite-scroll="handleInfiniteOnLoad"
        :infinite-scroll-immediate-check="false"
        :infinite-scroll-disabled="scrollDisabled"
        infinite-scroll-watch-disabled="scrollDisabled"
        :infinite-scroll-distance="20"
      >
        <div
          class="group"
          v-if="memberLevels.length > 0"
          v-for="memberGroup in memberLevels"
          :key="memberGroup.group_id"
        >
          <div class="banner" v-if="!(memberLevels.length === 1 && memberGroup.group_id === 0)">
            <div class="group-title">
              <span class="title">{{ memberGroup.group_name }}</span>
              <span class="num">（{{ memberGroup.government_list.length }}位）</span>
            </div>
            <t-link
              class="group-more"
              v-show="memberGroup.government_list?.length > 8 && !paramsSuper && !keyword"
              hover="color"
              theme="primary"
              @click="onGroupDetail(memberGroup)"
              >查看更多</t-link
            >
          </div>
          <div class="conbox">
            <div class="conbox-row">
              <div class="nlist">
                <span
                  v-for="(box, boxIndex) in memberGroup.government_list?.slice(
                    0,
                    paramsSuper || keyword
                      ? memberGroup.government_list?.length
                      : memberLevels.length === 1 && memberGroup.group_id === 0
                      ? memberGroup.government_list?.length
                      : 8,
                  )"
                  :key="box?.id_staff + '.' + box?.id"
                  class="box cursor"
                  @click="onLookNameDetail(box)"
                >
                  <!-- @click="viewImg(box.avatar)" -->
                  <!-- fit="cover" -->
                  <div class="imageLeft">
                    <t-image :src="box.avatar ? getSrcThumbnail(box.avatar): ''"  class="image">
                      <template #loading>
                        <img class="image" src="@renderer/assets/member/svg/innerDefault.svg" alt="default-avatar" />
                      </template>
                      <template #error>
                        <img class="image" src="@renderer/assets/member/svg/innerDefault.svg" alt="default-avatar" />
                      </template>
                    </t-image>
                    <span v-show="box.type === 2" class="per">个人</span>
                    <span  v-show="box.type === 1" class="uni">组织</span>
                  </div>
                  <!-- <img :src="box.avatar || PERSON_DEFAULT_AVATAR" class="image"  /> -->
                  <!-- @click="onCard(box)" -->
                  <span class="text">
                    <span class="top">
                      <span class="top-left">
                        <span class="name line-1">{{ box.name }}</span>
                         <!-- <t-tooltip :content="$t('member.winter_column.activeOptions_1')" :show-arrow="false">
                            <span class="boxname">
                              <iconpark-icon
                                v-show="box.activate === 1"
                                name="activeicon"
                                class="certify"
                              ></iconpark-icon>
                            </span>
                          </t-tooltip> -->
                        <t-link
                          v-show="box.self && !box.is_connect && box.type === 1 && box.activate === 1"
                          theme="primary"
                          hover="color"
                          class="linkPlatform"
                          @click.stop="onConnectPlatform(box)"
                          >连接平台</t-link
                        >
                      </span>
                      <!-- && box.status === 0  -->
                      <!-- <span v-show="box.activate ===1 " class="top-right">

                        <span class="job line-1">{{ box.member_job }}</span>
                      </span> -->
                      <!-- {{box?.label_relation?.can_edit}} -->
                      <span class="tag">
                        <!-- 平台标签 -->
                        <span  class="tag-member line-1 max-w-80px"
                          v-for="(pItem, pIndex) in box?.label_relation?.platform?.value" :key="pIndex"
                          :style="{'background-color': switchColor(pItem?.colour)?.bgColor, 'color': switchColor(pItem?.colour)?.color }"
                          >
                          {{pItem?.name}}
                        </span>

                        <span v-for="(perItem, perIndex) in box?.label_relation?.personal?.value" :key="pIndex" class="tag-member line-1 max-w-80px" :style="{'background-color': switchColor(perItem?.colour)?.bgColor, 'color': switchColor(perItem?.colour)?.color }">
                          {{perItem?.name}}
                        </span>

                        <template v-if="box?.label_relation?.personal?.can_edit || box?.label_relation?.platform?.can_edit">
                          <span class="setTag"  @click.stop="onSetTag(box)">设置标签</span>
                        </template>

                      </span>
                      <span v-show="box.type === 1 && box.activate === 1" class="top-right">
                        <span class="job line-1"> {{ box.team_name }}</span>
                      </span>
                      <span v-show="box?.label_relation?.title?.can_edit" class="tag"  >
                        <SetTitlePopup :origin="originType.Politics" :orData="box" :teamId="currentTeamId" :isHandleSave="true" @onRefresh="onSearch" @onSaveTitle="onSaveTitle">
                          <span class="setTag" @click.stop>设置头衔</span>
                        </SetTitlePopup>
                      </span>



                      <div class="touxian" v-show="box?.label_relation?.title?.value?.length > 0">
                        <span v-for="(kerItem, kerIndex) in box?.label_relation?.title?.value?.slice(0, box.type === 1? 2: 3)" :key="kerIndex" class="touxian-item line-1">
                          {{kerItem?.name}}
                        </span>

                      </div>
                      <!-- <span v-show="box.type === 2" class="top-right">
                        <span class="job line-1">个人</span>
                      </span>
                      <span v-show="box.type === 1 && box.activate === 1" class="top-right">
                        <span class="job line-1"> {{ box.team_name }}</span>
                      </span> -->
                    </span>
                    <!-- <span class="bottom">
                      <span class="card">
                        <span v-show="box.activate === 1" class="idcardbox" @click.stop="onCard(box)">
                          <t-tooltip :content="$t('member.bing.i')" :show-arrow="false">
                            <img src="@renderer/assets/member/svg/idCard.svg" />
                          </t-tooltip>
                        </span>
                        <t-link
                          v-show="box.self && !box.is_connect && box.type === 1 && box.activate === 1"
                          theme="primary"
                          hover="color"
                          @click.stop="onConnectPlatform(box)"
                          >连接平台</t-link
                        >
                      </span>
                    </span> -->
                    <!-- <span v-show="box.type === 2" class="bottom line-1">个人</span>
                    <span v-show="box.type === 1" class="bottom line-1">{{ box.team_name }}</span> -->
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isLoading" class="text-center w-full">
          <t-loading v-if="isLoading" :text="$t('components.infiniteLoading.loading')" size="small" />
        </div>
        <!-- <template v-if="!"> -->
        <div v-if=" !isNetworkError && memberLevels.length < 1 && !isLoading" class="empty">
          <Empty v-if="!isLoading" :tip="'暂无名录'" :name="'no-friend-list'" />
        </div>
        <!-- fdsfsdfsd{{  isLoading}} -->
        <!-- </template> -->
        <template v-else>
          <div v-if="memberLevels.length < 1 && !isLoading" class="empty">
            <Empty name="offline">
              <template #tip>
                <div class="tipEmpty">
                  <span class="text">网络链接失败，请检查网络后重试</span>
                  <t-button theme="primary" class="btn" @click="onReload">点击重试</t-button>
                </div>
              </template>
            </Empty>
          </div>
        </template>
      </div>
    </div>
  </div>
  <!-- </t-loading> -->

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    class="drawerSetForm"
    :header="$t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">类型</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.type"
            clearable
            :options="typeOptions"
            style="width: 422px"
            @change="typeChange"
          >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
      <div class="fitem" v-if="platformOptions?.length > 0 && labelSetting?.platform?.enable">
        <div class="title">
          平台标签
          <template v-if="labelSetting?.platform?.name">
            （{{labelSetting.platform.name}}）
          </template>

        </div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.label_value_id_platform"
            :options="platformOptions"
            clearable
            :popupProps="{
              overlayInnerStyle: {
                width: '420px',
              },
            }"
            :keys="{ label: 'name', value: 'value_id' }"
            style="width: 422px"
            @change="platformOptionsChange"
          >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>


      <!-- <div class="fitem">
        <div class="title">{{ t("member.svip.member_level") }}</div>
        <div class="ctl">
          <t-select v-replace-svg
            v-model="paramsTemp.level"
            :options="levelOptions"
            clearable
            style="width: 422px"
            @change="levelChange"
          />
        </div>
      </div> -->

      <!-- <div class="fitem">
        <div class="title">{{ $t("member.regular.active_status") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.activate"
            :options="activeOptions"
            clearable
            style="width: 422px"
            @change="activeOptionsChange"
          >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div> -->

      <div class="fitem">
        <div class="title">连接状态</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.connect"
            :options="connectOptions"
            clearable
            style="width: 422px"
            @change="connectOptionsChange"
          >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
      <div class="fitem" v-if="groupOptions?.length > 0">
        <div class="title">分组</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.group"
            :options="groupOptions"
            clearable
            :popupProps="{
              overlayInnerStyle: {
                width: '420px',
              },
            }"
            :keys="{ label: 'group_name', value: 'id' }"
            style="width: 422px"
            @change="groupOptionsChange"
          >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ $t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ $t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ $t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>
  <NameDetailModel
    ref="nameDetailModelRef"
    @change-succ="onSearch"
    :origin="originType.Politics"
    @onClose="onCloseDetail"
    @onCard="onCard"
    @onConnectPlatform="onConnectPlatformDetail"
  />
  <AnnualConnect
    ref="annualConnectRef"
    :activeAccount="activeAccount"
    @backType="onBackType"
    @refresh="onRefreshType"
  ></AnnualConnect>
  <SetTagModal ref="setTagModalRef" @onRefresh="onSearch" :origin="originType.Politics" :teamId="currentTeamId"/>
  <Tricks :offset="{ x: '-32', y: '-40' }" uuid="数字城市-名录"  :scene="2"/>
  <!-- <SetTagModal ref="setTagModalRef"/> -->
</template>

<script setup lang="ts">
import { getResponseResult } from "@renderer/utils/myUtils";
import { Ref, computed, onActivated, onBeforeMount, reactive, ref, toRaw, watch } from "vue";
// import {
//   getMemberNameMoreAxios,
//   getMemberNameSearchAxios,
//   getMemberNameDirectAxios,
//   getStaffsPlatform,
// getMemberJobsListAxios,
// getMemberNameDetailAxios,
// } from "@renderer/api/member/api/businessApi";
import {
  getMemberNameMoreAxios,
  getMemberNameSearchAxios,
  getGovernmentStaffOpenid,
  getMemberNameDirectAxios,
  getMemberNameDetailAxios,
  getDirectoryGroupAxios,
  getAllGroupListAxios,
  onGetLabelSettingAxios,
} from "@renderer/api/politics/api/businessApi";
import { originType } from "@renderer/views/digital-platform/utils/constant";
import Empty from "@renderer/components/common/Empty.vue";
import { useMemberStore } from "@renderer/views/member/store/member";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute, useRouter } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { staffOpenid } from "@renderer/api/contacts/api/recent";
import { number } from "echarts";
import avatar from "@/components/kyy-avatar/index.vue";
// import { PERSON_DEFAULT_AVATAR } from "@/views/square/constant";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { useI18n } from "vue-i18n";
import { MessagePlugin } from "tdesign-vue-next";
import NameDetailModel from "@renderer/views/member/member_number/modal/name-detail-modal.vue";
import { to } from "await-to-js";
import { onDisplayToPlatform } from "@renderer/views/digital-platform/utils/auth";
// import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import AnnualConnect from "@renderer/views/digital-platform/components/AnnualConnect.vue";
import { TeamAnnualTypeError } from "@renderer/views/digital-platform/utils/auth";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { getMemberJobsListAxios } from "@renderer/politics/api/businessApi";
import SetTagModal from '@renderer/views/digital-platform/modal/set-tag-modal.vue';
// import SetTagModal from "@renderer/views/politics/member_number/modal/set-tag-modal.vue"
import SetTitlePopup from '@renderer/views/digital-platform/modal/set-title-popup.vue';

import { TagColors, TagType } from '@renderer/views/digital-platform/utils/constant'

import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

// const store = usePoliticsStore();
const nameDetailModelRef = ref(null);

const keyword = ref("");

const isLoadMore = ref(false);
const currentMore = ref(null);

const isLoading = ref(false);
const isNetworkError = ref(false);

const memberList = ref([]);
const page = ref(null);
const memberLevels = ref([]);
const memberLevelsMore = ref([]);

const router = useRouter();
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => props.platform || route.query?.platform);

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
  return getMemberTeamID();
});

const store: any = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore;
  }
  return useMemberStore();
});

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    let user_ids = {};
    if (route.query.user_ids) {
      user_ids = JSON.parse(route.query.user_ids);
    }
    return { ...route.query, user_ids };
  } else {
    return store.activeAccount;
  }
});

const digitalRouter = useRouterHelper("digitalPlatformIndex");
const typeOptions = [
  // { label: "全部", value: 0 },
  // { label: "正常", value: 1 },
  // { label: "单位会员", value: 1 },
  // { label: "个人会员", value: 2 }
  // { label: t("member.winter_column.unit"), value: 1 },
  // { label: t("member.winter_column.person"), value: 2 },
  { label: '组织', value: 1 },
  { label: '个人', value: 2 },
];

const activeOptions = [
  // { label: "全部", value: 0 },
  // { label: "已激活", value: 1 },
  // { label: "未激活", value: 2 }
  { label: t("member.winter_column.activeOptions_1"), value: 1 },
  { label: t("member.winter_column.activeOptions_2"), value: 2 },
];
let groupOptions = ref([]);

let platformOptions = ref([]);

const connectOptions = [
  // { label: "全部", value: 0 },
  // { label: "已激活", value: 1 },
  // { label: "未激活", value: 2 }
  { label: "已连接", value: 1 },
  { label: "未连接", value: 2 },
];

const formData = ref({
  keyword: "", // 会员名称

  // level: undefined, // 会员级别
  type: 0, // 入会类型，1：单位，2：个人
  activate: undefined, // 激活状态，1：已激活，2：未激活
  connect: undefined, // 连接状态，1：已连接，2：未连接
  group: undefined, // 分组
  label_value_id_platform: undefined,  // 平台标签值

  type_text: undefined,
  level_text: undefined,
  connect_text: undefined,
  activate_text: undefined,
  group_text: undefined,
  label_value_id_platform_text: undefined
});
const form = ref(null);
const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    // formData.value.level ||
    formData.value.type ||
    formData.value.activate ||
    formData.value.connect ||
    formData.value.group !== undefined ||
    formData.value.label_value_id_platform !== undefined,
);

const paramsSuperFoot = computed(
  () =>
    // paramsTemp.value.level ||
    paramsTemp.value.type ||
    paramsTemp.value.activate ||
    paramsTemp.value.connect ||
    paramsTemp.value.group !== undefined ||
    paramsTemp.value.label_value_id_platform !== undefined,
);

const getDataRunDr = () => {
  filterVisible.value = false;
  // formData.value.level = paramsTemp.value.level;
  formData.value.type = paramsTemp.value.type;
  formData.value.type_text = paramsTemp.value.type_text;
  formData.value.activate = paramsTemp.value.activate;
  formData.value.activate_text = paramsTemp.value.activate_text;
  // formData.value.level_text = paramsTemp.value.level_text;

  formData.value.connect = paramsTemp.value.connect;
  formData.value.connect_text = paramsTemp.value.connect_text;

  formData.value.group = paramsTemp.value.group;
  formData.value.group_text = paramsTemp.value.group_text;

  formData.value.label_value_id_platform = paramsTemp.value.label_value_id_platform;
  formData.value.label_value_id_platform_text = paramsTemp.value.label_value_id_platform_text;

  onSearch();
};

let listCount = 0;
const handleInfiniteOnLoad = () => {
  console.log("handleInfiniteOnLoad");
  // 异步加载数据等逻辑
  if (scrollDisabled.value) {
    // 数据加载完毕
  } else {
    // 加载数据列表
    // pagination.page++;
    // onGetMemberLevelAxios(true);
  }
};
// 注意scrollDisabled 必须开始的时候就是false, 可以用length > 0 或者loading再渲染

const scrollDisabled = computed(() => {
  console.log(memberLevels.value.length, listCount, memberLevels.value.length >= listCount);
  console.log(listCount, "dd", listCount > 0 ? memberLevels.value.length >= listCount : false);
  return listCount > 0 ? memberLevels.value.length >= listCount : false;
  // return memberLevels.value.length >= listCount ;
});

const switchColor = (intColor)=> {
  return TagColors.find((item) => item.intColor == intColor)
}

const onSaveTitle = (ds) => {
  console.log(ds);
}

const clearFilters = () => {
  // formData.value.level = undefined;
  formData.value.type = undefined;
  formData.value.activate = undefined;
  formData.value.connect = undefined;
  formData.value.group = undefined;
  formData.value.label_value_id_platform = undefined;

  // paramsTemp.value.level = undefined;
  paramsTemp.value.activate = undefined;
  paramsTemp.value.connect = undefined;
  paramsTemp.value.type = undefined;
  paramsTemp.value.group = undefined;
  paramsTemp.value.label_value_id_platform = undefined;

  onSearch();
};

const setTagModalRef = ref(null);
const onSetTag = (box) => {
  setTagModalRef.value.onOpen(box);
}


const onReturnSetTipVal = (label_relation: any) => {
  console.log("returnSetTipVal");
  if(label_relation?.can_edit) { // 可否编辑
    if(label_relation?.personal?.enable && label_relation?.platform?.enable) {
      return label_relation?.personal?.value?.length === 0 || label_relation?.platform?.value?.length === 0;
    } else if(label_relation?.personal?.enable){ // 个人标签是否开启
      return label_relation?.personal?.value?.length === 0
    } else if(label_relation?.platform?.enable) { // 平台标签是否开启
      return label_relation?.platform?.value?.length === 0
    } else {
      return false;
    }
  } else {
    return false;
  }
};


const labelSetting = ref(null);
const onGetLabelSettingInfo = async ()=> {
  return new Promise(async (resolve, reject) => {
    const [err, res] = await to(onGetLabelSettingAxios({}, currentTeamId.value))
    if(err) return reject();
    if(res) {
      const { data }: any = res;
      console.log(data)
      labelSetting.value = data?.data;
      platformOptions.value = labelSetting.value?.platform?.value || [];

      resolve(data?.data)
    } else {
      reject();
    }
  })
}


const showFilter = () => {
  filterVisible.value = true;
  getGroupList().then((arr: any) => {
    console.log(arr);
    // groupOptions.value = arr.concat([{ id: 0, group_name: "未分组" }]);
    console.log(groupOptions.value);

    if(arr && arr.length === 0) {
      groupOptions.value = [];
    } else {
      groupOptions.value = arr.concat([{id: 0, group_name:'未分组'}]);
    }
  });

  onGetLabelSettingInfo();
};
const paramsTemp = ref({
  level: undefined,
  level_text: undefined,

  type: undefined,
  type_text: undefined,

  activate: undefined,
  activate_text: undefined,

  connect: undefined,
  connect_text: undefined,

  group: undefined,
  group_text: undefined,

  // 平台标签值
  label_value_id_platform: undefined,
  label_value_id_platform_text: undefined


});
const typeChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.type_text = ctx.option.label;
};
const levelChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.level_text = ctx.option.label;
};

const activeOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.activate_text = ctx.option.label;
};

const connectOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.connect_text = ctx.option.label;
};

const groupOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.group_text = ctx.option.label;
};

const platformOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.label_value_id_platform_text = ctx.option.label;
};

const clearActivate = () => {
  formData.value.activate = undefined;
  paramsTemp.value.activate = undefined;
  onSearch();
};
const clearConnect = () => {
  formData.value.connect = undefined;
  paramsTemp.value.connect = undefined;
  onSearch();
};
const clearType = () => {
  formData.value.type = undefined;
  paramsTemp.value.type = undefined;
  onSearch();
};
const clearlevel = () => {
  formData.value.level = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};

const clearGroup = () => {
  formData.value.group = undefined;
  paramsTemp.value.group = undefined;
  onSearch();
};

const clearPlatform = () => {
  formData.value.label_value_id_platform = undefined;
  paramsTemp.value.label_value_id_platform = undefined;
  onSearch();
}

const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);

const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.level = undefined;
};

const pagination = {
  pageSize: 20,
  page: 1,
  // total: 0,
};

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios({}, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;

    levelOptions.value = [
      ...result.data.map((v) => ({
        label: v.level_name,
        value: v.id,
      })),
    ];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      MessagePlugin.error(errMsg);
    }
    // MessagePlugin.error(errMsg);
  }
};
const contactCount = ref(0);
const onGetMemberLevelAxios = async (isCombine?) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  // isLoading.value = true;
  // 缓存信息存储
  // const caches = store.value.getStorageDatas || [];
  // const cache = caches.find(v=>v.teamId === currentTeamId.value);
  // if(!cache) {
  //   isLoading.value = true;
  // }
  console.log(activeAccount.value?.user_ids);
  try {
    isLoading.value = true;
    // {...pagination}
    result = await getDirectoryGroupAxios(
      {
        ...toRaw(formData.value),
        keyword: keyword.value,
        group_id: formData.value?.group,
        // ...pagination,
        // card_id: activeAccount.value?.user_ids?.platformStaff
        card_id: userItem.value.cardId,
      },
      currentTeamId.value,
    );
    result = getResponseResult(result);
    setTimeout(() => {
      isLoading.value = false;
    });
    if (!result) {
      return;
    }
    if (isCombine) {
      memberLevels.value = memberLevels.value.concat(result.data?.list || []);
    } else {
      memberLevels.value = result?.data?.list || [];
    }
    contactCount.value = result?.data?.contact_count;
    // listCount = result?.data?.total || 0;
    // 缓存处理 start
    const memberName = {
      items: toRaw(memberLevels.value),
      keyword: toRaw(keyword.value),

      // page: result.page
    };
    // if(cache) {
    //   cache.memberName = memberName
    // } else {
    //   caches.push({teamId: getPoliticsTeamID(), memberName});
    // }
    // store.value.setStorageDatas(caches)
    // 缓存处理 end
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    console.log(errMsg);
    isLoading.value = false;
    if (errMsg === "Network Error") {
      isNetworkError.value = true;

      // 读取缓存
      // memberLevels.value = cache.memberName?.items || [];
      // keyword.value = cache.memberName?.keyword || '';
      return;
    }
  }
  setTimeout(() => {
    isNetworkError.value = false;
    isLoading.value = false;
  });
};

const onGroupDetail = (group) => {
  console.log(group, userItem.value);
  const searchMenu = digitalRouter.routeList.find((v) => v.name.includes("politics_name_detail"));
  // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
  searchMenu.query = {
    ...searchMenu.query,
    platform: platform.digitalPlatform,
    team_id: currentTeamId.value,
    group_id: group?.group_id,
    card_id: userItem.value.cardId,
  };
  console.log(searchMenu.query);
  router.push({ path: searchMenu.fullPath, query: searchMenu.query });
  digitalPlatformStore.addTab({ ...toRaw(searchMenu), title: "查看更多" });
};

const onGetMemberLevelSearchAxios = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  // isNetworkError.value = false;
  // 缓存信息存储
  const caches = store.value.getStorageDatas;
  const cache = caches.find((v) => v.teamId === currentTeamId.value);
  if (!cache) {
    isLoading.value = true;
  }

  try {
    result = await getMemberNameSearchAxios({ keyword: keyword.value });
    result = getResponseResult(result);
    isNetworkError.value = false;
    isLoading.value = false;
    if (!result) {
      return;
    }
    memberList.value = result.data?.member_list;
    // 缓存处理 start
    const memberName = {
      items: toRaw(memberList.value),
      keyword: toRaw(keyword.value),
    };
    if (cache) {
      cache.memberName = memberName;
    } else {
      caches.push({ teamId: currentTeamId.value, memberName });
    }
    console.log("caches: ", caches);
    store.value.setStorageDatas(caches);
    // 缓存处理 end
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    if (errMsg === "Network Error" && cache) {
      isNetworkError.value = true;
      memberList.value = cache.memberName?.items || [];
      keyword.value = cache.memberName?.keyword || "";
    }
    console.log(errMsg);
  }
  isLoading.value = false;
};

const getGroupList = () => {
  return new Promise(async (resolve, reject) => {
    const [err, res] = await to(getAllGroupListAxios({}, currentTeamId.value));

    if (err) {
      reject(err);
      return;
    }
    const { data }: any = res;
    resolve(data?.data?.list || []);
  });
};

const onGetMemberLevelMoreAxios = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  isLoading.value = true;
  try {
    result = await getMemberNameMoreAxios({ level_id: currentMore.value?.level_id }, currentTeamId.value);
    result = getResponseResult(result);
    isLoading.value = false;
    if (!result) {
      return;
    }
    memberLevelsMore.value = result.data?.member_list;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    console.log(errMsg);
  }
  isLoading.value = false;
};
// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       onGetMemberLevelAxios();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );
const onCard = (box) => {
  if (box.activate !== 1) return;
  if (box.status === 3) return;
  onOpenCard(box);
};

const onOpenCard = (row) => {
  // const id = `PT${row.idStaff}`;
  const params = {
    cardId: row.card,
    myId: userItem.value.cardId,
  };
  ipcRenderer.invoke("identity-card", params);
};

const onGetMemberNameDetail = (box) => {
  return new Promise(async (resolve, reject) => {
    const [err, res] = await to(getMemberNameDetailAxios({ id: box.id }));
    if (err) {
      reject(err);
    } else {
      const { data } = res;
      resolve(data);
    }
  });
};
const onLookNameDetail = (box) => {
  if (box.activate !== 1) {
    MessagePlugin.warning({ duration: 1500, content: "成员暂未激活" });
    return;
  }
  onGetMemberNameDetail(box).then((res: any) => {
    console.log(res);
    nameDetailModelRef.value?.onOpen(res?.data, currentTeamId.value);
  });
};

const squareTeamId = ref("");
const annualFeeDialogUpgradeVisible: Ref<any> = ref(false);
const isShowNameDetail = ref(false);
// 升级后的回调
const upgradeLoaded = () => {
  // console.log('upgradeLoaded')
  // MessagePlugin.success('购买成功')
  onSearch();
  if (isShowNameDetail.value) {
    onGetMemberNameDetail(detailData.value).then((res: any) => {
      console.log(res);
      nameDetailModelRef.value?.onOpen(res.data, currentTeamId.value);
    });
  }

  // 升级后重新触发流程
  // upgradePackageRef.value.onClose();
  // onRunTeamAnnualFee()
};
const detailData = ref(null);
const openVisible = ref(false);
const onConnectPlatformDetail = (box) => {
  isShowNameDetail.value = true;
  detailData.value = box;
  onConnectPlatform(box);
};
const onCloseDetail = (box) => {
  isShowNameDetail.value = false;
};

const annualConnectRef = ref(null);
const onRefreshType = () => {
  if (isShowNameDetail.value) {
    onGetMemberNameDetail(detailData.value).then((res: any) => {
      console.log(res);
      nameDetailModelRef.value?.onOpen(res.data);
    });
  }

  onSearch();
}

const onBackType = (res) => {
  if (res?.type === TeamAnnualTypeError.Success) {
    // MessagePlugin.success('连接成功')
    // 更新详情里面的数据
    if (isShowNameDetail.value) {
      onGetMemberNameDetail(detailData.value).then((res: any) => {
        console.log(res);
        nameDetailModelRef.value?.onOpen(res.data);
      });
    }

    onSearch();
  }
};
const onConnectPlatform = async (row) => {
  annualConnectRef.value.onConnectPlatform({...row, name: row.team_name});

};

// const onConnectPlatform = (box) => {
//   squareTeamId.value = box?.relation_team_id;
//   const param = {
//     // squareId: square?.square?.squareId,
//     belong_team_id: currentTeamId.value,
//     consume_team_id:  squareTeamId.value
//   }
//   onDisplayToPlatform(param, squareTeamId.value).then((res)=> {
//     if(res === 'update') {
//       console.log('update')
//       // squareTeamId.value = square?.square?.originId;
//       annualFeeDialogUpgradeVisible.value = true;
//     } else if(res === 'success'){
//       MessagePlugin.success('连接成功')
//       // 更新详情里面的数据
//       if(isShowNameDetail.value) {
//         onGetMemberNameDetail(detailData.value).then((res:any) => {
//           console.log(res)
//           nameDetailModelRef.value?.onOpen(res.data);
//         });
//       }
//       onSearch();
//     }
//   }).catch((err)=> {
//     console.log('err:', err)
//     if(err) {
//       if(err === 'un_buy_annuual') {
//         openVisible.value = true;
//       } else {
//         MessagePlugin.error(err);
//       }
//     }
//   })
// };

// const buySuccess = (val?) => {
//   console.log(val)
//   MessagePlugin.success('购买成功')
//   // 购买成功提示弹窗
//   // onRunTeamAnnualFee();
//   // successOpenMemberRef.value.onOpen()
// }

const onLoadMore = (level) => {
  // 加载更多
  isLoadMore.value = true;
  currentMore.value = level;
  // onGetMemberLevelMoreAxios();
};
const onBack = () => {
  isLoadMore.value = false;
};

onBeforeMount(() => {
  memberLevels.value = [];
});
onMountedOrActivated(() => {
  pagination.page = 0;

  getIdentityTemp();
  // getMemberJobsList();
  // if(!isLoading.value) {
  //   setTimeout(() => {

  //   })
  // }
});

const onSearch = () => {
  console.log("onSearch");
  page.value = null;
  memberList.value = [];
  pagination.page = 1;
  // onGetMemberLevelSearchAxios();

  // const params = {
  //   ...toRaw(formData.value)
  // }

  onGetMemberLevelAxios();
};
const viewImg = (val) => {
  // const temp = attrs.value.value.map((item) => ({
  //   url: item.file_name,
  //   imgIndex: index,
  // }));
  let temp = [{ url: val }];
  ipcRenderer.invoke("view-img", JSON.stringify(temp));
};
const onReload = () => {
  if (keyword.value) {
    onSearch();
  } else {
    memberLevels.value = [];
    onGetMemberLevelAxios(); // 会员名录列表
  }
};
const userItem: any = ref({});
const userList = ref([]);
const changeUser = (user) => {
  userItem.value = user;
  // localStorage.setItem("GovernmentStaff", userItem.value?.id);
  localStorage.setItem("identityTemp", userItem.value?.id);

  onGetMemberLevelAxios();
};
const getIdentityTemp = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  isLoading.value = true;
  try {
    // staffOpenid
    // const id = localStorage.getItem("openid");
    result = await getGovernmentStaffOpenid(currentTeamId.value);
    // result = await getMemberNameMoreAxios({ level_id: currentMore.value?.level_id }, currentTeamId.value);
    result = getResponseResult(result);
    // isLoading.value = false;
    if (!result) {
      return;
    }
    console.log(result, "results");
    userList.value = result.data;
    const ident = localStorage.getItem("identityTemp");
    const getDefaultUser = () =>
      userList.value.length > 1 ? userList.value.find((item) => item.type === 2) : userList.value[0];
    if (ident) {
      userItem.value = userList.value.find((item) => item.staffId === Number(ident)) || getDefaultUser();
    } else {
      userItem.value = getDefaultUser();
    }
    localStorage.setItem("identityTemp", userItem.value?.staffId);
    console.log(userItem.value, "userItem.value");

    onGetMemberLevelAxios();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    console.log(errMsg);
  }
  // isLoading.value = false;
};
</script>

<style lang="less" scoped>
@import "@renderer/views/digital-platform/style/name-comp.less";

.group {
  .banner {
    display: flex;
    justify-content: space-between;
  }
  &-title {
    .title {
      color: var(--text-kyy_color_text_1, #1a2139);

      /* kyy_fontSize_2/bold */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .num {
      color: var(--text-kyy_color_text_2, #516082);

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
}
</style>
