/** 消息类型 */
export type MessageType =
  | 'parent-to-child'
  | 'child-to-parent'
  | 'iframe'
  | 'electron';

/** 消息数据接口 */
export interface MessageData<T = any> {
  type: MessageType;
  action: string;
  data?: T;
  callbackId: string;
  source?: string;
  target?: string;
}

/** 通信目标类型 */
export interface CommunicationTarget {
  window: Window;
  origin: string;
}

/** 通信配置接口 */
export interface CommunicationConfig {
  isElectron: boolean;
  isIframe: boolean;
  isChildWindow: boolean;
}
