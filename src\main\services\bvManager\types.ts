import { B<PERSON>er<PERSON>ie<PERSON>, BrowserViewConstructorOptions } from 'electron';

export interface ViewBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ViewOptions {
  bounds?: ViewBounds;
  url?: string;
  backgroundColor?: string;
  transparent?: boolean;
  autoResize?: boolean;
  webPreferences?: Electron.WebPreferences;
  [key: string]: any;
}

export interface ViewConfig {
  type: string;
  url?: string;
  baseURL?: string;
  maxPoolSize?: number;
  viewOptions?: ViewOptions;
  autoReload?: boolean;
}

export interface PoolConfig {
  type: string;
  maxSize: number;
}

