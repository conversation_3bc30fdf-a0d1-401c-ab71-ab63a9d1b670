export enum webHostEnum {
  TEST = 'https://web-qa.ringkol.com',
  DEV = 'https://web-dev.ringkol.com',
  PRE = 'https://web-pre.ringkol.com',
  PROD = 'https://web.ringkol.com',
}

interface ConfigInfo {
  NODE_ENV: string;
  env: string;
  isRingkolDesktopApp: boolean;
  token: string | null;
  webUrl: string;
}

const configInfo: ConfigInfo = {
  NODE_ENV: process.env.NODE_ENV,
  env: 'PROD',
  isRingkolDesktopApp: false,
  token: null,
  webUrl: '',
};

const getIsRingkolDesktopApp = (): boolean => {
  const isRingkolDesktopApp = navigator.userAgent.includes('Electron');
  // console.log('getIsRingkolDesktopApp userAgent:', isRingkolDesktopApp, navigator.userAgent)
  return isRingkolDesktopApp;
};

const getEnvironment = (origin: string): string => {
  const params = new URLSearchParams(location.search);
  const env = params.get('env');
  const token = params.get('token');

  if (token) {
    localStorage.setItem('main_token', token);
  }

  if (env && env in webHostEnum) return env;

  if (origin.includes('qa.') || origin.includes('test.')) return 'TEST';
  if (
    origin.includes('dev.') ||
    origin.includes('localhost:') ||
    origin.includes('unitpark.lynker.cn')
  )
    return 'DEV';
  if (origin.includes('pre.')) return 'PRE';
  return 'PROD';
};

const getBaseInfo = () => {
  const isDesktopApp = getIsRingkolDesktopApp();

  if (isDesktopApp && window.LynkerSDK?.config) {
    try {
      return window.LynkerSDK?.config;
    } catch (error) {
      console.error('Failed to get config from Ringkol Desktop App:', error);
      return { env: 'PROD' };
    }
  }

  return {
    env: getEnvironment(window.location.origin),
  };
};

export const init = async (): Promise<ConfigInfo> => {
  const baseInfo = getBaseInfo();
  const isDesktopApp = getIsRingkolDesktopApp();

  Object.assign(configInfo, {
    env: baseInfo.env,
    isRingkolDesktopApp: isDesktopApp,
    token: isDesktopApp ? baseInfo.token : localStorage.getItem('main_token'),
    webUrl: webHostEnum[baseInfo.env],
  });

  return configInfo;
};

export const getConfig = (): ConfigInfo => {
  //if (!configInfo.env) {
  init();
  //}
  // console.log('getConfig configInfo:', configInfo);
  return configInfo;
};

export const getPageQuery = (target: string): string => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('token') || target || '';
};
