import { jsbridge } from './utils/jsbridge';
import type {
  BusinessConfig,
  MessageData,
  UserInfo,
} from './utils/jsbridge/types';

// 1. 使用集成到 jsbridge 的 hook 方式
function setupJsBridgeWithHook() {
  jsbridge.useJsBridge(async (messageData: MessageData) => {
    console.log('Received message via hook:', messageData);

    switch (messageData.type) {
      case 'child-to-parent':
        // 处理来自子窗口的消息
        break;
      case 'iframe':
        // 处理来自iframe的消息
        break;
      case 'parent-to-child':
        // 处理来自父窗口的消息
        console.log('Received message from parent:', messageData.data);
        break;
    }
  });
}

// 2. 业务模块使用示例
async function businessExample() {
  try {
    // 获取用户信息
    const userResponse = await jsbridge.business.getUserInfo();
    if (userResponse.code === 0) {
      const userInfo: UserInfo = userResponse.data;
      console.log('User info:', userInfo);
    }

    // 获取业务配置
    const configResponse = await jsbridge.business.getBusinessConfig();
    if (configResponse.code === 0) {
      const config: BusinessConfig = configResponse.data;
      console.log('Business config:', config);
    }

    // 更新业务配置
    const newConfig: Partial<BusinessConfig> = {
      appId: 'new-app-id',
      version: '2.0.0',
    };
    await jsbridge.business.updateBusinessConfig(newConfig);
  } catch (error) {
    console.error('Business operation failed:', error);
  }
}

// 3. 消息监听示例 - 直接使用 jsbridge 方式
const messageHandler = (messageData: MessageData) => {
  console.log('Received message through listener:', messageData);
};

// 添加消息监听器
jsbridge.onMessage(messageHandler);

// 4. 跨窗口通信示例
async function communicationExample() {
  try {
    // 打开新窗口
    const windowOptions = {
      url: 'https://example.com',
      name: 'child-window',
      browserWindow: {
        width: 800,
        height: 600,
        resizable: true,
      },
    };

    // 发送消息到新窗口
    const messageData: MessageData = {
      type: 'parent-to-child',
      action: 'update-data',
      data: { key: 'value' },
      callbackId: Date.now().toString(),
      source: window.name || 'main-window',
    };

    // 发送消息
    await jsbridge.send('custom-action', messageData);

    // 向iframe发送消息
    const iframe = document.querySelector('iframe');
    if (iframe) {
      await jsbridge.send('iframe-action', { data: 'some-data' }, iframe);
    }
  } catch (error) {
    console.error('Communication failed:', error);
  }
}

// 5. 清理示例
function cleanup() {
  // 移除特定的消息监听器
  jsbridge.onMessage(null);
}

// 执行示例
async function runExample() {
  // 设置 Hook 方式的消息监听
  setupJsBridgeWithHook();

  await businessExample();
  await communicationExample();

  // 在适当的时候进行清理
  // cleanup();
}

runExample().catch(console.error);
