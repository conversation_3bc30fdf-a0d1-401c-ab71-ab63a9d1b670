<template>
  <div style="height: calc(100% - 40px);background: #fff;">
    <work-area-head
      :tab-theme="'arrow'"
      :tab-list="tabList"
      :active-index="activeIndex"
      :current-app-path="'approvalLayout'"
      :group-list="activityStore.groupList"
      :cloud-disk-type="cloudDiskType"
      :icon-icon-window="iconIconWindow"
      :not-red="true"
      :red-type="2"
      activity-hid-group
      @openRefresh="openRefresh"
      @deltab-item="deltabItem"
      @settab-item="settabItem"
      @getGroupListabi="getGroupListabi"
      @set-active-index="setActiveIndex"
    />
    <router-view
      v-slot="{ Component, route }"
      :tab-list="tabList"
      :tab-index="tabIndex"
      :active-index="activeIndex"
      class="clouddiskIndex"
      :cards-type="cloudDiskType"
      @set-active-index="setActiveIndex"
      @deltab-item="deltabItemRun"
      @settab-item="settabItem"
      @approval-exc="approvalExcInit"
      @set-active-index-and-name="setActiveIndexAndName"
    >
      <keep-alive :exclude="data.exc">
        <component
          :is="wrap(route, Component)"
          v-if="data.showCompoent"
          :key="route.path"
          ref="routerViewRef"
          :exclude="data.exc"
        />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, h, onBeforeUnmount} from "vue";
import { useRouter, useRoute } from "vue-router";
import { MessagePlugin } from "tdesign-vue-next";
import { useActivityStore } from "@renderer/views/activity/store";
import { useI18n } from "vue-i18n";
import WorkAreaHead from "../../components/common/WorkAreaHead.vue";
import LynkerSDK from '@renderer/_jssdk';
import emitter from "@/utils/MittBus";
const { ipcRenderer } = LynkerSDK;

const router = useRouter();
const route = useRoute();

const activityStore = useActivityStore();
const { t } = useI18n();

ipcRenderer.on("download-item-end", (name, val) => {
  MessagePlugin.success(`${val}已经下载完成`);
});

ipcRenderer.on('refresh-activity-unread-stats', (event, value) => {
  activityStore.getUnreadStats();
});

const iconIconWindow = ref(true);
const routerViewRef = ref(null);
const tabList = ref([]);
const cloudDiskType = ref({
  name: t("activity.activity.person"),
  id: "",
  icon: "",
  teamId: "",
  uuid: "",
});
const tabIndex = ref(0);
const data = ref({
  showCompoent: true,
  // exc: ['/activity/activityList', '/activity/activityListInvolved', '/activity/activityListDraft'],
  exc: [],
});
const activeIndex = ref(0);
const wrapperMap = new Map();
//路由+组件的显示
const wrap = (route, component) => {
  let wrapper;
  if (component) {
    const wrapperName = route.path;
    if (wrapperMap.has(wrapperName)) {
      wrapper = wrapperMap.get(wrapperName);
    } else {
      wrapper = {
        name: wrapperName,
        render() {
          return h(component);
        },
      };
      wrapperMap.set(wrapperName, wrapper);
    }
    return h(wrapper);
  }
};
const approvalExcInit = () => {
  data.value.exc.push("deviceFormDesign");
  data.value.showCompoent = false;
  setTimeout(() => {
    data.value.showCompoent = true;
    // data.value.exc = ['/activity/activityList', '/activity/activityListInvolved', '/activity/activityListDraft'];
    data.value.exc = [];
  });
};
//刷新
const openRefresh = () => {
  const currentRoute = router.currentRoute.value;
  console.log(data.value, currentRoute, "currentRoute--------------------------");
  currentRoute.matched.forEach((r) => {
    if (r.name === currentRoute.name) {
      if(currentRoute.name === 'activityCreate'|| currentRoute.name === 'activityProfessionalCreate'  || currentRoute.name === 'activityLiteCreate'){
        // 新建/草稿页面不刷新（包含极速版/标准版/专业版）
        return;
      }
      // 获取到当前页面的name
      // const comName = r.components.default.name;
      const comName = r.path;
      console.log(comName, r, "comName---------------------------comName------------------comName");
      if (comName !== undefined) {
        data.value.exc.push(currentRoute.path);
        data.value.showCompoent = false;
      }
    }
  });
  setTimeout(() => {
    data.value.showCompoent = true;
    // data.value.exc = ['/activity/activityList', '/activity/activityListInvolved', '/activity/activityListDraft'];
    data.value.exc = [];
  });
  memberTeamsReqRun();
};
const confirmDia = ref(null);

//获取所有tab列表
const getGroupListabi = (item) => {
  activeIndex.value = tabList.value?.findIndex((e) => e.path === item.path);
};
//删除某一项tab
const deltabItemRun = (index, flag) => {
  data.value.exc = tabList.value[index].name;
  data.value.showCompoent = false;
  console.log(activeIndex.value, index, flag);
  if (flag) {
    // router.push("/activity/activityList");
    router.push({
      path: tabList.value[index - 1].path,
      query: tabList.value[index - 1].query,
    });
  }
  setTimeout(() => {
    data.value.showCompoent = true;
    // data.value.exc = ['/activity/activityList', '/activity/activityListInvolved', '/activity/activityListDraft'];
    data.value.exc = [];
  });
  tabList.value.splice(index, 1);

  if(activeIndex.value > tabList.value.length - 1){
    activeIndex.value = tabList.value.length - 1;
  }
  activityStore.setTabList(tabList.value);
};
//删除某一项tab
const deltabItem = (index, flag, { path }) => {
  if (path.includes("/activity/activityCreate")) {
    // 特殊处理活动创建/草稿编辑页面的关闭
    emitter.emit("del-activity-create-tab", path);
    return;
  }
  if (path.includes("/activity/activityLiteCreate")) {
    // 特殊处理极速版活动创建/草稿编辑页面的关闭
    emitter.emit("del-activity-lite-create-tab", path);
    return;
  }
  if (path.includes("/activity/manage")) {
    // 特殊处理活动详情管理页面的关闭
    emitter.emit("del-activity-manage-tab", path);
    return;
  }
  deltabItemRun(index, flag);
};
//设置tab高亮
const setActiveIndex = (index) => {
  console.log(index);
  activeIndex.value = index;
};

//获取组织列表相关
const memberTeamsReqRun = async () => {
  activityStore.getUnreadStats();
  await activityStore.getGroupList();
  await activityStore.getManagedGroupList();
};

onMounted(() => {
  memberTeamsReqRun();
  jump()
  LynkerSDK.ipc.handleRenderer('activities-is-inited', async () => {
    console.log('activities-is-inited');
    return true;
  });
});

const jump = () => {
  const { jumpPath } = route?.query || {};
  if (!jumpPath) return;
  router.push({
    path: jumpPath,
    query: {
      ...route.query,
    },
  });
}

//添加tab
const settabItem = (item) => {
  tabList.value.push(item);
  activityStore.setTabList(tabList.value);
  activeIndex.value = tabList.value.length - 1;
};
//设置高亮tab
const setActiveIndexAndName = (data) => {
  console.log(data, tabList.value);
  let index = tabList.value.findIndex((item) => item.tag === (data.pathType || "activityList"));
  if (index !== -1) {
    activeIndex.value = index;
    tabList.value[index].path = data.path;
    tabList.value[index].name = data.name;
    tabList.value[index].title = data.title;
  }
};
</script>

<style lang="less" scoped></style>
