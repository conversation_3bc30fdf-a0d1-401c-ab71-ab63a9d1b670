<template>
	<div class="move-files-vue">
		<div class="head-box">
			<img
				style="width: 24px; margin-right: 12px"
				src="../../assets/img/<EMAIL>"
				@click="routeo"
			/>
			<t-popup trigger="click" placement="bottom-right">
				<div style="display: flex; align-items: center">
					<div style="color: #13161b; margin-right: 8px" class="org-text">{{ actGroup.name }}</div>
					<img style="width: 16px" src="../../assets/img/<EMAIL>" />
				</div>
				<template #content>
					<div class="t-popup-operate-move operate-kr">
						<div
							v-for="item in groupList"
							:key="item.id"
							class="flex-align operate-item-fliter"
							@click="changGroup(item)"
						>
							<!-- <img v-if="item.icon" :src="item.icon" />
							<img v-else style="width: 16px" src="../../assets/img/<EMAIL>" /> -->

							<span class="operate-item-span">{{ item.name }}</span>
						</div>
					</div>
				</template>
			</t-popup>
		</div>
		<div class="selector-box">
			<div v-if="optionsBreadcrumb.length > 0" class="head-title-right">
				<div v-if="optionsBreadcrumb.length < 4" class="breadcrumb-box">
					<div
						v-for="(item, index) in optionsBreadcrumb"
						:key="item.id"
						class="click-btn color-black"
						style="display: flex; align-items: center"
					>
						<img v-if="index !== 0" style="margin: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
						<t-tooltip :content="item.title">
							<div class="title-ovfler" @click="changBreadcrumbItem(item, index)">{{ item.title }}</div>
						</t-tooltip>

						<!-- <add-icon
							v-if="index === optionsBreadcrumb.length - 1"
							style="margin-left: 8px"
							class="addicon"
							@click="openAddFile(optionsBreadcrumb[optionsBreadcrumb.length - 1], 300)"
						/> -->
					</div>
				</div>
				<div v-if="optionsBreadcrumb.length >= 4" style="display: flex; align-items: center; padding-left: 12px">
					<div class="flex-align click-btn" @click="changBreadcrumbItem(optionsBreadcrumb[0], 0)">
						<t-tooltip :content="optionsBreadcrumb[0].title">
							<span class="title-ovfler">{{ optionsBreadcrumb[0].title }} </span>
						</t-tooltip>

						<img style="margin: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
					</div>
					<div class="flex-align">
						<span> ... </span>
						<img style="margin: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
					</div>
					<div class="flex-align click-btn">
						<t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 2].title">
							<span
								class="title-ovfler"
								@click="
									changBreadcrumbItem(optionsBreadcrumb[optionsBreadcrumb.length - 2], optionsBreadcrumb.length - 2)
								"
							>
								{{ optionsBreadcrumb[optionsBreadcrumb.length - 2].title }}
							</span>
						</t-tooltip>

						<img
							v-if="optionsBreadcrumb.length - 2 > -1"
							style="margin: 0 8px"
							src="../../assets/svg/icon_arrowRight.svg"
							alt=""
						/>

						<t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 1].title">
							<span
								class="title-ovfler"
								style="color: #13161b"
								@click="
									changBreadcrumbItem(optionsBreadcrumb[optionsBreadcrumb.length - 1], optionsBreadcrumb.length - 1)
								"
							>
								{{ optionsBreadcrumb[optionsBreadcrumb.length - 1].title }}
							</span>
						</t-tooltip>
					</div>
					<!-- <add-icon
						style="margin-left: 8px"
						class="addicon"
						@click="openAddFile(optionsBreadcrumb[optionsBreadcrumb.length - 1], 300)"
					/> -->
				</div>
			</div>
			<div class="selector">
				<div class="left">
					<!-- <div class="head-title">
						<span> {{ actGroup.name === '个人盘' ? '个人盘' : '团队文件' }} </span>

						<add-icon class="addicon" @click="openAddFile(actGroup, 200)" />
					</div> -->
					<ul class="ulbox">
						<!-- :class="{ selected: selectedIndex === index }" -->
						<li
							v-for="(option, index) in filterLeftData"
							:key="option.id"
							class="item-li"
							@click="selectLeft(index, option)"
						>
							<div class="option">
								<img class="icon" :src="fileImage(option.type)" />

								<span class="text">{{ option.title }}</span>
							</div>
						</li>
						<li v-if="filterLeftData.length === 0">
							<div class="nodata">
								<img src="../../assets/img/notdata.png" alt="" />
								<div>暂无数据</div>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
		<!-- <div class="selector-box">
			<div class="selector">
				<div class="left">
					<div class="head-title">
						<span> {{ actGroup.name === '个人盘' ? '个人盘' : '团队文件' }} </span>

						<add-icon class="addicon" @click="openAddFile(actGroup, 200)" />
					</div>
					<ul>
						<li
							v-for="(option, index) in filterLeftData"
							:key="index"
							:class="{ selected: selectedIndex === index }"
							@click="selectLeft(index, option)"
						>
							<div class="option">
								<img class="icon" :src="fileImage(option.type)" />

								<span class="text">{{ option.title }}</span>
							</div>
						</li>
					</ul>
				</div>
				<div class="right">
					<div class="head-title-right">
						<div v-if="optionsBreadcrumb.length < 4" class="breadcrumb-box">
							<div
								v-for="(item, index) in optionsBreadcrumb"
								:key="item.id"
								class="click-btn color-black"
								style="display: flex; align-items: center"
							>
								<img v-if="index !== 0" style="margin: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
								<t-tooltip :content="item.title">
									<div class="title-ovfler" @click="changBreadcrumbItem(item, index)">{{ item.title }}</div>
								</t-tooltip>

								<add-icon
									v-if="index === optionsBreadcrumb.length - 1"
									style="margin-left: 8px"
									class="addicon"
									@click="openAddFile(optionsBreadcrumb[optionsBreadcrumb.length - 1], 300)"
								/>
							</div>
						</div>
						<div v-if="optionsBreadcrumb.length >= 4" style="display: flex; align-items: center; padding-left: 12px">
							<div class="flex-align click-btn" @click="changBreadcrumbItem(optionsBreadcrumb[0], 0)">
								<t-tooltip :content="optionsBreadcrumb[0].title">
									<span class="title-ovfler">{{ optionsBreadcrumb[0].title }} </span>
								</t-tooltip>

								<img style="margin: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
							</div>
							<div class="flex-align">
								<span> ... </span>
								<img style="margin: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
							</div>
							<div class="flex-align click-btn">
								<t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 2].title">
									<span
										class="title-ovfler"
										@click="
											changBreadcrumbItem(optionsBreadcrumb[optionsBreadcrumb.length - 2], optionsBreadcrumb.length - 2)
										"
									>
										{{ optionsBreadcrumb[optionsBreadcrumb.length - 2].title }}
									</span>
								</t-tooltip>

								<img
									v-if="optionsBreadcrumb.length - 2 > -1"
									style="margin: 0 8px"
									src="../../assets/svg/icon_arrowRight.svg"
									alt=""
								/>

								<t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 1].title">
									<span
										class="title-ovfler"
										style="color: #13161b"
										@click="
											changBreadcrumbItem(optionsBreadcrumb[optionsBreadcrumb.length - 1], optionsBreadcrumb.length - 1)
										"
									>
										{{ optionsBreadcrumb[optionsBreadcrumb.length - 1].title }}
									</span>
								</t-tooltip>
							</div>
							<add-icon
								style="margin-left: 8px"
								class="addicon"
								@click="openAddFile(optionsBreadcrumb[optionsBreadcrumb.length - 1], 300)"
							/>
						</div>
					</div>
					<ul v-if="rightItems.length > 0">
						<li
							v-for="(item, index) in rightItems"
							:key="index"
							class="option"
							:class="{ selected: rightIndex === item.id }"
							@click="clickRightItem(item)"
						>
							<img class="icon" :src="fileImage(item.type)" />
							<span class="text">{{ item.title }}</span>
						</li>
					</ul>
					<ul v-else class="nodata">
						<img src="../../assets/img/notdata.png" alt="" />
						<div>暂无数据</div>
					</ul>
				</div>
			</div>
		</div> -->

		<!-- 提示未登录 -->
		<t-dialog
			v-model:visible="addDialog"
			:cancel-btn="false"
			:close-btn="false"
			:header="true"
			:footer="false"
			class="tishidengluclassaaa"
		>
			<div>
				<img
					src="@/assets/<EMAIL>"
					style="width: 16px; cursor: pointer; height: 16px; position: absolute; top: -36px; right: 20px"
					@click="addDialog = false"
				/>
				<div class="fisttip">文件夹名称</div>
				<div class="item-box" style="width: 100%; margin-bottom: 24px">
					<t-input v-model="fileName" :maxlength="50" placeholder="请输入文件夹名称" style="margin-top: 8px"> </t-input>
				</div>
				<div class="btnbox">
					<div class="zhuce" style="margin-right: 12px" @click="(addDialog = false), (fileName = '')">取消</div>
					<div class="lgonbtn" @click="subFlie()">新建</div>
				</div>
			</div>
		</t-dialog>
		<div class="footers">
			<div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
				<t-button
					style="height: 40px; width: 100%; margin-right: 12px"
					theme="default"
					variant="outline"
					@click="openAddFile(optionsBreadcrumb[optionsBreadcrumb.length - 1], 200)"
				>
					新建文件夹
				</t-button>
				<t-button
					style="height: 40px; width: 100%"
					:disabled="actGroup.name === '个人盘' ? false : optionsBreadcrumb.length === 0 ? true : false"
					@click="subForm()"
				>
					移动到此
				</t-button>
			</div>
		</div>
	</div>
</template>

<script setup lang="jsx" name="MoveFile">
import { ref, defineExpose, onMounted, computed } from 'vue';

import { MessagePlugin } from 'tdesign-vue-next';

import { useRoute, useRouter } from 'vue-router';
import { fileImage } from '@/utils/myUtils';
import { DiskFileListGetItem, AddDiskFile, FileMove, DiskFileList, FileCopy, DiskList } from '@/api/yunpanlink';
import router from '@/router';

const groupList = ref([]);
onMounted(() => {
	// window.localStorage.
	// getGroupListabi();
	openWin();
});

const getGroupListabi = async () => {
	console.log(11111111111111);
	// 当前和上一个比较如果不是一个类型而且类型是网盘就走网盘列表接口
	const res = await DiskList(); // 右上角菜单选项后端给接口
	console.log(res, '云盘resssssssssss');
	groupList.value = [];

	res.data.data.list.forEach((ele) => {
		groupList.value.push({
			id: ele.id,
			teamId: ele.team,
			permission: ele.permission,
			name: ele.type === 0 ? '个人盘' : ele.team.fullName,
			icon: ele.type === 0 ? '../assets/<EMAIL>' : ele.team.logo,
		});
	});
	actGroup.value = JSON.parse(JSON.stringify(groupList.value[0]));
};

const props = defineProps({
	leftData: {
		type: Array,
		default: () => [],
	},
	tokens:{
		type: String,
		default:''
	},
	// 当前行文件id
	actRow: {
		type: Object,
		default: () => {},
	},
});
const fistFileData = ref([]);
const rightIndex = ref(-1);
const fileName = ref('');
const actGroup = ref({});
const addDialog = ref(false);
const moveFile = ref(false);
const optionsBreadcrumb = ref([]);
const rightItems = ref([]);
const actitem = ref(null);
const emit = defineEmits([
	'getMoveLeftList',
	'setMoveActItem',
	'getFileList',
	'getFileListMainAddFileFalg',
	'clearSelectedRowKeys',
]);
const mainAddFileFalg = ref(100);
const selectedIndex = ref(-1);
const subFlie = () => {
	const objs = {
		disk_id: actGroup.value.id,
		title: fileName.value,
		is_folder: 1,
		type: 'folder',
		pid: actitem.value.id,
	};
	AddDiskFile(objs)
		.then((res) => {
			if (res.status === 418) {
				MessagePlugin.error({
					content: '你输入的信息包含敏感内容,请修改后重试',
					zIndex: 9100000001,
				});
			}
			if (res.status === 200) {
				fileName.value = '';
				if (optionsBreadcrumb.value.length === 0) {
					getMoveLeftList(actGroup.value.id);
				} else {
					getDiskFileListGetItem(actitem.value.id);
					// getMoveLeftList
				}
				// if (mainAddFileFalg.value === 200) {
				// 	MessagePlugin.success('添加成功');
				// 	// emit('getFileListMainAddFileFalg');
				// 	getMoveLeftList(actitem.value.id);
				// } else {
				// 	MessagePlugin.success('添加成功');
				// 	getDiskFileListGetItem(actitem.value.id);
				// }
				addDialog.value = false;
			}
			console.log(res, 'ressssssssss');
		})
		.catch((err) => {
			console.log(err, 'errerrerrerrerr');
		});
};
const routeo = () => {
	router.push({
		path: '/molinkTable',
		query: {
			key: route.query.key,
			code: route.query.code,
			resData: route.query.resData,
		},
	});
};
const route = useRoute();
const subForm = () => {
	FileCopy({
		file_id: route.query.id,
		pid:
			actGroup.value.name === '个人盘' && optionsBreadcrumb.value.length === 0
				? 0
				: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].id,

				token:props.tokens
	}).then((res) => {
		console.log(res);
		selectedIndex.value = -1;
		rightItems.value = [];
		optionsBreadcrumb.value = [];
		fileName.value = '';
		moveFile.value = false;
		if (res.status !== 200) {
			return MessagePlugin.error(res.data.message);
		}
		router.push({
			path: '/molinkTable',
			query: {
				key: route.query.key,
				code: route.query.code,
				resData: route.query.resData,
			},
		});

		return MessagePlugin.success('操作成功');
	});

	// if (arr.length > 0) {
	// 	Promise.all(arr).then((res) => {
	// 		// getDiskFileListGetItem(optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].id);
	// 		emit('getMoveLeftList', actGroup.value.id);
	// 		emit('getFileList');
	// 		emit('clearSelectedRowKeys');

	// 		selectedIndex.value = -1;
	// 		rightItems.value = [];
	// 		optionsBreadcrumb.value = [];
	// 		fileName.value = '';
	// 		moveFile.value = false;
	// 		res.forEach((element) => {
	// 			if (element.status !== 200) {
	// 				return MessagePlugin.error('操作失败');
	// 			}
	// 			return MessagePlugin.success('操作成功');
	// 		});
	// 		console.log(res, 'qqqqqqqqqqqssssssssssss');
	// 	});
	// }
};
const selectLeft = (index, item) => {
	if (item.type === 'folder') {
		console.log(item, 'sssssssssss');
		optionsBreadcrumb.value.push({
			title: item.title,
			id: item.id,
		});
		getDiskFileListGetItem(item.id);
		// if (selectedIndex.value !== index) {
		// 	rightIndex.value = -1;
		// 	selectedIndex.value = index;
		// 	// rowClick()
		// 	optionsBreadcrumb.value = [
		// 		{
		// 			title: item.title,
		// 			id: item.id,
		// 		},
		// 	];
		// 	getDiskFileListGetItem(item.id);
		// }
	}
};
const getDiskFileListGetItem = (id) => {
	DiskFileListGetItem(id).then((res) => {
		console.log(res, 'click-standalone-window');
		fistFileData.value = res.data.data.list.filter((item) => item.type === 'folder');

		// 右边数据
		// fileName.value = '';
	});
};
const changBreadcrumbItem = (item, index) => {
	if (index !== optionsBreadcrumb.value.length - 1) {
		// let arr=JSON
		const arr = JSON.parse(JSON.stringify(optionsBreadcrumb.value));
		console.log(arr, 'arrarrarrarr');

		optionsBreadcrumb.value.splice(index + 1);
		getDiskFileListGetItem(item.id);
	}
};
const clickRightItem = (item) => {
	optionsBreadcrumb.value.push({
		title: item.title,
		id: item.id,
	});
	rightIndex.value = item.id;
	getDiskFileListGetItem(item.id);
};
const changGroup = (item) => {
	rightItems.value = [];
	fileName.value = '';
	optionsBreadcrumb.value = [];
	selectedIndex.value = -1;
	actitem.value = null;
	rightIndex.value = null;
	// emit('getMoveLeftList', item.id);
	getMoveLeftList(item.id);
	actGroup.value = item;

	// emit('setMoveActItem', item);
};
const onClosedia = () => {
	moveFile.value = false;
	rightItems.value = [];
	fileName.value = '';
	rightIndex.value = null;

	optionsBreadcrumb.value = [];
	selectedIndex.value = -1;
	actitem.value = null;
};

const openAddFile = (item, val) => {
	// actGroup.name === '个人盘' &&
	if (actGroup.value.name !== '个人盘' && !item) {
		return MessagePlugin.error('请选择目录');
	}
	if (actGroup.value.name === '个人盘' && !item) {
		actitem.value = {
			id: 0,
		};
	} else {
		actitem.value = item;
	}
	addDialog.value = true;
	mainAddFileFalg.value = val;
};
const filterLeftData = computed(() => fistFileData.value.filter((item) => item.type === 'folder'));
const closeWin = () => {
	moveFile.value = false;
};
const getMoveLeftList = (id) => {
	DiskFileList(id).then((res) => {
		console.log(res, 'aaaaaaaaaaaaaa1111111');
		fistFileData.value = res.data.data.list;
	});
};
const openWin = async () => {
	// console.log(props.groupList, 'groupListgroupListgroupListgroupList');
	console.log(11111111111111);
	// 当前和上一个比较如果不是一个类型而且类型是网盘就走网盘列表接口
	const res = await DiskList(); // 右上角菜单选项后端给接口
	console.log(res, '云盘resssssssssss');
	groupList.value = [];

	res.data.data.list.forEach((ele) => {
		groupList.value.push({
			id: ele.id,
			teamId: ele.team,
			permission: ele.permission,
			name: ele.type === 0 ? '个人盘' : ele.team.fullName,
			icon: ele.type === 0 ? '../../assets/<EMAIL>' : ele.team.logo,
		});
	});
	actGroup.value = JSON.parse(JSON.stringify(groupList.value[0]));
	getMoveLeftList(groupList.value[0].id);

	moveFile.value = true;
};
defineExpose({
	closeWin,
	openWin,
});
</script>

<style lang="less" scoped>
.left {
	width: 100%;
}
.click-btn {
	cursor: pointer;
}
.selector-box {
	padding: 12px 0 0 16px;
	margin-top: 12px;
	background: #fff;
	display: flex;
	flex-direction: column;
}
.selector {
	// display: flex;
	// height: 100%;
	width: 100%;
}

ul {
	list-style: none;
	margin: 0;
	padding: 0;
	font-size: 14px;
	width: 100%;
	overflow-y: auto;
}
.addicon {
	width: 16px;
	height: 16px;
	font-size: 16px;
	cursor: pointer;
	color: #13161b;
}
.addicon:hover {
	color: #366ef4;
}
li {
	cursor: pointer;
	// width: 288px;
	width: 100%;
	margin-bottom: 4px;

	border-radius: 4px;
	transition: background-color 0.3s ease;
}
.ulbox {
	padding-bottom: 68px;
}
.ulbox:last-child {
	border: none;
}
.item-li:last-child {
	.option {
		border-bottom: none;
	}
}
.fisttip {
	height: 26px;
	font-size: 17px;
	font-family: PingFang SC, PingFang SC-Semibold;
	font-weight: 600;
	text-align: center;
	color: #13161b;
	margin-bottom: 12px;
	line-height: 26px;
}
.breadcrumb-box {
	display: flex;
	align-items: center;
	margin-left: 12px;
}
.title-ovfler {
	max-width: 75px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.color-black:last-child {
	color: #13161b;
}
.flex-align {
	display: flex;
	align-items: center;
}
.option {
	display: flex;
	align-items: center;
	padding: 12px 0 16px 16px;
	height: 72px;
	border-bottom: 1px solid #e3e6eb;
}
.footers {
	display: flex;
	align-items: center;
	height: 68px;
	padding: 0 16px;
	position: fixed;
	font-size: 17px;
	bottom: 0;
	width: 100%;
	background: #fff;
}
.text {
	font-size: 17px;
	color: #13161b;
	margin-right: 8px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 230px;
}
.nodata {
	img {
		width: 200px;
		height: 200px;
		display: block;
		margin: 47px auto 8px;
	}
	div {
		height: 22px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: center;
		color: #13161b;
		line-height: 22px;
	}
}
.move-files-vue {
	background: #f1f2f5;
	display: flex;
	flex-direction: column;
	height: 100%;
}
.icon {
	width: 48px;
	height: 48px;
	margin-right: 12px;
}

.right {
	padding-left: 8px;
	width: 100%;
}

.head-title-right {
	color: #717376;
	font-size: 14px;
	height: 46px;
	line-height: 46px;
}
.head-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	// width: ;
	line-height: 46px;
	height: 46px;
	span {
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Bold;
		font-weight: 700;
		text-align: left;
		color: #13161b;
		line-height: 46px;
		font-family: Microsoft YaHei, Microsoft YaHei-Bold;
		cursor: pointer;
	}
	img {
		width: 16px;
		height: 16px;
		cursor: pointer;
		margin-right: 8px;
	}
}
.head-box {
	height: 44px;
	background: #ffffff;
	padding: 10px 16px;
	display: flex;
	align-items: center;
}
.org-text {
	margin-right: 8px;
}
:deep(.tishidengluclassaaa) {
	.t-dialog__wrap {
		.t-dialog__position {
			.t-dialog {
				margin: 0 28px !important;
				padding: 32px 24px 20px;
			}
		}
	}
}
</style>
<style lang="less">
.t-popup-operate-move {
	.operate-item-fliter {
		height: 40px !important;
		border-radius: 4px;
		margin-bottom: 4px !important;
		display: flex;
		margin: 4px;
		cursor: pointer;
		align-items: center;
		img {
			width: 16px;
			height: 16px;
			margin-right: 8px;
			margin-left: 8px;
		}
		.operate-item-span {
			height: 22px;
			font-size: 14px;
			padding-right: 8px;
			font-family: Microsoft YaHei, Microsoft YaHei-Regular;
			font-weight: 400;
			color: #13161b;
			line-height: 22px;
		}
	}
}

.btnbox {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 32px;
	div {
		height: 44px;
		border: 1px solid #e3e6eb;
		border-radius: 6px;
		width: 100%;
		font-size: 17px;
		font-family: PingFang SC, PingFang SC-Regular;
		font-weight: 400;
		text-align: center;
		color: #13161b;
		line-height: 44px;
	}
	.lgonbtn {
		background: #ffffff;
		color: #13161b;
	}
	.zhuce {
		background: #4d5eff;
		color: #fff;
	}
}
</style>
