# RTable

表格复合组件

```jsx
import { ref } from 'vue';
import { RTable } from '@rk/unitPark';
import 'tdesign-vue-next/es/style/index.css';
import '../../style/index.less';

const tabs = {
  defaultInfo: {
    value: -1,
  },
  attrs: {
    theme: 'normal',
  },
  list: [
    {
      label: '全部',
      value: 'all',
    },
    {
      label: 'tab1',
      value: '1',
    },
  ],
};
const filter = {
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '请输入活动名称',
  },
  advanced: {
    submitText: '搜索',
    form: {
      list: [
        {
          name: 'created_at',
          label: '付款时间',
          type: 'dateRange',
          value: [],
          defaultValue: -1,
          attrs: {
            placeholder: '开始时间,结束时间',
            options: [],
          },
        },
        {
          label: '活动名称',
          type: 'input',
          name: 'name1',
        },
        {
          label: '22222',
          name: 'name2',
          type: 'select',
          // value: '1',
          defaultValue: -1,
          attrs: {
            placeholder: '请选择',
            options: [
              {
                label: '学生',
                value: '1',
              },
              {
                label: '家长',
                value: '2',
              },
            ],
          },
        },
        {
          label: '33333',
          type: 'date',
          name: 'name3',
        },
        {
          label: '44444',
          type: 'dateRange',
          name: 'name4',
          attrs: {
            'enable-time-picker': true,
          },
        },
        // {
        //   label: '是否有座位号',
        //   name: 'hasSeatNumber',
        //   value: '',
        //   defaultValue: 0,
        //   type: 'select',
        //   attrs: {
        //     placeholder: '请选择是否有座位号',
        //     options: '',
        //   },
        // },
        // {
        //   label: '人员类型',
        //   name: 'role',
        //   type: 'select',
        //   value: '1',
        //   defaultValue: -1,
        //   attrs: {
        //     placeholder: '请选择人员类型',
        //     options: [{
        //       label: '学生',
        //       value: '1',
        //     }],
        //   },
        // },
      ],
    },
  },
};
const table = {
  attrs: {
    'row-key': 'registId',
  },
  pagination: {
    pageSize: 5,
    total: 10,
  },
  columns: [
    {
      title: '报名用户',
      width: 280,
      colKey: 'userName',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: '报名ID',
      width: 280,
      colKey: 'registId',
    },
  ],
  list: [
    {
      name: '张君宝',
      userName: '名字',
      registId: '1111',
    },
    {
      name: '李四',
      userName: '名字',
      registId: '1111',
    },
  ],
};
const change = (e) => {
  console.log('change e', e);
};
export default () => (
  <>
    <RTable
      ref="RTableRef"
      tabs={tabs}
      filter={filter}
      table={table}
      emptyData="暂无参与人员"
      onChange={change}
    ></RTable>
  </>
);
```

### Props

<API id="RTable" type="props"></API>

### Events

<API id="RTable" type="events"></API>
