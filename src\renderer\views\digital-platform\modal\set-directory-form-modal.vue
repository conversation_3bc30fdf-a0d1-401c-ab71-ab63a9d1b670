<template>
  <t-drawer v-model:visible="visible" class="drawerSetForm drawerSetBodyNoBottomPadding" attach="body" :z-index="2500"
    :close-btn="true" :size="'472px'">
    <template #header>
      <div class="header">
        <span class="title">设置名录资料</span>
        <!-- <span v-show="!props.isMember" class="tip cursor" @click="onShowMemberFlow">
          <iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>{{ $t('member.winter_column.know_add_flow_1') }} </span> -->
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px; color: #516082" />
    </template>
    <div class="toBody" v-if="visible">
      <div class="form">
        <form-runtime v-show="currentTab === 'unit'" ref="runtimeUnitRef" :widgets="controls_unit"
          @release="releaseRun_unit" @controls-event="controlsEventRun_unit" />
        <form-runtime v-show="currentTab === 'person'" ref="runtimePersonRef" :widgets="controls_person"
          @release="releaseRun_person" @controls-event="controlsEventRun_unit" />
      </div>
    </div>
    <template #footer>
      <div class="footer" style="display: flex; justify-content: flex-end">
        <t-button theme="default" variant="outline" @click="onClose">
          {{ $t('member.impm.select_11') }}
        </t-button>
        <t-button theme="primary" @click="onSubmit">保存</t-button>
      </div>
    </template>
  </t-drawer>
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import lodash, { debounce } from 'lodash';

import { ref, reactive, Ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import formRuntime from '@renderer/components/free-from/runtime/index.vue';
import { useI18n } from 'vue-i18n';
import { originType, TagColors, TagType } from '@renderer/views/digital-platform/utils/constant';
import {
  onGetLabelSettingAxios as onGetLabelSettingPoliticsAxios,
  getIndustryListAxios as getIndustryListAxiosPolitics,
  getTeamsSizeListAxios as getTeamsSizeListAxiosPolitics,
  onPostDirectorySettingAxios as onPostDirectorySettingAxiosPolitics,
} from '@renderer/api/politics/api/businessApi';

import {
  onGetLabelSettingAxios as onGetLabelSettingMemberAxios,
  getIndustryListAxios as getIndustryListAxiosMember,
  getTeamsSizeListAxios as getTeamsSizeListAxiosMember,
  onPostDirectorySettingAxios as onPostDirectorySettingAxiosMember,
} from '@renderer/api/member/api/businessApi';


import {
  onGetLabelSettingAxios as onGetLabelSettingCBDAxios,
  getIndustryListAxios as getIndustryListAxiosCBD,
  getTeamsSizeListAxios as getTeamsSizeListAxiosCBD,
  onPostDirectorySettingAxios as onPostDirectorySettingAxiosCBD,
} from '@renderer/api/cbd/api/businessApi';


import {
  onGetLabelSettingAxios as onGetLabelSettingAssociationAxios,
  getIndustryListAxios as getIndustryListAxiosAssociation,
  getTeamsSizeListAxios as getTeamsSizeListAxiosAssociation,
  onPostDirectorySettingAxios as onPostDirectorySettingAxiosAssociation,
} from '@renderer/api/association/api/businessApi';

import { to } from 'await-to-js';
import { getChidlren, getResponseResult } from '@/utils/myUtils';
const currentTab = ref('');
const { t } = useI18n();
const type = ref(0); // 0创建 1编辑
const props = defineProps({
  teamId: {
    type: String,
    default: '',
  },
  origin: {
    type: String,
    default: originType.Member,
  },
  // orData: {
  //   type: Object,
  //   default: ()=> null
  // }
});

const emits = defineEmits(['onReload']);

const runtimeUnitRef: Ref<any> = ref(null);
const runtimePersonRef: Ref<any> = ref(null);

const handleObj = {
  getLabelFunc: null,
  getIndustryListAxios: null,
  getTeamsSizeListAxios: null,
  // saveTitleFunc: null,
  onPostDirectorySettingAxios: null,
};
const handleFunc = () => {
  switch (props.origin) {
    case originType.Member:
      // handleObj.saveTitleFunc =
      handleObj.getLabelFunc = onGetLabelSettingMemberAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosMember;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosMember;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosMember;
      break;
    case originType.Politics:
      handleObj.getLabelFunc = onGetLabelSettingPoliticsAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosPolitics;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosPolitics;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosPolitics;
      break;
    case originType.CBD:
      handleObj.getLabelFunc = onGetLabelSettingCBDAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosCBD;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosCBD;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosCBD;
      break;
    case originType.Association:
      handleObj.getLabelFunc = onGetLabelSettingAssociationAxios;
      handleObj.getIndustryListAxios = getIndustryListAxiosAssociation;
      handleObj.getTeamsSizeListAxios = getTeamsSizeListAxiosAssociation;
      // handleObj.saveTitleFunc = saveLabelRelationPoliticsAxios;
      // 用来保存名录设置
      handleObj.onPostDirectorySettingAxios = onPostDirectorySettingAxiosAssociation;
      break;
    default:

      break;
  }
};
handleFunc();

const optionsOrganizeType = [
  // { label: "企业", value: 1 },
  // { label: "商协会", value: 2 },
  // { label: "个体户", value: 3 },
  // { label: "其他", value: 0 }
  { label: t('member.second.g'), value: 1 },
  { label: t('member.second.h'), value: 2 },
  { label: t('lss.one.info_14'), value: 4 },
  { label: t('member.second.i'), value: 3 },
  { label: t('member.second.j'), value: 0 },
];

const controls_person: any = ref([
  {
    type: 'BaseInfoMember', // 所使用的套件
    vModel: 'baseInfoMember', // baseInfoMember基础信息 organizeInfoMember组织详情信息
    passiveRelevance: false,
    name: '基础信息',
    typeName: '基础信息',
    // addButtonText: "新增明细",
    value: [],
    fromType: 'person', // 单位unit、个人person
    origin: [
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '基础资料',
        typeName: '基础资料',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba44c-680b-4gfdc8b943c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Logo',
        vModel: 'nameLogo',
        value: [],
        name: '名录照片',
        typeName: '名录照片',
        placeholder: '请上传',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        aspectRatio: 0.7142,
        id: 'namfe-638b-4608-a71b-iiib233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'name',
        value: '',
        name: '姓名',
        typeName: '姓名',
        placeholder: '请输入',
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efbwwwwb-fbaa2c8b233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'TextArea',
        vModel: 'interest',
        value: '',
        name: '兴趣爱好',
        typeName: '兴趣爱好',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'ukklwet-we9y3c-wq',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Tag',
        vModel: 'tagPlatform',
        value: null,
        name: '平台标签',
        typeName: '平台标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: 'u672hjhkc-34kjkrree09y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
        options: [],
      },
      {
        type: 'Tag',
        vModel: 'tagPerson',
        value: null,
        name: '个人标签',
        typeName: '个人标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: 'udvv-aaa-111-lll-adsvcxvxcvc',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Title',
        vModel: 'titleT',
        value: null,
        name: '头衔',
        typeName: '头衔',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u67222-hhhh-aaa-eccec',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '数字名片',
        typeName: '数字名片',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5qqytyiuiu-yrtujhjgh-21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'ElectronicCard',
        vModel: 'electronicCard',
        value: false,
        name: '显示数字名片',
        typeName: '显示数字名片',
        tip: '开启后，会将个人数字名片显示出来。其他平台成员可收藏你的数字名片。',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1eggg-asc-ewewr-gfdg1c',
        parentInfo: {
          id: 'e21906b7-c5yy-4a73-aa76-c8cuuu895e22',
          name: '基础信息',
        },
      },
    ],
  },
]);

const controls_unit: any = ref([
  {
    type: 'BaseInfoMember', // 所使用的套件
    vModel: 'baseInfoMember', // baseInfoMember基础信息 organizeInfoMember组织详情信息
    passiveRelevance: false,
    name: '基础信息',
    typeName: '基础信息',
    // addButtonText: "新增明细",
    value: [],
    fromType: 'unit', // 单位unit、个人person
    origin: [
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '基础信息',
        typeName: '基础信息',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba144c-680b-4608-a71b-fbaa2c8b943c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },

      },
      {
        type: 'Logo',
        vModel: 'nameLogo',
        value: [],
        name: '名录照片',
        typeName: '名录照片',
        placeholder: '请上传',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        aspectRatio: 0.7142,
        id: 'namfe-638b-4608-a71b-f644432b233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'name',
        value: '',
        name: '代表人姓名',
        typeName: '代表人姓名',
        placeholder: '请输入',
        required: true, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'efba144c-680b-4608-a71b-fbaa2c8b233c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'TextArea',
        vModel: 'interest',
        value: '',
        name: '兴趣爱好',
        typeName: '兴趣爱好',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u6722th4c-635630b-467808-a71b-fb909y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Tag',
        vModel: 'tagPlatform',
        value: null,
        name: '',
        typeName: '平台标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u6722th4c-34kjkrree09y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
        options: [],
      },
      {
        type: 'Tag',
        vModel: 'tagPerson',
        value: null,
        name: '',
        typeName: '个人标签',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: false, // 是否显示
        id: 'u6722th4c-635630b-467808-a71b-fb909y3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
      {
        type: 'Title',
        vModel: 'titleT',
        value: null,
        name: '头衔',
        typeName: '头衔',
        placeholder: '请输入',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'u67qq4c-6356tt8-ppy3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb2',
          name: '基础信息',
        },
      },
    ],
  },
  {
    type: 'BaseInfoMember',
    vModel: 'organizeInfoMember',
    passiveRelevance: false,
    name: '组织详细信息',
    typeName: '组织详细信息',
    // addButtonText: "新增明细",
    value: [],
    fromType: 'unit', // 单位unit、个人person
    origin: [
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '组织详细信息',
        typeName: '组织详细信息',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1eedee-a3fb-4e02-8cd9-44445558bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Logo',
        vModel: 'organizeLogo',
        value: [],
        name: '组织logo',
        typeName: '组织logo',
        placeholder: '请上传',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: 'aaaee-a3fb-4e02-8cd9-44445558bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Select',
        vModel: 'organizeName',
        value: '',
        name: '组织名称',
        typeName: '组织名称',
        placeholder: '请输入',
        required: true, // 是否必填
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1eedee-a332-4e02-842-93348d53bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息',
        },
      },
      {
        type: 'InputUnborder',
        vModel: 'unitJob',
        value: '',
        name: '组织岗位（所在单位岗位）',
        typeName: '组织岗位（所在单位岗位）',
        placeholder: '请输入',
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '3bgj6ee-a3fb-4702-8cd9-930898781c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895eb6',
          name: '基础信息',
        },
      },
      {
        type: 'Input',
        vModel: 'organizeAbbrName',
        value: '',
        name: '组织简称',
        typeName: '组织简称',
        placeholder: '请输入',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '33334edee-a3fb-4e02-34679-4444558bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Select',
        vModel: 'organizeType',
        value: '',
        name: '组织类型',
        typeName: '组织类型',
        placeholder: '请选择',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b13323-323-45-6-d21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Select',
        vModel: 'industryType',
        value: undefined,
        name: '行业类型',
        typeName: '行业类型',
        placeholder: '请选择',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '512ee-a32b-4e02-8c22-44441c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Select',
        vModel: 'organizeScale',
        value: undefined,
        name: '组织规模',
        typeName: '组织规模',
        placeholder: '请选择',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '3fedee-a3ff-4ff2-8cd9-445h8bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'TextArea',
        vModel: 'business',
        value: '',
        name: '业务范围',
        typeName: '业务范围',
        placeholder: '请输入',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        width: '100%',
        id: 'unnc-638nn30b-469nn8-anb-fccy3c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },

      {
        type: 'Address',
        vModel: 'organizeAddress',
        value: [],
        address_value: undefined, // 详细地址
        address_placeholder: '请输入详细地址',

        name: '组织地址',
        typeName: '组织地址',
        placeholder: '请选择',
        width: '100%',
        required: false, // 是否必填
        disabled: false, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b2-a3fb-4e02-8cd9-44bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        tag: 'contactInfo',
        name: '联系人信息',
        typeName: '联系人信息',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1eed356-7898-9fg58bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'ContactInfo',
        vModel: 'contactInfo',
        value: false,
        name: '显示联系人',
        typeName: '联系人信息',
        tip: '开启后，若有联系人信息将会显示出来。',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1ee9911-22-23-gf1c',
        parentInfo: {
          id: 'e21906b7-c5yy-4a73-aa76-c8cuuu895e22',
          name: '基础信息',
        },
      },
      {
        type: 'Divider',
        vModel: 'divider',
        value: '',
        name: '数字名片',
        typeName: '数字名片',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1eed356-7898-9fg58bd21c',
        parentInfo: {
          id: 'e21906b7-c81f-4a73-aa76-c8cb65895e22',
          name: '基础信息',
        },
      },
      {
        type: 'ElectronicCard',
        vModel: 'electronicCard',
        value: false,
        name: '显示数字名片',
        typeName: '显示数字名片',
        tip: '开启后，会将个人数字名片显示出来。其他平台成员可收藏你的数字名片。',
        placeholder: '请输入',
        lineType: 'noBorder', // 线型类型: noBorder 无边框, border 有边框
        required: false, // 是否必填
        // passiveRelevance: false,
        disabled: true, // 是否禁用、是否可编辑
        isShow: true, // 是否显示
        id: '5b1e2344-66-778-88d21c',
        parentInfo: {
          id: 'e21906b7-c5yy-4a73-aa76-c8cuuu895e22',
          name: '基础信息',
        },
      },

    ],
  },
]);
const rules = {

};
const datas = ref(null);
const visible = ref(false);

let formData = reactive({
  name: '', // 名录照片

});

const switchColor = (intColor) => TagColors.find((item) => item.intColor == intColor);
const onGetLabelSettingInfo = async () => new Promise(async (resolve, reject) => {
  const [err, res] = await to(handleObj.getLabelFunc({}, props.teamId));
  if (err) return reject();
  if (res) {
    const { data } = res;
    console.log(data);
    resolve(data?.data);
  } else {
    reject();
  }
});

// 获取行业列表
const onGetIndustryList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      // console.log(activeAccount.value.teamRegion, "memberStorememberStore");

      result = await handleObj.getIndustryListAxios('', props.teamId);
      console.log(result, '行业11');
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

// 获取组织规模列表
const onGetTeamsSizeList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await handleObj.getTeamsSizeListAxios({}, props.teamId);
      console.log(result);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

const onSubmit = debounce(() => {
  if (currentTab.value === 'person') {
    runtimePersonRef.value.submitRun();
  } else if (currentTab.value === 'unit') {
    runtimeUnitRef.value.submitRun();
  }
}, 200);

const releaseRun_unit = async (data: any) => {
  console.log(data);
  if (data && data.free_form && data.free_form.length > 0) {
    const params = onConstructorParams(lodash.cloneDeep(data.free_form));
    console.log(params);
    // let result = null;
    const [err, res]: any = await to(handleObj.onPostDirectorySettingAxios(params));
    if (err) {
      const errMsg: any = err instanceof Error ? err?.message : err;
      MessagePlugin.error(errMsg);
      return;
    }
    // const {data} = res;
    MessagePlugin.success('保存成功');
    onClose();
    emits('onReload');
  }
};

const releaseRun_person = async (data: any) => {
  console.log(data);
  if (data && data.free_form && data.free_form.length > 0) {
    const params = onConstructorParams(lodash.cloneDeep(data.free_form));
    console.log(params);
    // let result = null;
    const [err, res]: any = await to(handleObj.onPostDirectorySettingAxios(params));
    if (err) {
      const errMsg: any = err instanceof Error ? err?.message : err;
      MessagePlugin.error(errMsg);
      return;
    }
    // const {data} = res;
    MessagePlugin.success('保存成功');
    onClose();
    emits('onReload');
  }
};

// 构造单位申请的参数
const onConstructorParams = (freeForm: Array<any>) => {
  const params = {
    directory_id: datas.value?.directory_id, // v2版本必传 名录标识
    directory_image: '',
    directory_image_values: [],
    name: '',
    hobby: '',
    team_logo: '',
    job: '',
    team_type: '',
    industry: '',
    industry_text: '',
    size: undefined,
    size_text: '',
    business: '',
    contact_switch: 0, // 联系人开关 1开启 0关闭
    business_card_switch: 0, // 数字名片 1开启 0关闭
    address_code: [],
    country: '',
    province: '',
    city: '',
    district: '',
    address: '',
    team_name: '',
    team_short_name: '',
    type: '',
    id: datas.value?.id,
    label_relation: {
      personal_value_ids: [],
      platform_value_ids: [],
      title_value_ids: [],
    },
  };
  const baseList = freeForm.filter((v: any) => v.type === 'BaseInfoMember');
  baseList.map((v: any) => {
    let origin: any = null;

    origin = v.origin.find((or: any) => or.vModel === 'titleT');
    if (origin) {
      params.label_relation.title_value_ids = origin.value?.map((v) => v.value_id) || [];
    }

    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      // params.label_relation.personal_value_ids = origin.value?.map(v=>v.value_id) || [];
      params.label_relation.personal_value_ids = origin.value ? [origin.value?.value_id] : [];
    }

    origin = v.origin.find((or: any) => or.vModel === 'tagPlatform');
    if (origin) {
      params.label_relation.platform_value_ids = origin.value ? [origin.value] : [];
    }

    origin = v.origin.find((or: any) => or.vModel === 'contactInfo');
    if (origin) {
      params.contact_switch = origin.value ? 1 : 0;
    }

    origin = v.origin.find((or: any) => or.vModel === 'electronicCard');
    if (origin) {
      params.business_card_switch = origin.value ? 1 : 0;
    }

    // 名录照片
    origin = v.origin.find((or: any) => or.vModel === 'nameLogo');
    if (origin && origin.value && origin.value.length > 0) {
      params.directory_image = origin.value[0].file_name;
      params.directory_image_values = origin.value;
    }
    // 代表人姓名
    origin = v.origin.find((or: any) => or.vModel === 'name');
    if (origin) {
      params.name = origin.value;
    }
    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === 'interest');
    if (origin) {
      params.hobby = origin.value;
    }

    // 组织logo
    origin = v.origin.find((or: any) => or.vModel === 'organizeLogo');
    if (origin && origin.value && origin.value.length > 0) {
      params.team_logo = origin.value[0].file_name;
    }

    // 组织名称
    origin = v.origin.find((or: any) => or.vModel === 'organizeName');
    if (origin) {
      params.team_name = origin.value;
    }

    // 所在单位岗位 - 新
    origin = v.origin.find((or: any) => or.vModel === 'unitJob');
    if (origin) {
      params.job = origin.value;
    }

    // 组织简称
    origin = v.origin.find((or: any) => or.vModel === 'organizeAbbrName');
    if (origin) {
      params.team_short_name = origin.value;
    }

    // 组织类型
    origin = v.origin.find((or: any) => or.vModel === 'organizeType');
    if (origin) {
      params.team_type = origin.value || '';
    }

    // 所在行业
    origin = v.origin.find((or: any) => or.vModel === 'industryType');
    if (origin) {
      params.industry = origin.value || undefined;
      const industryItem: any = getChidlren(origin.value, origin?.options);
      params.industry_text = industryItem?.name;
      origin.options = [];
    }
    // 组织规模
    origin = v.origin.find((or: any) => or.vModel === 'organizeScale');
    if (origin) {
      params.size = origin.value;
      const sizeItem: any = origin?.options?.find((v: any) => v.id === origin.value);
      params.size_text = sizeItem?.name;
      origin.options = [];
    }
    // 业务范围
    origin = v.origin.find((or: any) => or.vModel === 'business');
    if (origin) {
      params.business = origin.value;
    }
    // 组织地址
    origin = v.origin.find((or: any) => or.vModel === 'organizeAddress');
    if (origin) {
      params.address_code = origin.value || [];
      // address_code 拆分出国家省市区
      if (params.address_code && params.address_code.select) {
        params.address_code.select.forEach((item: any, itemIndex: number) => {
          if (itemIndex === 0) {
            params.country = item.name;
          } else if (itemIndex === 1) {
            params.province = item.name;
          } else if (itemIndex === 2) {
            params.city = item.name;
          } else if (itemIndex === 3) {
            params.district = item.name;
          }
        });
      }
      params.address = origin.address_value;
    }

    return v;

  });
  return params;
};

const onGetSetting = async () => {
  const result = await Promise.all([
    onGetLabelSettingInfo(), // 获取标签设置信息
    onGetIndustryList(), // 组织行业列表
    onGetTeamsSizeList(), // 组织规模
  ]);
  if (currentTab.value === 'unit') {
    onSetUnitForm(result);
  } else {
    onSetPersonForm(result);
  }
};

const onSetPersonForm = (result) => {
  const baseList = controls_person.value?.filter(
    (v: any) => v.type === 'BaseInfoMember',
  );
  baseList.map((v: any) => {
    let origin: any = null;
    origin = v.origin.find((or: any) => or.vModel === 'tagPlatform');
    if (origin) { // 平台标签
      // const item = origin.options.find((v: any) => v.value === origin.value);

      let options = result[0]?.platform?.value;
      options = options?.map((v) => {
        if (switchColor(v.colour)) {
          return {
            ...v,
            ...switchColor(v.colour),
          };
        }
        return v;

      });
      console.log(options);
      origin.options = options;
      origin.isShow = !!result[0]?.platform?.enable;
      origin.name = result[0]?.platform?.name;
      // origin.tip =  result[0]?.platform?.name;
      if (datas.value?.label_relation?.platform?.value?.length) {
        origin.value = datas.value?.label_relation?.platform?.value[0]?.value_id;
      } else {
        origin.value = '';
      }
    }

    // 个人标签
    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      origin.isShow = !!result[0]?.personal?.enable;
      origin.tip = result[0]?.personal?.explain;
      origin.name = result[0]?.personal?.name;
      // console.log('f', datas.value?.label_relation?.personal)
      if (datas.value?.label_relation?.personal?.value?.length) {
        const cos = origin.value = datas.value?.label_relation?.personal?.value[0];
        origin.value = {
          ...cos,
          ...switchColor(cos?.colour),
        };
      } else {
        origin.value = '';
      }
    }

    // 头衔
    origin = v.origin.find((or: any) => or.vModel === 'titleT');
    if (origin) {
      console.log('kaka', datas.value?.label_relation?.title?.value);
      // origin.isShow = result[0]?.personal?.enable ? true: false;
      origin.value = datas.value?.label_relation?.title?.value || [];
      origin.tip = result[0]?.title?.name;
    }

    origin = v.origin.find((or: any) => or.vModel === "nameLogo");
    if (origin) {
      if (datas.value?.directory_image_values?.length > 0) {
        origin.value = datas.value?.directory_image_values
      } else if (datas.value?.directory_image) {
        const nameLogoSrc = datas.value?.directory_image;
        const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
        const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
        const ck = [
          {
            file_name: nameLogoSrc,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
          },
        ];
        console.log(ck);
        origin.value = ck;
      } else {
        origin.value = [];
      }
    }


    // 姓名
    origin = v.origin.find((or: any) => or.vModel === 'name');
    if (origin) {
      origin.value = datas.value?.name || '';
    }

    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === 'interest');
    if (origin) {
      origin.value = datas.value?.hobby || '';
    }

    return v;
  });
};

const onSetUnitForm = (result) => {
  console.log(result);

  const baseList = controls_unit.value?.filter(
    (v: any) => v.type === 'BaseInfoMember',
  );

  baseList.map((v: any) => {
    let origin: any = null;
    origin = v.origin.find((or: any) => or.vModel === 'tagPlatform');
    console.log(origin);
    if (origin) { // 平台标签
      // const item = origin.options.find((v: any) => v.value === origin.value);

      let options = result[0]?.platform?.value;
      options = options?.map((v) => {
        if (switchColor(v.colour)) {
          return {
            ...v,
            ...switchColor(v.colour),
          };
        }
        return v;

      });
      console.log(options);
      origin.options = options;
      origin.isShow = !!result[0]?.platform?.enable;
      origin.name = result[0]?.platform?.name;
      if (datas.value?.label_relation?.platform?.value?.length) {
        origin.value = datas.value?.label_relation?.platform?.value[0]?.value_id;
      } else {
        origin.value = '';
      }

    }

    // 个人标签
    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      origin.isShow = !!result[0]?.personal?.enable;
      origin.tip = result[0]?.personal?.explain;
      origin.name = result[0]?.personal?.name;
      // console.log('f', datas.value?.label_relation?.personal)
      if (datas.value?.label_relation?.personal?.value?.length) {
        const cos = origin.value = datas.value?.label_relation?.personal?.value[0];
        origin.value = {
          ...cos,
          ...switchColor(cos?.colour),
        };
      } else {
        origin.value = '';
      }
    }

    // 是否显示联系人
    origin = v.origin.find((or: any) => or.vModel === 'contactInfo');
    if (origin) {
      origin.value = !!datas.value?.contact_switch;
      if(datas.value?.type === 3) {
        origin.isShow = false;
      } else {
        origin.isShow = true;
      }
    }

    // 对联系人做处理
    origin = v.origin.find((or: any) => or.vModel === 'divider' && or.tag === 'contactInfo');
    if (origin) {
      if(datas.value?.type === 3) {
        origin.isShow = false;
      } else {
        origin.isShow = true;
      }
    }
    // 是否显示联系人
    origin = v.origin.find((or: any) => or.vModel === 'electronicCard');
    if (origin) {
      origin.value = !!datas.value?.business_card_switch;
    }

    origin = v.origin.find((or: any) => or.vModel === "nameLogo");
    if (origin) {
      if (datas.value?.directory_image_values?.length > 0) {
        origin.value = datas.value?.directory_image_values
      } else if (datas.value?.directory_image) {
        const nameLogoSrc = datas.value?.directory_image;
        const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
        const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
        const ck = [
          {
            file_name: nameLogoSrc,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
          },
        ];
        console.log(ck);
        origin.value = ck;
      } else {
        origin.value = [];
      }
    }

    // 代表人姓名
    origin = v.origin.find((or: any) => or.vModel === 'name');
    if (origin) {
      origin.value = datas.value?.name || '';

      if (props.origin === originType.CBD) {
        origin.name = '负责人';
      } else if(props.origin === originType.Member && datas.value?.type === 3) {
        origin.name = '姓名';
      } else {
        origin.name = '代表人姓名';
      }
    }

    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === 'interest');
    if (origin) {
      origin.value = datas.value?.hobby || '';
      if(datas.value?.type === 3) {
        origin.isShow = false;
      }
    }

    // 行业选择
    origin = v.origin.find((or: any) => or.vModel === 'industryType');
    if (origin) {
      // origin.code = 86;
      origin.options = result[1];
      // industryListData.value = result[1];
      origin.value = datas.value?.industry;
    }

    // 组织规模
    origin = v.origin.find((or: any) => or.vModel === 'organizeScale');
    if (origin) {
      origin.options = result[2];
      // sizeListData.value = result[2];
      origin.value = datas.value?.size;
    }

    // 组织类型选择
    origin = v.origin.find((or: any) => or.vModel === 'organizeType');
    if (origin) {
      origin.options = optionsOrganizeType;
      origin.value = datas.value?.team_type || '';
    }

    // 组织岗位
    origin = v.origin.find((or: any) => or.vModel === 'unitJob');
    if (origin) {
      origin.value = datas.value?.job;
    }

    // 业务范围
    origin = v.origin.find((or: any) => or.vModel === 'business');
    if (origin) {
      origin.value = datas.value?.business;
    }

    // 组织地址
    origin = v.origin.find((or: any) => or.vModel === 'organizeAddress');
    if (origin) {
      origin.value = datas.value?.address_code || [];
      origin.address_value = datas.value?.address_detail;
    }

    // 个人标签
    origin = v.origin.find((or: any) => or.vModel === 'tagPerson');
    if (origin) {
      origin.isShow = !!result[0]?.personal?.enable;
      // console.log('f', datas.value?.label_relation?.personal)
      if (datas.value?.label_relation?.personal?.value?.length) {
        const cos = origin.value = datas.value?.label_relation?.personal?.value[0];
        origin.value = {
          ...cos,
          ...switchColor(cos?.colour),
        };
      } else {
        origin.value = '';
      }
    }

    // 头衔
    origin = v.origin.find((or: any) => or.vModel === 'titleT');
    if (origin) {
      console.log('kaka', datas.value?.label_relation?.title?.value);
      // origin.isShow = result[0]?.personal?.enable ? true: false;
      origin.value = datas.value?.label_relation?.title?.value || [];
    }

    origin = v.origin.find((or: any) => or.vModel === 'organizeName');
    if (origin) {
      // origin.value = datas.value?.team_logo;
      origin.value = datas.value?.team_name || '';
    }

    origin = v.origin.find((or: any) => or.vModel === 'organizeAbbrName');
    if (origin) {
      // origin.value = datas.value?.team_logo;
      origin.value = datas.value?.team_short_name || '';
    }

    origin = v.origin.find((or: any) => or.vModel === 'organizeLogo');
    if (origin) {
      // origin.value = datas.value?.team_logo;
      if (datas.value?.team_logo) {
        const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(datas.value?.team_logo);
        const origin_name = datas.value?.team_logo ? datas.value?.team_logo.substring(datas.value?.team_logo.lastIndexOf('/') + 1) : '';
        const ck = [
          {
            file_name: datas.value?.team_logo,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: logoRes?.length > 1 ? logoRes[1] : '',
          },
        ];
        console.log(ck);
        origin.value = ck;
      } else {
        origin.value = [];
      }
    }

    return v;
  });

};

const onSave = debounce(() => { });

const onOpen = (data?: any) => {
  datas.value = data;
  console.log(datas.value?.directory_image_values)
  if (data?.type === 1 || data?.type === 3) {
    currentTab.value = 'unit';
  } else {
    currentTab.value = 'person';
  }

  onGetSetting();

  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

// :deep(.t-form__item) {
//   margin-right: 0;
//   margin-bottom: 0 !important;
// }
:deep(.detail-control) {
  margin-bottom: 16px !important;
  margin-top: 0 !important;
}

:deep(.t-form__label) {
  white-space: wrap !important;
}

:deep(.t-popup) {
  z-index: 2501;
}

.searchForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  // gap: 24px;
  &-item {
    min-width: 324px;
    width: 100%;
  }
}

.t-alert--info {
  padding: 8px 16px;
}

.form {
  // margin-top: 10px;
}

// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;

  .tips {
    font-size: 14px;

    font-weight: 400;
    color: #717376;
  }
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }</style>
<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

.createUpdate {
  .toBody {
    // height: 68vh;
    // overflow: auto;
    // padding-bottom: 120px;
  }

  .t-dialog__header {
    padding: 0 24px;
  }

  .t-dialog__footer {
    padding: 0 24px;
  }

  .t-dialog--default {
    padding-left: 0;
    padding-right: 0;
  }
}

.detail-control {
  margin-top: 16px;
  margin-bottom: 4px;

  .lable {
    display: flex;
    align-items: center;

    // &::before {
    //   content: " ";
    //   width: 2px;
    //   height: 14px;
    //   background: #2069e3;
    //   border-radius: 2px;
    //   // position: absolute;
    //   left: 0;
    //   top: 2px;
    // }
    .line {
      width: 2px;
      height: 14px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);

      border-radius: 2px;
      margin-right: 10px;
    }

    .text {
      font-size: 14px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      height: 22px;
      line-height: 24px;
    }
  }

  .value {
    width: 100%;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    margin-top: 4px;
  }
}
</style>
