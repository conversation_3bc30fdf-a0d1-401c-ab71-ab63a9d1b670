<template>
  <AppCard
    :style="'position:relative;width:360px;'"
    :card-height="'475px'"
    @click="handleDetail"
  >
    <AppCardHeader
      :theme="getHeaderTheme(contentExtra.scene, contentExtra)"
    >
      <div class="flex-1 flex flex-row justify-between items-center">
        <span>{{ content?.title }}</span>
        <template v-if="isExamine">
          <div class="flex flex-row items-center gap-1 overflow-hidden h-[26px]">
            <t-loading v-if="loading" size="small" />
            <span v-else :class="getHeaderStatusClass(nowStatus) + ' flex px-[4px] py-[2px] justify-center items-center gap-1 text-[12px] text-[#828DA5]'"
            >
              {{
                nowStatus === 0 ? '待审核' :
                nowStatus === 1 ? '已通过' :
                nowStatus === 2 ? '已下架' :
                nowStatus === 3 ? '已拒绝' :
                nowStatus === 4 ? '已退会' :
                ''
              }}
            </span>
          </div>
        </template>
      </div>
    </AppCardHeader>
    <AppCardBody @click="">
      <div class="flex flex-col gap-[8px]">
        <div v-for="item in content?.body" :key="item.key"
        class="flex gap-[16px] flex-row"
        >
          <div class="w-[88px] flex-shrink-0 color-[#828DA5] text-[14px] leading-[20px]">{{ item.key }}</div>
          <div class="color-[#1D2129] text-[14px] w-0 flex-grow-2 break-all">{{ item.value }}</div>
        </div>
      </div>
    </AppCardBody>
    <template v-if="contentExtra.scene !== SceneType.IDLE_CHANNEL_DEL">
      <div class="h-1 bg-[#ECEFF5] mb-16 ml-16 mr-16" />
      <AppCardFooter>
        <Button v-if="contentExtra.scene === SceneType.IDLE_CHANNEL_EXAMINE_PENDING" class="w-full fw-600" variant="outline"
          :theme="nowStatus === 0 ? 'primary' : 'default'"
          @click="handleExamine"
        >
          {{ nowStatus === 0 ? '审核' : '查看详情' }}
        </Button>
        <Button v-else class="w-full fw-600" variant="outline"
          @click="handleDetail"
        >
          查看详情
        </Button>
      </AppCardFooter>
    </template>
  </AppCard>
</template>

<script lang="ts" setup>
import { onMounted, computed, PropType, onUnmounted, ref, watch } from 'vue';
import { Button, MessagePlugin } from 'tdesign-vue-next';
import lodash from 'lodash';
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter } from '../../MessageAppCard';
import { i18nt } from "@/i18n";
import { MessageToSave } from 'customTypes/message';
import jssdk from '@lynker-desktop/web';
import { SceneType, getHeaderTheme } from './constant';
import LynkerSDK from '@renderer/_jssdk';
import { getIdleGoodsStatusApi } from '@renderer/api/im/api';
const loading = ref(false);

const isShowMessage = ref(false);

const handleMessage = lodash.debounce(() => {
  if (isShowMessage.value) {
    return;
  }
  isShowMessage.value = true;
  MessagePlugin.warning({
    content: '请前往移动端进行查看和操作',
    onClose: () => {
      isShowMessage.value = false;
    },
  });
}, 500);

const handleExamine = () => {
  if (SceneType.IDLE_PUBLISH_EXAMINE_PASS === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_EXAMINE_REFUSE === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_ON === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_OFF === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_DEL === contentExtra.value?.scene
  ) {
    handleMessage();
    return;
  }
  const teamId = contentExtra.value?.extend?.team_id;
  const channelId = `${contentExtra.value?.extend?.channel_id}`;
  const channelStatus = `${contentExtra.value?.extend?.goods_status}`;
  const url = LynkerSDK.getH5UrlWithParams(`/shop/index.html#/Idle-goods-management-detail`, {
    teamId,
    channelId,
    channelStatus,
  });
  jssdk.workBench_openTabForWebview({
    url,
    title: '数字高校',
    path_uuid: `Idle-goods-management-detail-${teamId}-${channelId}`,
    teamId,
    query: {
      teamId,
      channelId,
      channelStatus,
    },
  });
  console.log('handleExamine', teamId, url);
};
const handleDetail = () => {
  if (SceneType.IDLE_PUBLISH_EXAMINE_PASS === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_EXAMINE_REFUSE === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_ON === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_OFF === contentExtra.value?.scene ||
    SceneType.IDLE_PUBLISH_DEL === contentExtra.value?.scene
  ) {
    handleMessage();
    return;
  }
  const teamId = contentExtra.value?.extend?.team_id;
  const channelId = `${contentExtra.value?.extend?.channel_id}`;
  const channelStatus = `${contentExtra.value?.extend?.goods_status}`;
  const url = LynkerSDK.getH5UrlWithParams(`/shop/index.html#/Idle-goods-management-detail`, {
    teamId,
    channelId,
    channelStatus,
  });
  jssdk.workBench_openTabForWebview({
    url,
    title: '数字高校',
    path_uuid: `Idle-goods-management-detail-${teamId}-${channelId}`,
    teamId,
    query: {
      teamId,
      channelId,
      channelStatus,
    },
  });
  console.log('handleDetail', teamId, url);
};

const props = defineProps({
  data: { type: Object as PropType<MessageToSave>, default: null },
  activityContentType: { type: String, default: 'APP_ACTIVITY' }
});

type IdleContentExtra = {
    template: string; // normal
    type: string;
    scene: number;
    header: any;
    content: {
        title: string;
        body: {
            key: string;
            value: string;
        }[];
    };
    extend: {
        notice_status: number;
        channel_id: number;
        apply_id: number;
        team_id: string;
        goods_id: number;
        goods_status: number;
    };
};

const contentExtra = computed<IdleContentExtra>(() => props?.data?.contentExtra?.data);
const content = computed(() => contentExtra.value?.content);
const isExamine = computed(() => contentExtra.value?.scene === SceneType.IDLE_CHANNEL_EXAMINE_PENDING);
const nowStatus = ref(contentExtra.value?.extend?.goods_status);
const getHeaderStatusClass = (status: number) => {
  return status === 0 ? 'bg-[#FFE3E3] text-[#FF4D4F]' :
    status === 1 ? 'bg-[#E3F5E3] text-[#52C41A]' :
    'bg-[#F5E3E3] text-[#FF4D4F]';
};

const getIdleGoodsStatus = lodash.debounce(async () => {
  loading.value = true;
  try {
    if (isExamine.value) {
      const res = await getIdleGoodsStatusApi(`${contentExtra.value?.extend?.channel_id}`, contentExtra.value?.extend?.team_id);
      const { channel_status } = res?.data?.data;
      nowStatus.value = channel_status;
    }
  } catch (error) {
    console.log(error, 'error');
  } finally {
    loading.value = false;
  }
}, 500);

watch(nowStatus, (newVal) => {
  getIdleGoodsStatus();
  console.log(newVal, 'newVal');
});

onMounted(() => {
  console.log(props, 'props.data');
  getIdleGoodsStatus();
})
onUnmounted(() => {

});

</script>
