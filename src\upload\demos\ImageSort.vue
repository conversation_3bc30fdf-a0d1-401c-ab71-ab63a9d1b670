/**
 * title: 多图排序
 * description: 支持设置最大上传数量，可支持多张图片上传，并支持拖拽排序。设置 `autoSort` 为 `true` 后，排序会自动更新 v-model 绑定的值，无需手动处理 `sort` 事件。
 */

<template>
  <div class="demo-wrap">
    <!-- 自动排序模式 -->
    <div class="demo-section">
      <h4>自动排序模式</h4>
      <p>拖拽图片来测试自动排序功能</p>
      <RkUploadImage
        v-model="imgUrls1"
        root-dir="换成上传OSS根目录"
        :max-count="5"
        sortable
        auto-sort
      />
      <div class="url-list">
        <p>当前图片列表：</p>
        <ol>
          <li
            v-for="(url, index) in imgUrls1"
            :key="index"
          >
            {{ getImageName(url) }}
          </li>
        </ol>
      </div>
    </div>

    <!-- 手动排序模式（向后兼容） -->
    <div class="demo-section">
      <h4>手动排序模式</h4>
      <p>拖拽图片，手动处理排序事件</p>
      <RkUploadImage
        v-model="imgUrls2"
        root-dir="换成上传OSS根目录"
        :max-count="5"
        sortable
        @sort="handleSort"
      />
      <div class="url-list">
        <p>当前图片列表：</p>
        <ol>
          <li
            v-for="(url, index) in imgUrls2"
            :key="index"
          >
            {{ getImageName(url) }}
          </li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkUploadImage } from '@rk/unitPark';

const imgUrls = [
  'https://tdesign.gtimg.com/demo/demo-image-1.png',
  'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/shop_apply_bg.png',
  'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/upload_logo_example.png',
];

// 自动排序模式的图片
const imgUrls1 = ref([...imgUrls]);
// 手动排序模式的图片
const imgUrls2 = ref([...imgUrls]);

// 手动排序处理函数
const handleSort = (oldIndex: number, newIndex: number) => {
  console.log('手动排序 - 收到 sort 事件:', { oldIndex, newIndex });
  const [removed] = imgUrls2.value.splice(oldIndex, 1);
  imgUrls2.value.splice(newIndex, 0, removed);
};

const getImageName = (url: string) => url.split('/').pop()?.split('?')[0];
</script>

<style scoped lang="less">
.demo-wrap {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.demo-section {
  h4 {
    &:first-of-type {
      margin-top: 0;
    }
  }
}

.url-list {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
