<template>
  <div class="RK-Form">
    <TForm
      v-if="list && list.length > 0"
      v-bind="props.attrs"
      class="rForm"
      ref="rForm"
      :rules="rules"
      :data="formData"
      @submit="onSubmit"
    >
      <TFormItem
        v-for="(item, index) in list"
        :key="item.name+index"
        :label="item.label"
        :name="item.name"
        :initial-data="item.defaultInfo"
      >
        <template v-if="item.type === 'select'">
          <TSelect
            v-bind="item?.attrs"
            v-model="formData[item.name]" 
            :popup-props="{
              overlayClassName: 'rk-select-form-popup'
            }" 
            @change="e => onChange(e, item)"
          />
        </template>
        <template v-else-if="item.type === 'input'">
          <TInput
            v-bind="item?.attrs"
            v-model="formData[item.name]"
            @change="e => onChange(e, item)"
          />
        </template>
        <template v-else-if="item.type === 'textarea'">
          <TTxtarea
            v-bind="item?.attrs"
            v-model="formData[item.name]"
            @change="e => onChange(e, item)"
          />
        </template>
        <template v-else-if="item.type === 'date'">
          <TDatePicker
            style="width: 100%;"
            v-bind="item?.attrs"
            v-model="formData[item.name]"
            @change="e => onChange(e, item)"
          />
        </template>
        <template v-else-if="item.type === 'dateRange'">
          <TDateRangePicker
           style="width: 100%;"
            v-bind="item?.attrs"
            v-model="formData[item.name]"
            @change="e => onChange(e, item)"
          />
        </template>
        <template v-else-if="item.type === 'component'">
          <component
            :is="item?.component"
            v-bind="item?.attrs"
            v-model="formData[item.name]"
            @change="e => onChange(e, item)"
          />
        </template>
        <template v-if="item.type === 'slot' && $slots[item?.name]">
          <slot :name="item?.name" :data="formData[item.name]"></slot>
        </template>
      </TFormItem>
    </TForm>
  </div>
</template>

<script setup lang="ts" name="RK-Form">
import { computed, onMounted, ref, watch } from 'vue';
import {
  Form as TForm,
  FormItem as TFormItem,
  Input as TInput,
  Textarea as TTxtarea,
  Select as TSelect,
  DatePicker as TDatePicker,
  DateRangePicker as TDateRangePicker,
} from 'tdesign-vue-next';
import { FormItemType } from './type';

const props = defineProps({
  attrs: {
    type: Object,
    default: {}
  },
  defaultInfo: {
    type: Object,
    default: {}
  },
  list: {
    type: Array<FormItemType>,
    default: []
  },
  change: {
    type: Function,
    default: () => {}
  }
});
const emit = defineEmits(['submit', 'change']);
const listDefault = ref(props.list||[]);
const rules = ref({});
const formData = ref({});
const rForm = ref(null);

watch(() => props.list, (newVal) => {
  console.log('watch props.list', newVal);
  listDefault.value = newVal;
});


const list = computed(() => {
  return initForm();
})

const onChange = (e, item) => {
  console.log('onChange', e, item);
  emit('change', item, e);
};
const onSubmit = (data) => {
  // console.log('onSubmit', data);
  emit('submit', data, formData.value);
};

const submit = () => {
  rForm.value.submit();
};

// const validate = (name) => {
//   console.log('validate', name, formData.value);
//     // 手动触发表单校验
//   if (rForm.value) {
//     rForm.value
//       .validate(name)
//       .then((validateResult) => {
//         if (validateResult === true) {
//           console.log('用户名校验通过');
//         } else {
//           console.log('用户名校验不通过，结果：', validateResult);
//         }
//       }).catch((error) => {
//         console.error('校验失败：', error);
//       });
//   }
// };

const getForm = () => {
  return rForm.value
};

const initForm = () => {
  const _list = []
  const setInfo = (v) => {
  // console.log('setInfo v', v);
    rules.value[v.name] = v.rules;
    if (v.type === 'dateRange') {
      formData.value[v.name] = [v.value?.[0], v.value?.[1]];
    } else {
      formData.value[v.name] = v.value;
    }
    _list.push(v)
  }
  listDefault.value.map((v:FormItemType) => {
    if (v.isShow) {
      if (typeof v.isShow === 'function' && v.isShow()) {
        setInfo(v);
      }
    } else {
      setInfo(v);
    }
  })
  return _list;
};

// 更新校验规则方法
const updateRules = (key, val) => {
  rules.value[key] = val;
  return rForm.value.validate({
    fields: [key]
  }).then((result) => {
    return result;
  });
}

const setformData = (data) => {
  if (!data) return;
  const isClear =  data && Object.keys(data).length <= 0;
  const _data = isClear ? Object.keys(formData.value) :  Object.keys(data)
  // console.log('setformData', isClear, data, _data);
  _data.forEach(key => {
    formData.value[key] = isClear ? '' : data[key];
  });
}

defineExpose({
  formData: formData.value,
  updateRules,
  setformData,
  initForm,
  submit,
  getForm
})
</script>

<style lang="less">
.RK-Form{
  .rForm {
    .t-form__item {
      .t-form__label {
        height: 22px;
        line-height: 22px;
        min-height: 22px;
        margin-bottom: 8px;
        white-space: nowrap !important;
      }
    }
    .t-textarea__inner{
      height: 146px;
    }
    .t-textarea__info_wrapper {
      position: absolute;
      bottom: 5px;
      right: 20px;
    }
  }
}
.rk-select-form-popup {
  .t-select__dropdown-inner {
    .t-select__list {
      padding: 8px !important;
      .t-select-option.t-is-selected:not(.t-is-disabled) {
        color: #4d5eff;
      }
    }
  }
}
</style>
