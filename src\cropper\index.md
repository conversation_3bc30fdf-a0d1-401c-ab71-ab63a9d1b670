# Cropper

## RkCropperDialog

`RkCropperDialog` 是一个基于 `RkImageCropper` 的图片裁剪对话框组件，用于将裁剪的图片上传到 OSS 服务器

<code src="./demos/dialog.vue"></code>

### Props

<API id="RkCropperDialog" type="props"></API>

:::info{title=options}
`options` 属性为 `Cropper.js` 配置项。具体请参见 [Cropper.js 文档](https://github.com/fengyuanchen/cropperjs/tree/main#options)
:::

### Events

<API id="RkCropperDialog" type="events"></API>

### Instance Methods

<API id="RkCropperDialog" type="imperative"></API>

---

## RkImageCropper

`RkImageCropper` 是一个基于 `Cropper.js` 的基础图片裁剪组件，主要用于：

- **图片裁剪**：支持 `Cropper.js` 的基本操作
- **灵活配置**：支持自定义裁剪行为
- **裁剪形状**：支持圆形或椭圆形裁剪
- **事件处理**：支持裁剪过程中的各种事件回调

<code src="./demos/index.vue"></code>

### Props

<API id="RkImageCropper" type="props"></API>

:::info{title=options}
`options` 属性为 `Cropper.js` 配置项。具体请参见 [Cropper.js 文档](https://github.com/fengyuanchen/cropperjs/tree/main#options)
:::

### Events

<API id="RkImageCropper" type="events"></API>

### Instance Methods

<API id="RkImageCropper" type="imperative"></API>
