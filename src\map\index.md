# Map

本目录下包含百度地图相关的业务组件。

## RkBaiduMapRouterCard

`RkBaiduMapRouterCard` 输入终点起点出现路径
<code src="./demos/MapRoute.vue"></code>

### Props

| 属性名 | 说明                                   | 类型   | 默认值                              |
| ------ | -------------------------------------- | ------ | ----------------------------------- |
| width  | 地图宽度                               | number | 448                                 |
| height | 地图高度                               | number | 252                                 |
| start  | 起点坐标，包含经度(lng)和纬度(lat)     | object | { lng: 116.310791, lat: 40.003419 } |
| end    | 终点坐标，包含经度(lng)和纬度(lat)     | object | { lng: 116.386419, lat: 40.003519 } |
| rider  | 骑手位置坐标，包含经度(lng)和纬度(lat) | object | { lng: 0, lat: 0 }                  |

## RkBaiduMapCard

`RkBaiduMapCard` 是一个百度地图卡片组件，用于展示地图信息，支持地图缩放、移动、标记点等操作。

<code src="./demos/Basic.vue"></code>

### Props

<API id="RkBaiduMapCard" type="props"></API>

### Events

<API id="RkBaiduMapCard" type="events"></API>

### Instance Methods

<API id="RkBaiduMapCard" type="imperative"></API>

---

## RkBaiduMapSelector

`RkBaiduMapSelector` 是一个百度地图选择器弹窗组件，用于选择地图上的位置，并返回选择的经纬度等地址信息。

<code src="./demos/Selector.vue"></code>

### Props

<API id="RkBaiduMapSelector" type="props"></API>

### Events

<API id="RkBaiduMapSelector" type="events"></API>

## IP 定位到当前城市

```ts
import { useBaiduMap, BMap, mapOptions } from '@rk/unitPark';

// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({
  onIPLocation() {
    console.log(location.value);
  },
  onPointGeocoder() {
    console.log(markerInfo.value);
  },
});
```

:::warning{title=注意}
需要模板中声明 `BMap` 组件并设置高度为 0 不可见，`useIpLocation`、`usePointGeocoder` 等 hooks 依赖于 `BMapGL`，所以需要在 Map 组件初始化完毕后调用。
:::

```bash
<BMap :api-url="BAIDU_API_URL" :height="0" v-bind="mapOptions" @initd="onMapInit" />
```

如果需要在指定流程中触发获取 IP 定位，可声明一个标识符，在 `onIPLocation` 或 `onPointGeocoder` 回调中判断标识符以执行特定操作。

```ts
// 地图 IP 定位
const { location, markerInfo, onMapInit, getIPLocation, getPointGeocoder } =
  useBaiduMap({
    onIPLocation() {
      if (refreshFlag.value) {
        getPointGeocoder(location.value.point);
      }
    },
    onPointGeocoder() {
      if (refreshFlag.value) {
        refreshPosition();
      }
    },
  });

// 刷新标识符
const refreshFlag = ref(false);

// 刷新位置
const refreshPosition = () => {
  // do something
  // ... ...

  // 别忘了重置标识符
  refreshFlag.value = false;
};
```
