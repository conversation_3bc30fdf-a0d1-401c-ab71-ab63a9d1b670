import {
  activitiesRequest,
  lssClientOrganizeMemRequest as client_orgRequest,
  lssSquareMemRequest,
  ringkolRequestForExport,
} from "@renderer/utils/apiRequest";
import { DigitalPlatformTypeSquare } from "@renderer/views/digital-platform/utils/constant";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";

// 会员管理
const Api = {
  commonTeamsApi: "/common/teams", // 获取成员的组织列表
  workShopAppTotal: "/association/total", // 应用消息统计
  teams: "/teams", // 获取组织详情

  GetMemberSettingApi: "/association/setting", // 获取设置
  MemberSettingApplyApi: "/association/setting/apply", // 入会设置
  MemberSettingRemindApi: "/association/setting/remind", // 提醒设置
  GetMemberHomeListApi: "/association/team", // 获取商协会首页列表数据

  // 会员职务
  MemberJobsApi: "/association/jobs",
  SortMemberJobsApi: "/association/jobs/sort", // 排序
  FastMemberJobs: "/association/jobs/fast", // 快速创建职务列表 快速创建职务

  // 正式会员
  GetMemberLevelApi: "/association/level-member", // 获取会员级别分组成员列表
  RegularMemberApi: "/association",
  MemberRelationApi: "/association/relation", // 选择代表人
  MemberCardsApi: "/association/cards", // 会员卡片列表
  activateSms: "/association/activateSms", // 会员激活发送短信

  // 入会申请
  GetMemberApplyListApi: "/association/apply", // 列表
  GetMemberApplyDetailApi: "/association/apply", // 详情
  GetMemberApplyLinkApi: "/association/apply/link", // 邀请入会
  GetMemberApplyLinkDetailApi: "/association/apply/link-detail", // 获取邀请链接详情
  PostMemberApplyAgreeApi: "/association/apply/agree", // 审核通过
  PostMemberApplyRejectApi: "/association/apply/reject", // 驳回申请
  GetMemberApplyTotalApi: "/association/apply/total", // 入会申请数量
  GetMemberApplyRecordApi: "/association/apply/list-openId", // 获取用户的申请入会列表 、申请记录

  // 应用管理
  MemberAdminApi: "/association/admin", // get管理员列表、post添加管理员
  CheckIsAdminApi: "/association/admin/checkIsAdmin", // 判断当前用户是否为管理员
  ChangeAdminApi: "/association/admin/change", // 转移超级管理员
  DeleteAdminApi: "/association/admin", // /member/admin/{id} // 删除管理员
  AppStaffApi: "/association/admin/app-staff", // 应用可用成员列表

  // 激活会员
  GetMemberActivatesApi: "/association/activate", // 激活会员列表、激活会员详情/member/activate/{id}
  GetMemberActivateTotalApi: "/association/activate/total", // 激活会员数量
  PostAgreeMemberActivateApi: "/association/activate/agree", // 审核通过
  PostRejectMemberActivateApi: "/association/activate/reject", // 驳回申请

  // 会费记录
  FeeRecordApi: "/association/order",

  // 会员联系人
  MemberContactApi: "/association/contact", //
  // AddOrganizeApi: "/association/contact/create-staff", // 添加组织成员
  AddOrganizeApi: '/digit212/association/createPlatformStaff',

  // 商协会会员广场号列表
  MemberSquaresApi: "/square/v1/digitalBusiness/digitalBusinessMemberSquares",
  SquaresApi: "/v1/digital_business/squares", // 商协会已开通广场号列表

  // 商机展示列表
  MemberNicheApi: "/niche/business/list",

  // 会员名录
  MemberNameDirectoryApi: "/association/directory/all", // 会员名录列表
  MemberNameSearchApi: "/member-directory/search", // 会员名录搜索
  MemberMoreApi: "/member-directory/more",

  MemberNameDetail: "/association/directory",

  // 获取广场号
  SharedSquareApi: "/v1/shared/square", // 获取广场号
  IndividualSquareApi: "/v1/individual_square", // 获取个人广场号（未开通时自动开通)
  MemberSettingGroupApi: "/association/setting/auto_create_group", // 设置群

  memberSettingGroupCreate: "/association/setting/create_group", // 创建群

  DynamicsListApi: "/associationDynamic", // 获取平台动态列表
  DynamicsApplyListApi: "/associationDynamic/apply", // 获取平台申请列表
  DynamicsApplyAgreeApi: "/associationDynamic/apply/agree", // 同意
  DynamicsApplyRejectApi: "/associationDynamic/apply/refuse", // 拒绝
  adoptSetTop: "/associationDynamic/adoptSetTop", // 置顶
  adoptSetCancelTop: "/associationDynamic/cancelAdoptSetTop", // 取消置顶
  adoptDel: "/associationDynamic/adoptDel", //删除
  getFrontDynamicList: "/associationDynamic/frontList", // 获取前台动态列表
  pendingReviewTotal: "/associationDynamic/pendingReviewTotal", // 待审核数量-动态列表

  // 数字城市1.1
  getGroupOnlyList: "/association-group/only-group-list", // 获取分组列表
  getAllGroupList: "/association-group/all-list", // 获取分组列表
  setGroup: "/association/set-group", // 设置分组

  getGovernmentGov: "/association/gov-auth", // 获取城市联络人/城市管理员/组织管理员 权限
  delGroupGov: "/association/setting/del-group", // 解散群
  getDirectoryGroup: "/association/directory/list-with-group", // 获取带分组的名录

  isEmptyContacts: "/association-group/is-empty-contacts", // 判断是否有联络人（平台交流按钮判断）

  onGetGovGroupCommunication: "/association-group/platform-communication",

  saveLabelRelationApi: "/association/label/value/relation", // 会员名录保存标签值、平台值、头衔值
  getLabelSettingApi: '/association/label/setting', // 获取标签设置
  getSetDirectoryApi: '/association/directory/setDirectory', // 保存目录设置

  setContactUp: '/association-group/contacts/set-group', // 联络人设置信息

  // 联系人列表搜索，选人
  getContactListSelectOne: '/association/contact/search-list',
  transferByAdmin: '/association/delegate/transfer-by-admin', // 管理端转移代表人
  transferBySelf: '/association/delegate/transfer-by-self', // 平台端转移代表人
  applyReviewApi: '/association/contact/apply-review', // 审核 联系人申请
  setDisableSquareMarket: '/association/setting/disable-square-market', // 关闭广场号主页市场入口
};

// 关闭广场号主页市场入口
export function onSetDisableSquareMarketAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.setDisableSquareMarket,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 审核 联系人申请
export function onApplyReviewAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.applyReviewApi,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 管理端转移代表人
export function onTransferByAdminAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.transferByAdmin,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}
// 平台端转移代表人
export function onTransferBySelfAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.transferBySelf,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function onGetContactListSelectOneAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getContactListSelectOne,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}


export function onSetContactUpAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.setContactUp,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}


export function onPostDirectorySettingAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.getSetDirectoryApi,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function onGetLabelSettingAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getLabelSettingApi,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function saveLabelRelationAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.saveLabelRelationApi,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}



export function onGetGovGroupCommunicationAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.onGetGovGroupCommunication,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function isEmptyContactsAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.isEmptyContacts,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 获取带分组的名录
export function getDirectoryGroupAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getDirectoryGroup,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 解散群
export function delGroupGovAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.delGroupGov,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 获取城市联络人/城市管理员/组织管理员 权限
export function getGovernmentGovAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getGovernmentGov,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function setGroupAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.setGroup,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 获取分组列表
export function getAllGroupListAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.getAllGroupList,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function getGroupOnlyListAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getGroupOnlyList,
    params: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 获取动态待审核数量
export function dynamicCountAppAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.pendingReviewTotal,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

// 列表-平台动态列表
export function getDynamicsApplyListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.DynamicsApplyListApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 获取前台动态列表
export function getFrontDynamicsListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.getFrontDynamicList,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 列表-平台动态列表
export function getDynamicsListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.DynamicsListApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 置顶
export function DynamicsTopAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.adoptSetTop,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 取消置顶
export function DynamicsCancelTopAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.adoptSetCancelTop,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 删除动态
export function DynamicsDelAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.adoptDel,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 申请同意
export function DynamicsApplyAgreeAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.DynamicsApplyAgreeApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 申请拒绝
export function DynamicsApplyRejectAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.DynamicsApplyRejectApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 创建平台群
export function memberSettingGroupCreateAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.memberSettingGroupCreate,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 群设置
export function memberSettingGroupAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.MemberSettingGroupApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 获取申请和应用总数
export function workShopAppAxios(data, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.workShopAppTotal,
    data: {
      ...data,
    },
    headers: {
      teamId,
    },
  });
}

export function getTeamsAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.teams,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 获取广场号
export function getSharedSquaresAxios(params?) {
  return lssSquareMemRequest({
    method: "get",
    url: Api.SharedSquareApi,
    params: {
      ...params,
    },
    // headers: {
    //   teamId: getAssociationTeamID(),
    // },
  });
}

// 获取个人广场号
export function getIndividualSquareAxios(params?, teamId?) {
  return lssSquareMemRequest({
    method: "get",
    url: Api.IndividualSquareApi,
    params: {
      ...params,
    },
    headers: {
      teamId,
    },
    // headers: {
    //   teamId: getAssociationTeamID(),
    // },
  });
}

export function getMemberNameMoreAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberMoreApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function getMemberNameSearchAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberNameSearchApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

export function getMemberNameDirectAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberNameDirectoryApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 获取商机展示列表
export function getMemberNichAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberNicheApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 商协会会员广场号列表
export function getMemberSquaresAxios(params?, teamId?) {
  return ringkolRequestForExport({
    method: "get",
    url: Api.MemberSquaresApi,
    params: {
      digital_platform_type: DigitalPlatformTypeSquare.Association,
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 商协会已开通广场号列表
export function getSquaresAxios(params?, teamId?) {
  return lssSquareMemRequest({
    method: "get",
    url: Api.SquaresApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 联系人-删除
export function delContactAxios(staffId, params?, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: `${Api.MemberContactApi}/${staffId}`,
    params: {
      ...params,
    },
    headers: {
      teamId,
    },
  });
}
// 获取会员联系人-详情
export function getMemberContactDetailAxios(staffId, params?) {
  return client_orgRequest({
    method: "get",
    url: `${Api.MemberContactApi}/${staffId}`,
    params: {
      ...params,
    },
  });
}

// 获取商协会首页列表数据GetMemberHomeListApi
export function getMemberHomeListAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberHomeListApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 获取-联系人列表
export function getMemberContactListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberContactApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 编辑联系人
export function patchContactorAxios(staffId, params, teamId?) {
  return client_orgRequest({
    method: "patch",
    url: `${Api.MemberContactApi}/${staffId}`,
    data: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 添加联系人
export function createMemberContactAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.MemberContactApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 添加组织成员
export function createOrganizeContactAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.AddOrganizeApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 会费记录-创建
export function createMemberOrderAxios(data) {
  return client_orgRequest({
    method: "post",
    url: Api.FeeRecordApi,
    data: {
      ...data,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}
// 会费记录-编辑
export function patchMemberOrderAxios(staffId, params) {
  return client_orgRequest({
    method: "patch",
    url: `${Api.FeeRecordApi}/${staffId}`,
    data: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 会费记录-列表
export function getMemberOrderListAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.FeeRecordApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}
// 会费记录-详情
export function getMemberOrderDetailAxios(staffId, params?) {
  return client_orgRequest({
    method: "get",
    url: `${Api.FeeRecordApi}/${staffId}`,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}
// 会费记录-删除
export function delMemberOrderAxios(staffId, params?) {
  return client_orgRequest({
    method: "delete",
    url: `${Api.FeeRecordApi}/${staffId}`,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 发送短信
export function sendActivateSmsAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.activateSms,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 正式会员
// 创建
export function createRegularAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.RegularMemberApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 编辑
export function patchRegularAxios(staffId, params, teamId?) {
  return client_orgRequest({
    method: "patch",
    url: `${Api.RegularMemberApi}/${staffId}`,
    data: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 详情-正式会员
export function getRegularDetailAxios(staffId, params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: `${Api.RegularMemberApi}/${staffId}`,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 列表-正式会员
export function getRegularListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.RegularMemberApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 列表-选择代表人列表
export function getMemberRelationAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberRelationApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
export function getMemberRelationAxios2(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: `/visitor/platformList?type=association`,
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 列表-会员卡片列表
export function getMemberCardsAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberCardsApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 邀请激活-正式会员
export function getRegularLinkAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: "/association/link",
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 会员续费-正式会员
export function renewalRegularAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association/renewal",
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 会员退会-正式会员
export function exitRegularAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association/exit",
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 下载模板-正式会员
export function downloadRegularAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: "/association/download",
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 导入-正式会员
export function importRegularAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association/import",
    headers: {
      "Content-Type": "multipart/form-data", // 设置请求头为multipart/form-data
      // 其他自定义请求头
      teamId: teamId || getAssociationTeamID(),
    },
    data: {
      ...data,
    },
  });
}
// 获取会员级别分组成员列表
export function getMemberLevelAxios(params?) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.GetMemberLevelApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 激活会员
// 审核通过
export function postAgreeMemberActivateAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.PostAgreeMemberActivateApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 驳回申请
export function postRejectMemberActivateAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.PostRejectMemberActivateApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 激活会员-详情
export function getMemberActivatesDetailAxios(staffId, params?) {
  return client_orgRequest({
    method: "get",
    // url: Api.GetMemberActivatesApi,
    url: `${Api.GetMemberActivatesApi}/${staffId}`,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 激活会员-列表
export function getMemberActivatesAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberActivatesApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 数量
export function getMemberActivateTotalAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberActivateTotalApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 入会申请

// 获取用户的申请入会列表
export function getMemberApplyRecordsAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberApplyRecordApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}
// 入会申请总数
export function getMemberApplyTotalAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberApplyTotalApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 审核通过
export function postMemberApplyAgreeAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.PostMemberApplyAgreeApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 驳回申请
export function postMemberApplyRejectAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.PostMemberApplyRejectApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 获取邀请链接详情
export function getMemberApplyLinkDetailAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberApplyLinkDetailApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 详情
export function getMemberApplyDetailAxios(staffId, params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: `${Api.GetMemberApplyDetailApi}/${staffId}`,
    params: {
      ...params,
    },
    data: {
      ...params
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 邀请入会
export function getMemberApplyLinkAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberApplyLinkApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 列表
export function getMemberApplyListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.GetMemberApplyListApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 会员职务排序
export function sortMemberJobsAxios(data) {
  return client_orgRequest({
    method: "post",
    url: Api.SortMemberJobsApi,
    data: {
      ...data,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}
// 快速创建职务
export function fastMemberJobsAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.FastMemberJobs,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 快速创建职务列表
export function fastMemberJobsListAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.FastMemberJobs,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 删除会员职务
export function deleteMemberJobsAxios(id) {
  return client_orgRequest({
    method: "delete",
    url: `${Api.MemberJobsApi}/${id}`,
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 获取名录详情
export function getMemberNameDetailAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberNameDetail + "/" + params.id,
    params: {
      ...params,
    },
    headers: {
      teamId,
    },
  });
}

// 获取会员职务列表
export function getMemberJobsListAxios(params?) {
  return client_orgRequest({
    method: "get",
    url: Api.MemberJobsApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 会员职务详情
export function getMemberJobsDetailAxios(staffId, params?) {
  return client_orgRequest({
    method: "get",
    url: `${Api.MemberJobsApi}/${staffId}`,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 编辑会员职务
export function editMemberJobsAxios(staffId, params) {
  return client_orgRequest({
    method: "patch",
    url: `${Api.MemberJobsApi}/${staffId}`,
    data: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 创建会员职务
export function createMemberJobsAxios(data) {
  return client_orgRequest({
    method: "post",
    url: Api.MemberJobsApi,
    data: {
      ...data,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 应用可用成员列表
export function getAppStaffAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.AppStaffApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 删除管理员
export function deleteAdminAxios(id, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: `${Api.DeleteAdminApi}/${id}`,
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 转移超级管理员
export function changeAdminAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.ChangeAdminApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 判断当前用户是否为管理员
// idStaff integer 内部员工ID
export function checkIsAdminAxios(data?, teamId?) {
  return client_orgRequest({
    method: "post",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.CheckIsAdminApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 添加管理员
export function addAdminAxios(data?, teamId?) {
  return client_orgRequest({
    method: "post",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.MemberAdminApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 应用管理, 列表
export function getMemberAdminAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.MemberAdminApi,
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// 获取成员的组织列表
export function getCommonTeamsAxios(params) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.commonTeamsApi,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 提醒设置
export function memberSettingRemindAxios(data) {
  return client_orgRequest({
    method: "post",
    url: Api.MemberSettingRemindApi,
    data: {
      ...data,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 入会设置
export function memberSettingApplyAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: Api.MemberSettingApplyApi,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function memberSettingDynamicsAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: `/association/setting/dynamics-auto`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}


// 获取设置
export function getMemberSettingAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    // url: `${Api.MemberInfoList}?${Qs.stringify(params)}`,
    url: Api.GetMemberSettingApi,
    params: {
      ...params,
      teamId: teamId || getAssociationTeamID(),
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
// export function marketClassifytree() {
//   return client_orgRequest({
//     method: "get",
//     url: "/market-classify/tree",
//   });
// }
// 获取行业列表
export function getIndustryListAxios(data, teamId?) {
  let area = JSON.parse(window.localStorage.getItem("profile")).area;
  if (data) {
    area = data;
  }
  // const area = JSON.parse(window.localStorage.getItem("profile")).area;

  return client_orgRequest({
    method: "get",
    // url: "/teams/industry",
    url: `/market-classify/tree?region=${area}`,
    // params: {
    //   ...params,
    // },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 组织规模列表
export function getTeamsSizeListAxios(params?: any, teamId?) {
  return client_orgRequest({
    method: "get",
    url: "/teams/size",
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 获取组织详情 teamId
export function getTeamsDetailAxios(params?: any) {
  return client_orgRequest({
    method: "get",
    url: "/teams",
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 获取部门列表
export function getOrganizeDepartmentListAxios(data?: any, teamId?) {
  return client_orgRequest({
    method: "get",
    url: "/departmentJob/departmentList",
    params: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

// 获取部门中岗位列表
export function getOrganizeDepartmentJobsListAxios(data?: any) {
  return client_orgRequest({
    method: "get",
    url: "/departmentJob/jobList",
    params: {
      ...data,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

// 获取指定组织的公开活动
export function eventsTeamId(data?: any) {
  return activitiesRequest({
    method: "get",
    url: `/v2/events/${data.teamId}`,
    params: {
      ...data,
    },
    headers: {
      teamId: data.teamId || getAssociationTeamID(),
    },
  });
}

export function getGovernmentStaffOpenid(teamId?) {
  return client_orgRequest({
    method: "get",
    url: "/association/staff-openid",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function governmentGroupList(teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/list",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function governmentGroupCreate(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/create",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}

export function governmentGroupDetail(id, teamId?) {
  return client_orgRequest({
    method: "get",
    url: `/association-group/detail/${id}`,
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function governmentGroupEdit(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/update",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}

export function governmentDissolve(id, teamId?, groupId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/exit-im",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data: { id, group_im: groupId },
  });
}

export function editSpecifiedFieldsGVR(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association/editSpecifiedFields",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}
export function editSpecifiedFieldsASS(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association/editSpecifiedFields",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}
export function getStaffsPlatform(teamId) {
  return client_orgRequest({
    method: "post",
    url: "/member/platform/getStaffsPlatform",
    data: {
      team_id: teamId,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function getGovernmentdelete(id, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: "/association-group/delete",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data: {
      id,
    },
  });
}

export function governmentsort(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/sort",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}

export function getGovernmentcontactslist(params, teamId?) {
  return client_orgRequest({
    method: "get",
    url: "/association-group/contacts/list",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    params,
  });
}

export function governmentcontactsCreate(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/contacts/create",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}

export function gcontactsdelete(id, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: "/association-group/contacts/delete",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data: {
      id,
    },
  });
}

export function getGovernmentlist(teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/all-list",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function setcontactsGroup(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/contacts/set-group",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}
export function governmentgetsetting(teamId?) {
  return client_orgRequest({
    method: "get",
    url: "/association-group/get-setting",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
export function governmentsetting(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: "/association-group/setting",
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
    data,
  });
}

export function settingVisitor(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: `/visitor/association/setting/visitor`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}




export function getVisitorPlatformList(params, teamId) {
  return client_orgRequest({
    method: "get",
    url: `/visitor/platformList`,
    params,
    headers: {
      teamId,
    },
  });
}

export function getPlatformDetail(id, teamId) {
  return client_orgRequest({
    method: "get",
    url: `/visitor/platformDetail/${id}/association`,
    headers: {
      teamId,
    },
  });
}

export function applyVisitList(params, teamId) {
  return client_orgRequest({
    method: "get",
    url: `/association/apply-visit/list`,
    params,
    headers: {
      teamId,
    },
  });
}

export function applyAgree(id, teamId) {
  return client_orgRequest({
    method: "post",
    url: `/association/apply-visit/agree`,
    data: { id, },
    headers: {
      teamId,
    },
  });
}

export function applyReject(id, teamId) {
  return client_orgRequest({
    method: "post",
    url: `/association/apply-visit/reject`,
    data: { id, },
    headers: {
      teamId,
    },
  });
}

export function getStatistics(teamId) {
  return client_orgRequest({
    method: "get",
    url: `/association/common/statistics`,
    headers: {
      teamId,
    },
  });
}
