/**
 * 地点输入提示
 * https://lbsyun.baidu.com/faq/api?title=webapi/place-suggestion-api
 */
export interface BaiduMapSuggestionParams {
  /** 输入建议关键字（支持拼音） */
  q?: string;

  /** 输入建议关键字（支持拼音） */
  query?: string;

  /** 支持城市及对应百度编码（指定的区域的返回结果加权，可能返回其他城市高权重结果。若要对返回结果区域严格限制，请使用city_limit参数） */
  region?: string;

  /** 取值为"true"，仅返回region中指定城市检索结果 */
  city_limit?: string;

  /** 传入location参数后，是综合各种相关性排序的，location参数只是其中一个因素，但不会严格按距离排序决定最终的排序 */
  location?: string;

  /** 返回数据格式，可选json、xml两种 */
  output?: string;
}
