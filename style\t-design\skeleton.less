// skeleton 动画
:root,
:root[theme-mode="light"] {
  --skeleton-animation-gradient: rgba(0, 0, 0, 4%);
}

:root[theme-mode="dark"] {
  --skeleton-animation-gradient: rgba(255, 255, 255, 6%);
}

@skeleton-animation-gradient: var(--skeleton-animation-gradient);
@skeleton-animation-flashed: rgba(90%, 90%, 90%, .3);

.t-skeleton__col {
  background-color: #ECEFF5;
}

@keyframes t-skeleton--flashed {
  0% {
    opacity: 1;
  }

  50% {
    background-color: @skeleton-animation-flashed;
    opacity: .3;
  }

  100% {
    opacity: 1;
  }
}