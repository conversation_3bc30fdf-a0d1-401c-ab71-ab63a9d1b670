import { defineConfig } from 'dumi';
// import babel from '@rollup/plugin-babel';
export default defineConfig({
  apiParser: {},
  resolve: {
    entryFile: 'src/index.ts',
  },
  outputPath: 'docs-dist',
  themeConfig: {
    name: 'unitPark',
  },
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  presets: [require.resolve('@dumijs/preset-vue')],
  // chainWebpack(config) {
  //   // config.devtool('source-map');
  //   config.module
  //     .rule('babel')
  //     .use('babel-loader')
  //     .tap(options => {
  //       // // 确保 options.plugins 是一个数组
  //       // if (!Array.isArray(options.plugins)) {
  //       //   options.plugins = [];
  //       // }

  //       // // 添加 Babel 插件
  //       // options.plugins.push(babel);
  //       options = options || {};

  //       // 其他配置
  //       options.compact = false; // 或者设置为 true，根据你的需求

  //       return options;
  //     });
  // },
  chainWebpack(config) {
    ['src', 'jsx-ts-tsx'].forEach((rule) => {
      config.module
        .rule(rule)
        .use('babel-loader')
        .tap((opts) => {
          opts.compact = false;
          return opts;
        });
    });
    return config;
  },
});
