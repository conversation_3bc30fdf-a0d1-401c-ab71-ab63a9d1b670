@import './transition.css';

html,
body,
#app {
  width: 100%;
  height: 100%;
}

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  margin: 0;
  padding: 0;
}

html {
  box-sizing: border-box;

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: none;
  }

  ::-webkit-scrollbar-track {
    background-color: none;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #555;
  }
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

//main-container全局样式
.app-main {
  min-height: 100%
}

.app-container {
  padding: 20px;
}