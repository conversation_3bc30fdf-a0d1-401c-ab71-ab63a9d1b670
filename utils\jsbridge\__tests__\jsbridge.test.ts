import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
  vi,
} from 'vitest';

// First, clear all mocks
vi.clearAllMocks();

// Mock the base module with both getEnv and JSBridgeBase
vi.mock('../base', () => {
  const mockEnv = {
    isElectron: true,
    isWeb: false,
    isRingkolDesktopApp: true,
  };

  class MockJSBridgeBase {
    protected static instance: MockJSBridgeBase;

    public static getInstance(): MockJSBridgeBase {
      if (!MockJSBridgeBase.instance) {
        MockJSBridgeBase.instance = new MockJSBridgeBase();
      }
      return MockJSBridgeBase.instance;
    }

    public getEnv() {
      return mockEnv;
    }

    public async send<T = any>(action: string, data?: any): Promise<any> {
      return { code: 0, data: { success: true }, message: 'success' };
    }

    public onMessage(handler: any): void {}
  }

  return {
    getEnv: () => mockEnv,
    JSBridgeBase: MockJSBridgeBase,
  };
});

// Import after mocking
import { getEnv } from '../base';
import { JSBridge } from '../index';

describe('JSBridge', () => {
  const mockResponse = { code: 0, data: { success: true }, message: 'success' };
  const mockInvoke = vi.fn().mockResolvedValue(mockResponse);
  let originalWindow: Window;
  let messageHandlers: Array<(event: MessageEvent) => void> = [];

  beforeAll(() => {
    originalWindow = global.window;
    vi.useFakeTimers();
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
    JSBridge.resetInstance();
    messageHandlers = [];

    // Create window mock with all required properties
    const windowMock = {
      addEventListener: vi.fn((event: string, handler: any) => {
        if (event === 'message') {
          messageHandlers.push(handler);
        }
      }),
      removeEventListener: vi.fn((event: string, handler: any) => {
        if (event === 'message') {
          messageHandlers = messageHandlers.filter((h) => h !== handler);
        }
      }),
      postMessage: vi.fn(),
      parent: null, // This ensures isParentWindow is true
      name: 'test-window',
      location: {
        origin: 'https://dev.ringkol.com',
        href: 'https://dev.ringkol.com/test',
        pathname: '/test',
        search: '',
        hash: '',
        protocol: 'https:',
        host: 'dev.ringkol.com',
        hostname: 'dev.ringkol.com',
      },
      LynkerSDK: {
        ipcRenderer: {
          invoke: mockInvoke,
          send: vi.fn(),
        },
      },
      __APP_ENV__: {
        VITE_APP_CONFIG_INFO: true,
      },
    } as unknown as Window;

    // Set the mock window
    global.window = windowMock;
  });

  afterEach(async () => {
    await vi.runAllTimersAsync();
    messageHandlers = [];
    JSBridge.resetInstance();
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  afterAll(() => {
    global.window = originalWindow;
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  describe('Communication', () => {
    it('should handle message sending in Electron environment', async () => {
      const instance = JSBridge.getInstance();
      const testAction = 'test-action';
      const testData = { foo: 'bar' };

      // Debug the environment state
      const env = getEnv();
      console.log('Environment state:', env);

      // Verify we're in Electron environment
      expect(env.isRingkolDesktopApp).toBe(true);
      expect(env.isElectron).toBe(true);

      const result = await instance.send(testAction, testData);

      expect(mockInvoke).toHaveBeenCalledWith(
        testAction,
        expect.objectContaining(testData),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle message events', async () => {
      const instance = JSBridge.getInstance();
      const mockHandler = vi.fn().mockResolvedValue(undefined);

      instance.onMessage(mockHandler);

      const messageEvent = new MessageEvent('message', {
        data: {
          action: 'test',
          data: { foo: 'bar' },
        },
      });

      // Manually trigger all message handlers
      await Promise.all(
        messageHandlers.map((handler) => handler(messageEvent)),
      );

      expect(mockHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'test',
          data: { foo: 'bar' },
        }),
      );
    });
  });
});
