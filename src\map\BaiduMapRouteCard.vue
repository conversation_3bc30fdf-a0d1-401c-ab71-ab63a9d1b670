<template>
  <div class="route-map-box" :style="{width:props.width+'px',height:props.height+'px'}">
    <BMap v-if="isShow" :ak="BAIDU_AK" :zoom="zoom" enable-scroll-wheel-zoom :api-url="BAIDU_API_URL" @initd="initdFn" />
    <div v-show="!isShow" :style="{
      width:props.width+'px',
      height:props.height+'px'
    }" id="route-map">
    </div>
    <img  :src="RIDER_ICON_REFRESH" class="refresh-btn"
      @click="refreshFn()">
  </div>
</template>

<script setup lang="ts">
  import { BMap } from 'vue3-baidu-map-gl';
  import { BAIDU_AK, BAIDU_API_URL } from './utils';
  import { ref } from 'vue';
  const zoom = ref(10);

  // 常量定义
  const RIDER_ICON = "http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/qs.svg";
  const RIDER_ICON_REFRESH = "http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/refresh_icon.svg";

  const props = defineProps({
    width: {
      type: Number,
      default: 448,
      description: '地图宽度'
    },
    height: {
      type: Number,
      default: 252,
      description: '地图高度'
    },
    start: {
      type: Object,
      default: () => ({
        lng: 116.310791,
        lat: 40.003419
      }),
      description: '起点坐标，包含经度(lng)和纬度(lat)'
    },
    end: {
      type: Object,
      default: () => ({
        lng: 116.386419,
        lat: 40.003519
      }),
      description: '终点坐标，包含经度(lng)和纬度(lat)'
    },
    rider: {
      type: Object,
      default: () => ({
        lng: 0,
        lat: 0
      }),
      description: '骑手位置坐标，包含经度(lng)和纬度(lat)'
    }
  });

  const isShow = ref(true);
  const mapObj = ref(null);
  const riding = ref(null);
  const markerRef = ref(null);
  const mapInstance = ref(null);
  const createMarker = (BMapGL, point) => {
    const myIcon = new BMapGL.Icon(RIDER_ICON, new BMapGL.Size(32, 42));
    const pt = new BMapGL.Point(point.lng, point.lat);
    return new BMapGL.Marker(pt, { icon: myIcon });
  };

  // 搜索路线
  const searchRoute = () => {
    if (!riding.value || !mapObj.value) return;
    const start = new mapObj.value.BMapGL.Point(props.start.lng, props.start.lat);
    const end = new mapObj.value.BMapGL.Point(props.end.lng, props.end.lat);
    riding.value.search(start, end);
  };

  const refreshFn = (newPosition = null) => {
    if (props.rider?.lng && props.rider?.lat) {
      if (!mapObj.value || !mapInstance.value) return;

      const newLng = newPosition?.lng || props.rider.lng;
      const newLat = newPosition?.lat || props.rider.lat;

      // 移除旧标记
      if (markerRef.value) {
        mapInstance.value.removeOverlay(markerRef.value);
      }
      const marker = createMarker(mapObj.value.BMapGL, { lng: newLng, lat: newLat });
      mapInstance.value.addOverlay(marker);
      markerRef.value = marker;
     
    }
    searchRoute();

  };

  const initdFn = (val) => {
    mapObj.value = val;
    isShow.value = false;
    const map = new val.BMapGL.Map("route-map");
    mapInstance.value = map;
    // 启用滚轮缩放
    map.enableScrollWheelZoom(true);
    // 添加骑手标记
    if (props.rider?.lng && props.rider?.lat) {
      const point = new val.BMapGL.Point(props.rider.lng, props.rider.lat);
      map.centerAndZoom(point, 15);

      const marker = createMarker(val.BMapGL, props.rider);
      map.addOverlay(marker);
      markerRef.value = marker;
    }
    riding.value = new val.BMapGL.RidingRoute(map, {
      renderOptions: {
        map: map,
        autoViewport: true
      }
    });
    searchRoute();
    const zoomCtrl = new val.BMapGL.ZoomControl();
    map.addControl(zoomCtrl);
  };
</script>

<style scoped>
  .route-map-box {
    position: relative;
  }

  :global(.route-map-box #route-map .anchorBR) {
    position: absolute !important;
    inset: auto !important;
    top: 12px !important;
    right: 12px !important;
  }

  .refresh-btn {
    position: absolute;
    bottom: 12px;
    right: 12px;
    z-index: 9999;
    width: 32px;
    height: 32px;
    cursor: pointer;
  }

  #route-map {
    position: relative;
  }
</style>