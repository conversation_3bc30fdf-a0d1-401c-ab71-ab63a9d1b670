<template>
  <div class="demo-container">
    <h4>基本高亮</h4>
    <div class="demo-item">
      <p>原文：{{ content }}</p>
      <p>高亮关键词 "Vue"：<RkHighlight :content="content" keyword="Vue" /></p>
    </div>

    <t-divider />

    <h4>忽略大小写</h4>
    <div class="demo-item">
      <p>原文：{{ content2 }}</p>
      <p>忽略大小写高亮 "javascript"：<RkHighlight :content="content2" keyword="javascript" /></p>
      <p>区分大小写高亮 "javascript"：<RkHighlight :content="content2" keyword="javascript" :ignore-case="false" /></p>
    </div>

    <t-divider />

    <h4>无匹配内容</h4>
    <div class="demo-item">
      <p>原文：{{ content3 }}</p>
      <p>高亮不存在的关键词 "Python"：<RkHighlight :content="content3" keyword="Python" /></p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RkHighlight } from '../index';
import { Divider as TDivider } from 'tdesign-vue-next';

const content = 'Vue.js 是一个用于构建用户界面的渐进式 JavaScript 框架。';
const content2 = 'JavaScript 是一种高级的、解释型的编程语言，支持面向对象、命令式和函数式编程范式。';
const content3 = 'React 是由 Facebook 开发的用于构建用户界面的 JavaScript 库。';
</script>

<style lang="less" scoped>
@import '../../demo-common.less';
</style>
