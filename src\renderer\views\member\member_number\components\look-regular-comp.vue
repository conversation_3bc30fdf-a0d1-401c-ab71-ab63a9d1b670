<template>
  <!-- <template #footer>
      <div v-if="data" class="operates">
        <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onClose"
        >取消</t-button>
        <t-button
          v-show="data.status === 1"
          theme="danger"
          variant="outline"
          class="operates-item"
          @click="onReject"
        >驳回申请</t-button>
        <t-button
          v-show="data.status === 1"
          theme="primary"
          class="operates-item"
          @click="onSuccess"
        >审核通过</t-button>
      </div>
    </template> -->

  <div v-if="data && data.submit_data && visible" class="drawerSet-body">
    <div v-if="data.status === 3" class="system">
      <div class="detail-control" style="width: 100%">
        <div class="lable"><span class="line" />{{$t('member.bing.c')}}</div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.bing.d')}}</div>
        <div class="value">
          {{ $filters.isPeriodEmpty(data.exit_time) }}
        </div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.bing.e')}}</div>
        <div class="value">
          {{ $filters.isPeriodEmpty(data.content) }}
        </div>
      </div>
    </div>
    <form-detail
      ref="runtimeRef"
      :widgets="data.submit_data.free_form"
      @release="releaseRun"
    />

    <div class="system" v-show="!isHiddenSystem">
      <div class="detail-control" style="width: 100%">
        <div class="lable"><span class="line" />{{$t('member.bing.f')}}</div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.bing.g')}}</div>
        <div class="value">
          {{ data.activate === 1 ? $t('member.winter_column.activeOptions_1') : $t('member.winter_column.activeOptions_2')  }}
        </div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.bing.h')}}</div>
        <div class="value">
          <div v-if="data.status === 1" class="success">{{$t('member.winter_column.memberOptions_1')}}</div>
          <div v-else-if="data.status === 2" class="reject">{{$t('member.winter_column.memberOptions_2')}}</div>
          <div v-else-if="data.status === 3" class="reject">{{$t('member.winter_column.memberOptions_3')}}</div>
        </div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.svip.create_time')}}</div>
        <div class="value">
          {{ $filters.isPeriodEmpty(data.created_at) }}
        </div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.svip.update_time')}}</div>
        <div class="value">
          {{ $filters.isPeriodEmpty(data.updated_at) }}
        </div>
      </div>
      <div class="detail-control" :style="{ width: '50%' }">
        <div class="subLable">{{$t('member.svip.updator')}}</div>
        <div class="value">
          {{ $filters.isPeriodEmpty(data.operator_name) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch, computed } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import formDetail from "@renderer/components/free-from/detail/index.vue";
import { getMemberSettingAxios } from "@renderer/api/member/api/businessApi";
import { formDiff } from "@renderer/components/free-from/utils";
import memberConst from "@renderer/components/free-from/design/constants/memberConst";
import lodash from "lodash";
import { getResponseResult } from "@/utils/myUtils";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import { useI18n } from "vue-i18n";
const { t } = useI18n();


const optionsOrganizeType = [
  // { label: "企业", value: 1 },
  // { label: "商协会", value: 2 },
  // { label: "个体户", value: 3 },
  // { label: "其他", value: 0 }
  { label: t("member.second.g"), value: 1 },
  { label: t("member.second.h"), value: 2 },
  { label: "政府单位", value: 4 },
  { label: t("member.second.i"), value: 3 },
  { label: t("member.second.j"), value: 0 },
];

// import RejectModal from "./reject-modal.vue";
// import SuccessModal from "./success-modal.vue";

// import { ClientSide } from "@renderer/types/enumer";

// 运行时
const controls = ref([]);
const runtimeRef = ref(null);

const props = defineProps({
  isHiddenSystem: {
    type: Boolean,
    default: false
  },
  platform: {
    type: String,
    default: '',
  },
})

// const organizeSelectCompRef = ref(null);

const visible = ref(false);
let origin_data = null; // 用于内部全局
const data = ref(null);
const emits = defineEmits(["reload"]);





const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else {
    return  getMemberTeamID()
  }
})



const releaseRun = (data) => {
  console.log(data);
};
// 自动构造自定义表单
const onInitConstructFreeForm = (origins: any) => {
  if (origins && origins.submit_data && origins.submit_data.free_form) {
    // show: true
    origins.submit_data.free_form.map((fr) => (fr.show = true));
    const baseList = origins.submit_data.free_form.filter(
      (v: any) => v.type === "BaseInfoMember"
    );
    console.log(baseList);
    baseList.map((v: any) => {
      v.origin.map((vo) => {
        vo.isShow = true;
      });
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        // const levelItem = origin.options.find(
        //   (v: any) => v.id === origin.value
        // );
        origin.value = origin_data.level_name;
      }

      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin) {
        origin.value = origin_data.data.team_name;
      }
      // 组织简称
      origin = v.origin.find((or: any) => or.vModel === "organizeAbbrName");
      if (origin) {
        // origin.options = optionsOrganizeType;
        origin.value = origin_data?.team_short_name  || origin_data?.submit_data?.team_short_name  ;
      }

      // 名录照片 2024-07-05 1.4.2
      origin = v.origin.find((or: any) => or.vModel === "nameLogo");
      if (origin) {
        origin.value = origin_data?.submit_data?.directory_image_values || [];
      }

      // 所在单位岗位
      origin = v.origin.find((or: any) => or.vModel === "unitJob");
      if (origin) {
        origin.value = origin_data?.job || origin_data?.submit_data?.job ;
      }

      // 兴趣爱好
      origin = v.origin.find((or: any) => or.vModel === "interest");
      if (origin) {
        origin.value = origin_data?.hobby || origin_data?.submit_data?.hobby ;
      }

      // 组织LOGO
      origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      if (origin) {

        const organizeLogoSrc = origins?.team_logo  || origins?.submit_data?.team_logo ;

        if (organizeLogoSrc) {
          const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(organizeLogoSrc);
          const origin_name = organizeLogoSrc ? organizeLogoSrc.substring(organizeLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: organizeLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: logoRes?.length > 1 ? logoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = [];
        }
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin) origin.value = [{ file_name: origin_data.avatar }];

      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = origin.code_value
          ? Number(origin.code_value)
          : Number(origin_data.telCode);
        origin.value = origin_data.telephone;
      }

      // 会员编号
      origin = v.origin.find((or: any) => or.vModel === "memberNum");
      if (origin) {
        origin.value = origin_data.data.no;
      }

      // 推荐人
      origin = v.origin.find((or: any) => or.vModel === "reference");
      if (origin) {
        origin.value = origin_data.referrer;
      }

      origin = v.origin.find((or: any) => or.vModel === "referenceUnit");
      if (origin) {
        origin.value = origin_data.referrer_unit;
      }

      // 代表人姓名
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) origin.value = origin_data.name;

      // 到期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) origin.value = origin_data.expire_time;

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = origin_data.join_time;

      // 邮箱
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) origin.value = origins.submit_data.email;

      // 部门
      origin = v.origin.find((or: any) => or.vModel === "department");
      // if (origin) origin.value = origin_data.department_name;
      if (origin) origin.value = origin_data.department_name;

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        origin.options = optionsOrganizeType;
        const typeKey = origins?.team_type  || origins?.submit_data.team_type  ;

        const item = origin.options?.find((v: any) => v.value === typeKey);
        origin.value = item ? item.label : "";
      }

      // 业务范围，
      origin = v.origin.find((or: any) => or.vModel === "business");
      if (origin) {
        origin.value = origins?.business || origins?.submit_data.business ;
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        // origin.value = origins.submit_data.industry_text;
        origin.value = origins?.industry_text || origins?.submit_data?.industry_text  ;

      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        // origin.value = origins.submit_data.size_text;
        origin.value = origins.size_text || origins.submit_data.size_text;

      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        // origin.value =
        //   origins.submit_data.country +
        //   origins.submit_data.province +
        //   origins.submit_data.city +
        //   origins.submit_data.district;
        origin.value =
          origins.submit_data.country +
          origins.submit_data.province +
          origins.submit_data.city +
          origins.submit_data.district;
        origin.detail = origins.submit_data.address;
      }
      return v;
    });
  }
};

// 通过自定义表单添加的时候，使用该方法
const onInitFreeForm = (origins: any) => {
  if (origins && origins.submit_data && origins.submit_data.free_form) {
    origins.submit_data.free_form.map((fr) => (fr.show = true));
    const baseList = origins.submit_data.free_form.filter(
      (v: any) => v.type === "BaseInfoMember"
    );
    console.log(baseList);
    baseList.map((v: any) => {
      v.origin.map((vo) => {
        vo.isShow = true;
        return vo;
      });
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        // const levelItem = origin.options?.find(
        //   (v: any) => v.id === origin.value
        // );
        // origin.value = levelItem ? levelItem.level_name : "";
        origin.value = origins?.level_name;
      }
      console.log(origin);
      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin && origin.value) {
        // origin.value = origin.value.teamFullName;
      }

      // 组织简称
      origin = v.origin.find((or: any) => or.vModel === "organizeAbbrName");
      if (origin) {
        // origin.options = optionsOrganizeType;
        origin.value = origins?.team_short_name  || origins?.submit_data?.team_short_name;
      }
      // 组织LOGO
      origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      if (origin) {

        const organizeLogoSrc = origins?.team_logo  || origins?.submit_data?.team_logo ;

        if (organizeLogoSrc) {
          const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(organizeLogoSrc);
          const origin_name = organizeLogoSrc ? organizeLogoSrc.substring(organizeLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: organizeLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: logoRes?.length > 1 ? logoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = [];
        }
      }

      // 名录照片 2024-07-05 1.4.2
      origin = v.origin.find((or: any) => or.vModel === "nameLogo");
      if (origin) {
        // origin.value = origin_data?.submit_data?.directory_image_values || [];
        if (origins?.directory_image) {
          const nameLogoSrc = origins?.directory_image;
          const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
          const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: nameLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = [];
        }
      }


      // 姓名/代表人
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) {
        // origin.disabled = false;
        // origin.value = applyData.value?.name || origin.value;
        origin.value = origins?.name;
      }

      // 邮箱 2023-11-8 商协会1.2
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) {
        // origin.disabled = false;
        // origin.value = applyData.value?.name || origin.value;
        origin.value = origins?.email;
      }

      // 兴趣爱好
      origin = v.origin.find((or: any) => or.vModel === "interest");
      if (origin) {
        origin.value = origins?.hobby || origins?.submit_data?.hobby ;
      }

      // 所在岗位
      origin = v.origin.find((or: any) => or.vModel === "unitJob");
      if (origin) {
        origin.value = origins?.job || origins?.submit_data?.job  ;
      }

      // 姓名/代表人
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin) {
        // origin.disabled = false;
        // origin.value = applyData.value?.name || origin.value;
        origin.value = [{ file_name: origins?.avatar }];
      }

      origin = v.origin.find((or: any) => or.vModel === "department");
      if (origin) {
        console.log(origins);
        // origin.value = `${origins.department_name}`;
        origin.value = origins?.departments;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin && origin.value && origin.value.length > 0) {
        // origin.value = origin.value[0].file_name;
      }


      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = origin.code_value
          ? Number(origin.code_value)
          : Number(origins.telCode);
        origin.value = origins.telephone;
      }

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 到期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) origin.value = origins.expire_time;

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = origins.join_time;

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        const typeKey = origins?.team_type  || origins?.submit_data.team_type ;

        const item = origin.options?.find((v: any) => v.value === typeKey);
        origin.value = item ? item.label : "";
      }

      // 业务范围，
      origin = v.origin.find((or: any) => or.vModel === "business");
      if (origin) {
        origin.value = origins?.business || origins?.submit_data.business  ;
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        origin.value = origins.submit_data.industry_text;
      }

      // 推荐人
      origin = v.origin.find((or: any) => or.vModel === "reference");
      if (origin) {
        origin.value = origins.referrer || origins.submit_data.referrer;
      }

      origin = v.origin.find((or: any) => or.vModel === "referenceUnit");
      if (origin) {
        origin.value =
          origins.referrer_unit || origins.submit_data.referrer_unit;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.value = origins.submit_data.size_text;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        origin.value =
          origins.submit_data.country +
          origins.submit_data.province +
          origins.submit_data.city +
          origins.submit_data.district;
        origin.detail = origins.submit_data.address;
      }
      return v;
    });

    // controls.value = origins.submit_data.free_form;
  }
  //   else {
  //     controls.value = [];
  //   }
};

// 通过

const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const successModalRef = ref(null);

// 2023-8-3 lss 这里做一下处理，如果用户未设置自由表单，则读取默认的
const initFreeForm = (res) => {
  console.log(res);
  // eslint-disable-next-line no-empty
  if (res.personal_form && res.personal_form.length > 0) {
  } else {
    const menuList = memberConst.filter((v) => v.fromType === "person");
    // 给系统默认
    res.personal_form = lodash.cloneDeep(menuList);
  }
  // eslint-disable-next-line no-empty
  if (res.team_form && res.team_form.length > 0) {
  } else {
    const menuList = memberConst.filter((v) => v.fromType === "unit");
    // 给系统默认
    res.team_form = lodash.cloneDeep(menuList);
  }
};

const onOpen = (item?: any) => {
  controls.value = [];
  // data.value = null;
  // 0 是详情信息， 1是系统设置信息
  // onInitFreeForm(item);
  origin_data = item[0];
  // 这里做一下处理，如果用户未设置自由表单，则读取默认的
  initFreeForm(item[1]);

  let subData = null;
  if (item[0].type === 1) {
    // 单位
    subData = item[1].team_form;
  } else {
    subData = item[1].personal_form;
  }
  // 以系统表单设计为主

  if (item[0].submit_data && item[0].submit_data.free_form) {
    item[0].submit_data.free_form = formDiff(
      item[0].submit_data.free_form,
      subData
    );
    console.log(item[0]);
    onInitFreeForm(item[0]);
  } else if (item[0].submit_data) {
    item[0].submit_data = { ...item[0].submit_data, free_form: subData };
    onInitConstructFreeForm(item[0]);
  }
  data.value = item[0];
  console.log(data.value);
  visible.value = true;
  //   visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>
<style lang="less" scoped>
@import url("@renderer/views/member/member_home/panel/public.less");

:deep(.t-steps--vertical).t-steps--dot-anchor
  .t-steps-item--finish
  .t-steps-item__icon {
  border-color: #c7c7c8;
  background: #c7c7c8;
}

:deep(.t-steps-item--finish) {
  &::before {
    border-right-color: #c7c7c8 !important;
    color: #c7c7c8 !important;
    left: 2.5px;
    top: 24px;
  }
}

.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}
.toTitle {
  font-size: 14px;

  font-weight: 400;

  // color: #13161b;
}
.toContent {
  font-size: 14px;

  font-weight: 400;
}
.operates {
  display: flex;
  justify-content: flex-end;
}
.drawerSet {
  // width: 720px;
  &-body {
  }
  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }
}

:deep(.t-drawer__header) {
  border-bottom: 0;
  color: red;
}
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}
</style>
