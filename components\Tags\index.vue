<template>
  <div class="RK-Tags">
    <div class="tags-wrap">
      <TTag
        :class="`${item.name} ${item.type}`"
        v-bind="props.attrs"
        v-for="(item, index) in list"
        :key="index"
        @click="(e) => onClick(e, item)"
      >
        {{ item.label }}
      </TTag>
    </div>
  </div>
</template>

<script setup lang="ts" name="RK-Tags">
import { ref, watch } from 'vue';
import { Tag as TTag } from 'tdesign-vue-next';
import { TagItemType } from './type'

const props = defineProps({
  list: {
    type: Array<TagItemType>,
    required: true
  },
  attrs: {
    type: Object,
    default: {}
  }
});
const emit = defineEmits(['click']);
const list = ref(props.list);
watch(() => props.list, (newVal) => {
  list.value = newVal;
});
const onClick = (e, item) => {
  emit('click', { event: e, data: {...item} });
};
</script>

<style lang="less" scoped>
@import (reference) '../../style/commonComp.less';

.RK-Tags{
  .tags-wrap {
    :deep(.t-tag){
      margin-right: 10px;
      display: inline-block;
      height: 20px;
      line-height: 18px;
      .theme-color();
      border: none;
      > span {
        display: inline-block;
        vertical-align: middle;
        font-size: 12px;
      }
    }
  }
}
</style>
