import { RESOURCE_DOMAIN_LOCAL } from '../constants';
import { parseJsonSafely } from './base';
import { getFileExtension } from './file';

/**
 * 音视频加速域名：video.ringkol.com，其他类型使用image.ringkol.com
 * @param urlString - 要替换域名的URL字符串
 * @param type - 可选：文件类型
 * @returns 修改后的URL字符串
 */
export const replaceUrlDomain = (urlString: string, type?: string): string => {
  if (!urlString) return '';
  if (!urlString.startsWith('http')) return urlString;

  const resourceDomains = getResourceDomains();

  // 校验资源域名配置结构是否有效
  if (
    !(
      resourceDomains &&
      typeof resourceDomains === 'object' &&
      resourceDomains.defaultDomain &&
      Array.isArray(resourceDomains.domains)
    )
  ) {
    return urlString;
  }

  try {
    const url = new URL(urlString);
    const fileType = type ? type.toLowerCase() : getFileExtension(url.pathname);
    const newDomain = getDomainByFileType(resourceDomains, fileType);
    url.hostname = newDomain;
    return url.toString();
  } catch (error) {
    console.error('Invalid URL:', error, urlString, resourceDomains);
    return urlString;
  }
};

// 从 localStorage 获取资源域名配置
const getResourceDomains = (): any => {
  try {
    const resourceDomainsJson =
      typeof window !== 'undefined' && window.localStorage
        ? window.localStorage.getItem(RESOURCE_DOMAIN_LOCAL)
        : null;
    return parseJsonSafely(resourceDomainsJson);
  } catch (error) {
    console.error('获取域名资源失败:', error);
    return null;
  }
};

// 根据文件类型获取对应的域名
const getDomainByFileType = (
  resourceDomains: any,
  fileType: string,
): string => {
  const domainItem = resourceDomains.domains.find((item: any) =>
    item.formats?.includes(fileType),
  );
  return domainItem ? domainItem.domain : resourceDomains.defaultDomain;
};
