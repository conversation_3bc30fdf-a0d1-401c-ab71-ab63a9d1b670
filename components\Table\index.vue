<template>
  <div class="RK-Table">
    <TTable
      cell-empty-content="--"
      :row-key="rowKey"
      v-bind="attrs"
      :data="list"
      :columns="columns"
      :pagination="pagination"
      :on-page-change="onPageChange"
      :on-sort-change="onSortChange"
      :sort-icon="sortIcon"
      class="tTable"
      :style="tableScrollStyle"
    >
      <template #action="{ row }">
        <div> {{ row.name }} </div>
      </template>
      <template #empty>
        <div v-if="$slots.empty" class="empty">
          <slot name="empty"></slot>
        </div>
        <Empty v-else :name="'no-data-new'" />
      </template>
    </TTable>
  </div>
</template>

<script setup lang="tsx" name="RK-Table">
import {ref, watch, computed} from 'vue';
import {Table as TTable} from 'tdesign-vue-next';
import Empty from '../Empty/index.vue';

const props = defineProps({
  rowKey: {
    type: String,
    required: true
  },
  list: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  pagination: {
    type: Object,
    required: false
  },
  attrs: {
    type: Object,
    required: false
  },
});
const emit = defineEmits(['change']);
const list = ref(props.list);
const pagination = ref(props.list && props.list.length > 0 ? props.pagination : null);
const columnsList = ref(props.columns || [])
const attrs = ref(props.attrs);

console.log('table attrs', attrs);
watch(() => props.attrs, (newVal) => {
  attrs.value = newVal;
});
watch(() => props.list, (newVal) => {
  list.value = newVal || [];
  if (list.value.length > 0) {
    pagination.value = props.pagination;
  } else {
    pagination.value = null;
  }
});
watch(() => props.columns, (newVal) => {
  columnsList.value = newVal;
});

const columns = computed(() => {
  const list = []
  columnsList.value.map(v => {
    if (v.isShow && typeof v.isShow === 'function') {
      if (v.isShow()) {
        list.push(v);
      }
    } else {
      list.push(v);
    }
  })
  console.log('columns', columnsList.value, list)
  return list
})

const sortIcon = (h) => h && <div className="rk-sort-icon"></div>;

const onPageChange = (pageInfo, newDataSource) => {
  emit('change', {
    name: 'pageChange',
    pageInfo,
    newDataSource
  });
}

const onSortChange = (sortInfo) => {
  emit('change', {
    name: 'sortChange',
    sortInfo,
  });
}

const tableScrollStyle = computed(() => {
  if (props.attrs.maxTableHeight) {
    return {
      maxHeight: `${props.attrs.maxTableHeight}px`,
      overflowY: 'auto'
    }
  }
  return {}
})

</script>

<style lang="less">
.RK-Table{
  .tTable{
    .t-pagination__total{
      margin-right: 24px;
      flex: initial;
    }
    .t-table__content > table > tbody.t-table__body > tr{
        background-color: #fff;
        &.t-table__empty-row{
          &:hover{
            background-color: #fff !important;
          }
        }
    }
  }

  .t-table__double-icons{
    height: 12px;
    justify-content: space-between;

    .t-table-sort-asc{
      top: 0;
    }

    .t-table-sort-desc{
      bottom: 0;
    }

    .t-table__sort-icon--active{
      .rk-sort-icon{
        background-image: url('../../assets/common/icon_sort_active.svg');
      }
    }
  }

  .rk-sort-icon{
    width: 8px;
    height: 4px;
    background: url('../../assets/common/icon_sort.svg') no-repeat center / 100%;
  }
}
</style>
