<script setup lang="ts">
import { ref } from 'vue';
import RkTextEllipsis from '../index.vue';
import { Button as TButton } from 'tdesign-vue-next';

const longText = '在Vue.js生态系统中，我们经常需要处理大量的文本内容。当这些内容超出容器的显示范围时，我们就需要使用文本省略功能。TextEllipsis组件提供了一个优雅的解决方案，它不仅可以自动计算并截断文本，还提供了展开和收起的交互功能，让用户可以根据需要查看完整内容。';

const textEllipsisRef = ref();
const expanded = ref(false);

const onClickAction = (event: MouseEvent) => {
  // 切换状态
  expanded.value = !expanded.value;
  console.log('文本展开状态:', expanded.value);
};

const handleToggle = () => {
  if (textEllipsisRef.value) {
    textEllipsisRef.value.toggle();
  }
};
</script>

<template>
  <div class="demo-container">
    <h3>自定义按钮文案</h3>
    <div class="demo-block">
      <p>当前状态: {{ expanded ? '已展开' : '已收起' }}</p>

      <RkTextEllipsis
        :content="longText"
        :rows="2"
        expand-text="展开 ↓"
        collapse-text="收起 ↑"
        @click-action="onClickAction"
      />
    </div>

    <h3>外部控制展开/收起</h3>
    <div class="demo-block">
      <t-button
        theme="primary"
        @click="handleToggle"
      >
        外部触发切换
      </t-button>
      <RkTextEllipsis
        ref="textEllipsisRef"
        :content="longText"
        :rows="2"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
@import '../../demo-common.less';
</style>
