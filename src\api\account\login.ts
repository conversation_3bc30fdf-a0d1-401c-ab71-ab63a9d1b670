// import request from '../request';
import request, { ringkolRequest } from '@/utils/requestim';
import requestimlogin from '@/utils/requestimlogin';

import {
  type register,
  type identifyCode,
  type loginMa,
  type loginMaV2,
  type resetPw,
  type jwt,
  type joinTeam,
  type sms,
} from './loginModel';

export function listAreaCodes(data: any) {
	return requestimlogin({
		method: 'get',
		url: '/iam/v2/area_code/listAreaCodes',
		data,
	});
}
export function registerAccount(data: register) {
  return request({
    method: 'post',
    url: '/v1/accounts',
    data,
  });
}

export function getIdentifyCode(data: identifyCode) {
  return request({
    method: 'post',
    url: '/v1/proofs',
    data,
  });
}

export function loginAccount(data: loginMa) {
  return request({
    method: 'post',
    url: '/v1/passport/guest',
    data,
  });
}
export function loginOrRegisterByMobile(data: any) {
	return request({
		method: 'post',
		url: '/iam/v2/account/loginOrRegisterByMobile',
		data,
	});
}

export function loginAccountV2(data: any) {
	return request({
		method: 'post',
		url: '/iam/v2/account/loginAccount',
		data,
	});
}

export function getJwt(data: jwt) {
  return request({
    method: 'post',
    url: '/v1/passport',
    data,
  });
}

export function resetPassword(data: resetPw) {
  return request({
    method: 'put',
    url: '/v1/password',
    data,
  });
}

export function regionsSearch(data: { code: string }) {
  return request({
    method: 'post',
    url: '/v1/regions/search',
    data,
  });
}

export function getProfile() {
  return request({
    method: 'get',
    url: '/v1/profiles/me',
  });
}

export function getAreaCodes(data: any) {
  return request({
    method: 'post',
    url: '/v2/area_code/listAreaCodes',
    data,
  });
}

export function getQrCode() {
  return request({
    method: 'get',
    url: '/v1/accounts/qrcode',
  });
}
