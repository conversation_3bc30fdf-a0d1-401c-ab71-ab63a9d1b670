<template>
  <t-drawer v-model:visible="visible" class="filterDrawer filterDrawerdp" :size="472" :closeBtn="true" header="详情"
    :footer="cardData.official_type === 2">
    <div class="form-boxxx">
      <div class="sbar">
        <div class="av">
          <kyy-avatar round-radius class="plat-avatar" avatar-size="46px" :image-url="cardData.avatar"
            :user-name="cardData.name" />
        </div>
        <div class="info">
          <div class="name">{{ cardData.name }}</div>
          <div class="tags">
            <div class="status1" v-if="cardData.status === 1">{{ t('ebook.vzc') }}</div>
            <div class="status2" v-else>{{ t('ebook.vjr') }}</div>
            <div class="type1" v-if="cardData.official_type === 1">{{ t('ebook.vzs') }}</div>
            <div class="type2" v-else>{{ t('ebook.vfk2') }}</div>
          </div>
        </div>
      </div>
      <div class="cont">
        <div class="ibox">
          <div class="title">
            <div class="tg"></div>
            <div class="text">{{ t('ebook.vb') }}</div>
          </div>
          <div class="rowb">
            <div class="row-item">
              <div class="row-item-title">{{ t('ebook.vname') }}</div>
              <div class="row-item-content">{{ cardData.name || '--' }}</div>
            </div>
            <div class="row-item">
              <div class="row-item-title">{{ t('ebook.vph2') }}</div>
              <div class="row-item-content"> +{{ cardData.telCode }} {{ cardData.telephone }}</div>
            </div>
            <div class="row-item">
              <div class="row-item-title">{{ t('ebook.vemail') }}</div>
              <div class="row-item-content">{{ cardData.email || '--' }}</div>
            </div>
            <div class="row-item">
              <div class="row-item-title">{{ t('ebook.vat') }}</div>
              <div class="row-item-content">{{ cardData.official_date || '--' }}</div>
            </div>
          </div>
        </div>

        <div class="ibox mt32" v-if="cardList.length">
          <div class="title">
            <div class="tg"></div>
            <div class="text">{{ t('ebook.vzty') }}</div>
          </div>
          <div class="card-row">
            <div class="card-row-item" v-for="(item, index) in cardList" :key="index">
              <div class="card-row-item-av">
                <template v-if="item.logo">
                  <t-image class="logo" :src="item.logo" fit="cover">
                    <template #loading>
                      <img src="@renderer/assets/member/svg/innerDefault.svg" />
                    </template>
                    <template #error>
                      <img src="@renderer/assets/member/svg/innerDefault.svg" />
                    </template>
                  </t-image>
                </template>
                <template v-else>
                  <img class="logo" src="@renderer/assets/member/svg/innerDefault.svg" />
                </template>
              </div>
              <div class="card-row-item-content">
                <div class="name">{{ item.staff_name }}</div>

                <template v-if="item.type === 1">
                  <template v-if="props.platform === 'member'">
                    <div class="post"> {{ item.is_contact === 1 ? t('ebook.vxx') : item.job_name }}</div>
                  </template>

                  <template v-else-if="props.platform === 'cbd'">
                    <div class="post" v-if="item.is_contact === 1"> {{ t('ebook.vdb') }}</div>
                    <div class="post" v-else> {{ t('ebook.vfz') }}</div>
                  </template>

                  <template v-else>
                    <div class="post" v-if="item.is_contact === 1"> {{ t('ebook.vxx') }}</div>
                    <div class="post" v-else> {{ t('ebook.vdb') }}</div>
                  </template>
                </template>
                <template v-else>
                  <div class="post"> {{ t("order.geren") }}</div>
                </template>
                <div class="com" v-if="item.type === 1">{{ item.name }}</div>
                <div class="tag" v-if="props.platform === 'member'">{{ item.level_name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer v-if="cardData.official_type === 2">
      <div style="display: flex;justify-content: end;">
        <t-button theme="danger" variant="outline" @click="delRun">{{ t('ebook.delf') }}</t-button>
      </div>
    </template>
  </t-drawer>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { visitorDel } from '@renderer/api/digital-platform/api/businessApi';

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
  teamId: {
    type: String,
    default: '',
  },
})

const { t } = useI18n();
const visible = ref(false);
const cardData = ref({});
const cardList = ref([]);
const emit = defineEmits(['refdata']);
const open = (data) => {
  cardData.value = data.detail;
  cardList.value = data.cards;
  console.log(data);
  console.log(props.teamId);
  visible.value = true;
}
const delRun = () => {
  const confirmPlugin = DialogPlugin({
    header: t('ebook.vdelt'),
    theme: 'warning',
    body: t('ebook.vdelc'),
    closeBtn: null,
    closeOnOverlayClick: false,
    cancelBtn: t('ebook.mset4'),
    confirmBtn: t('ebook.mset3'),
    className: 'delmode',
    onConfirm: async () => {
      confirmPlugin.hide();
      delvReq();
    },
    onClose: () => {
      confirmPlugin.hide();
    },
  });
}

const delvReq = () => {
  visitorDel(cardData.value.id, props.teamId).then((res) => {
    if (res.data?.code === 0) {
      visible.value = false;
      MessagePlugin.success(t('ebook.vdsu'));
      emit('refdata', true);
    } else {
      MessagePlugin.error(t('ebook.vfk3'));
      visible.value = false;
      emit('refdata', true);
    }
  }).catch((error) => {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    emit('refdata', true);
    visible.value = false;
  });
}

defineExpose({
  open,
})
</script>

<style lang="less" scoped>
.box {
  width: 100%;
}

.sbar {
  display: flex;
  padding: 12px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
  display: flex;
  gap: 12px;

  .name {
    margin-bottom: 4px;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-overflow: ellipsis;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .tags {
    display: flex;
    gap: 4px;
  }

  .status1 {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 1px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_success, #E0F2E5);
    color: var(--kyy_color_tag_text_success, #499D60);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  .status2 {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 1px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_warning, #FFE5D1);
    color: var(--kyy_color_tag_text_warning, #FC7C14);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  .type1 {
    display: flex;
    height: 24px;
    padding: 0px var(--kyy_radius_toopltip, 6px);
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_brand, #EAECFF);
    color: var(--kyy_color_tag_text_brand, #4D5EFF);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  .type2 {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 0px 8px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_magenta, #FFE3F1);
    color: var(--kyy_color_tag_text_magenta, #FF4AA1);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

}

.cont {
  margin-top: 16px;

  .ibox {
    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      margin-bottom: 16px;

      .tg {
        width: var(--checkbox-kyy_radius_checkbox, 2px);
        height: 14px;
        min-width: var(--checkbox-kyy_radius_checkbox, 2px);
        max-width: var(--checkbox-kyy_radius_checkbox, 2px);
        min-height: 14px;
        max-height: 14px;
        border-radius: 1px;
        background: var(--brand-kyy_color_brand_default, #4D5EFF);
      }

      .text {
        color: var(--text-kyy_color_text_1, #1A2139);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        /* 157.143% */
      }
    }

    .rowb {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      .row-item {
        width: 200px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .row-item-title {
          color: var(--text-kyy_color_text_3, #828DA5);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }

        .row-item-content {
          color: var(--text-kyy_color_text_1, #1A2139);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }
      }
    }

    .card-row {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .card-row-item {
        display: flex;
        padding: 12px;
        align-items: flex-start;
        gap: 12px;
        align-self: stretch;
        border-radius: 8px;
        // background: #F1F4FF;
        background: url('@/assets/member/card_2.png');
        background-size: 100% 100%;

        .card-row-item-av {
          width: 72px;
          height: 100px;
          border-radius: 4px;

          .logo {
            width: 100%;
            height: 100%;
            border-radius: 4px;

          }
        }

        .card-row-item-content {
          .name {
            color: var(--text-kyy_color_text_1, #1A2139);

            /* kyy_fontSize_3/bold */
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
            /* 150% */
          }

          .post {
            color: var(--text-kyy_color_text_3, #828DA5);

            /* kyy_fontSize_2/regular */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }

          .com {
            color: var(--text-kyy_color_text_2, #516082);

            /* kyy_fontSize_2/regular */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin: 4px 0px 2px 0px;
          }

          .tag {
            display: flex;
            height: 24px;
            min-height: 24px;
            max-height: 24px;
            padding: 0px 8px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_purple, #FAEDFD);
            color: var(--kyy_color_tag_text_purple, #CA48EB);
            text-align: center;
            min-width: 72px;
            /* kyy_fontSize_2/bold */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
            max-width: 100%;
            width: max-content;
          }
        }
      }
    }

  }
}

.form-boxxx {
  padding: 0 8px;
}
</style>
