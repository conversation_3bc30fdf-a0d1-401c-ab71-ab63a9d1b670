//switch
.t-switch.t-is-checked {
  background: var(--kyy-color-switch-brand-default, #4D5EFF);
}

.t-switch {
  min-width: 44px !important;
  height: 24px !important;
  line-height: 24px !important;
}
.advertising-space{

  .t-swiper__arrow--default .t-swiper__arrow-left:hover{
    color: #fff;
    border-radius: 50% !important;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36)) !important;
  }
  .t-swiper__arrow--default .t-swiper__arrow-right:hover{
    color: #fff;
    border-radius: 50% !important;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36)) !important;
  }
}
.t-switch__handle {
  top: 2px !important;
  width: 20px !important;
  height: 20px !important;

  left: 2px
}

.t-switch.t-is-checked .t-switch__handle {
  top: 2px;
  width: 20px;
  height: 20px;
  left: calc(100% - 2px);
}

.t-switch {
  min-width: 44px;
  background: var(--kyy-color-switch-brand-default, #D5DBE4);
}

.t-switch.t-is-checked:hover {
  background: var(--kyy-color-switch-brand-default, #4D5EFF);
}
