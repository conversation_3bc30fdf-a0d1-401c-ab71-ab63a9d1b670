<template>
  <div class="RK-Tabs">
    <TTabs
      v-bind="attrs"
      ref="tabsRef"
      :value="activeTab?.value"
      @change="handleChange"
    >
      <TTabPanel
        v-for="item in listInfo"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      >
        <template #label>
          <TPopconfirm
            v-if="item.popconfirm"
            v-bind="item.popconfirm"
            @confirm="item.popconfirm.onConfirm"
            @cancel="item.popconfirm.onCancel"
          >
            <span
              :class="`tab-${item.value}`"
              :data-value="item.value"
            >
              {{ item.label }}
            </span>
          </TPopconfirm>
          <span 
            v-else
            :class="`tab-${item.value}`" 
            :data-value="item.value"
          >
            {{ item.label }}
          </span>
        </template>
      </TTabPanel>
    </TTabs>
  </div>
</template>

<script setup lang="ts" name="RK-Tabs">
import { ref, watch, computed, onMounted } from 'vue';
import { Tabs as TTabs, TabPanel as TTabPanel, Popconfirm as TPopconfirm, TabValue } from 'tdesign-vue-next';
import { TabItemType } from './type';

const props = defineProps<{
  list: TabItemType[];
  defaultInfo?: TabItemType;
  attrs?: Record<string, any>;
}>();

const emit = defineEmits<{
  (e: 'change', value: TabValue): void;
}>();

const activeTab = ref<TabItemType>(props.defaultInfo || props.list[0]);
const activeTabElement = ref<HTMLElement | null>(null);
const tabsRef = ref<InstanceType<typeof TTabs> | null>(null);
const listInfo = ref(props.list);

watch(() => props.list, (newVal) => {
  listInfo.value = newVal;
  // 如果 activeTab 不在新的 list 中，则使用第一个值
  if (!newVal.find(item => item.value === activeTab.value?.value)) {
    activeTab.value = newVal[0];
  }
}, {
  immediate: true
});

// 监听外部 defaultInfo.value 的变化，动态更新 selectVal
watch(
  () => props.defaultInfo,
  (newVal) => {
    if (newVal && typeof newVal.value !== 'undefined') {
      if (newVal.value !== activeTab.value) {
        handleChange(newVal.value);
      }
    }
  }, {
    deep: true
  }
);

const translateX = computed(() => {
  if (!activeTabElement.value) return 0;
  const tabWidth = activeTabElement.value.offsetWidth;
  console.log('tabWidth:', tabWidth);

  // 获取当前 tab 的索引
  const tabs = tabsRef.value?.$el.querySelectorAll('.t-tabs__nav-item');
  if (!tabs) return 0;
  const currentIndex = Array.from(tabs).indexOf(activeTabElement.value);

  console.log('currentIndex', currentIndex);
  // 计算前面的 tab 宽度和间隔
  let offset = 32 * currentIndex;

  // 加上当前 tab 的一半宽度 减本身一半宽度
  offset += tabWidth / 2 - 8;
  console.log('offset:', offset);
  return offset;
});

const updateActiveTab = () => {
  if (!tabsRef.value) return;
  const target = tabsRef.value.$el.querySelector('.t-tabs__nav-item.t-is-active') as HTMLElement;
  if (target) {
    activeTabElement.value = target;
  }
};

onMounted(() => {
  updateActiveTab();
});

watch(() => props.defaultInfo?.value, () => {
  updateActiveTab();
});

const handleChange = (val: TabValue) => {
  activeTab.value = props.list.find(item => item.value === val) || props.defaultInfo;
  emit('change', val);
  // 等待 DOM 更新后再获取元素
  setTimeout(() => {
    updateActiveTab();
  }, 0);
};
</script>

<style lang="less">
.RK-Tabs{
  .t-tabs__nav-wrap {
    gap: 32px;
  }
  .t-tabs__bar{
    width: 16px !important;
    transform: translateX(v-bind('translateX + "px"'));
    border-radius: 1.5px;
    background: var(--brand-kyy_color_brand_default, #4D5EFF);
  }
  .t-tabs__nav-item{
    height: 56px !important;
    .t-tabs__nav-item-text-wrapper{
      margin: auto;
    }
    .t-tabs__nav-item-wrapper {
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      color: var(--text-kyy_color_text_1, #1A2139);
      line-height: 24px;
      padding: 0;
      // margin: 0 32px 0 0;
      min-width: 50px;
      &.t-is-active{
        font-weight: 600;
        color: var(--brand-kyy_color_brand_default, #4D5EFF);
      }
    }
  }
}
</style>
