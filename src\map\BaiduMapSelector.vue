<script setup lang="ts">
import { computed, nextTick, onBeforeMount, onMounted, PropType, ref, watch } from 'vue';
import debounce from 'lodash/debounce';
import { Dialog as <PERSON><PERSON><PERSON>, <PERSON><PERSON> as T<PERSON><PERSON>on, Link as TLink, Input as TInput, Loading as TLoading } from 'tdesign-vue-next';
import { useVModels } from '@vueuse/core';
import { BMap, BZoom, BMarker, useIpLocation, usePointGeocoder } from 'vue3-baidu-map-gl';
import { baiduMapSuggestion } from '../../axios/api';
import to from 'await-to-js';
import { mapOptions, markerIcon, covertData, openExternalMap, BAIDU_API_URL, BAIDU_AK } from './utils';
import { RkEmpty } from '../empty';
import { highlight } from '../../utils/format';
import type { Location, MapPoint, MapClickEvent, SearchResultItem, BaiduMapSearchResponse, MarkerInfo } from './types';

// HACK: md5 is not defined
// https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
let moduleObject: any;
onBeforeMount(() => {
  moduleObject = (window as any).module;
  // eslint-disable-next-line no-global-assign
  (window as any).module = undefined;
});
const onBMapInitdHandler = () => {
  // eslint-disable-next-line no-global-assign
  (window as any).module = moduleObject;
};

const props = defineProps({
  /** 显示 */
  visible: {
    type: Boolean,
    default: false,
  },
  /** 位置 */
  loc: {
    type: Object as PropType<Location>,
    default: () => ({}),
  },
  /** 仅显示 */
  onlyShow: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  /** 显示 */
  'update:visible': [visible: boolean];
  /** 确定 */
  'confirm': [data: MarkerInfo];
  /** 桌面端打开外部地图 */
  'open-external-map': [url: string];
}>();

const { visible } = useVModels(props, emit);

const mapVisible = ref(false);
onMounted(() => {
  mapVisible.value = true;
});

// 搜索信息
const keyword = ref('');
const loading = ref(false);
const listLoaded = ref(false);
const listLoading = ref(false);
// 搜索结果列表
const posList = ref<SearchResultItem[]>([]);
// 搜索结果选中项
const selectItem = ref<SearchResultItem | null>(null);

// 地图中心点
const center = ref<MapPoint>();
// 当前城市
const city = ref('');
// 地图缩放级别
const zoom = ref(10);
// 拖拽或点击地图得到的marker
const markerInfo = ref<MarkerInfo | null>(null);
// 标记点坐标
const markerPoint = ref<MapPoint | undefined>(center.value);

// 添加地图初始化状态
const mapInitialized = ref(false);

const locPoint = computed(() => {
  const { latLng } = props.loc || {};
  if (!latLng) return null;

  const { longitude, latitude } = latLng;
  if (!longitude || !latitude) return null;
  return { lat: latitude, lng: longitude };
});

// 定位到指定经纬度
const handleSelectedData = (loc: MapPoint | null): boolean => {
  if (!loc || !mapInitialized.value) return false;

  center.value = loc;
  markerPoint.value = loc;

  selectPOI(loc);
  getPointGeocoder(loc);
  return true;
};

// 由坐标点解析地址信息（逆向地理编码）
const { get: getPointGeocoder, isEmpty } = usePointGeocoder({}, (res) => {
  if (isEmpty.value) return;

  const data = covertData(res.value);
  if (selectItem.value) {
    const { name, address } = selectItem.value;
    markerInfo.value = {
      ...data,
      name,
      address: address || name,
      location: selectItem.value.location,
    };
  } else {
    markerInfo.value = data;
  }
});

// IP 定位（用于获取用户所在的城市位置信息，根据用户 IP 自动定位到城市）
const { get: getIPLocation, location } = useIpLocation(() => {
  if (!location.value || !mapInitialized.value) return;
  if (handleSelectedData(locPoint.value)) return;

  const { point, name } = location.value;
  center.value = point;
  city.value = name;
  zoom.value = 18;

  markerPoint.value = point;
  getPointGeocoder(point);
});

// 地图初始化
const mapInit = () => {
  onBMapInitdHandler();
  mapInitialized.value = true;

  if (handleSelectedData(locPoint.value)) return;

  // 不指定目标地址时，定位到当前城市中心
  getIPLocation();
};

// 点击地图设置新的标记点，并获取其详情地址
const mapClick = ({ latlng }: MapClickEvent) => {
  posList.value = [];

  if (props.onlyShow) {
    const { latLng, name } = props.loc || {};
    if (!latLng?.longitude || !latLng?.latitude) return;

    const { longitude: lng, latitude: lat } = latLng;
    openExternalMap(
      { lng, lat, name: name || '定位地址', title: name || '定位地址' },
      (url: string) => emit('open-external-map', url)
    );
    return;
  }

  selectPOI(latlng);
  getPointGeocoder(latlng);
};

// 搜索结果列表
const getPlaceList = async () => {
  mapVisible.value = false;
  listLoading.value = true;
  listLoaded.value = false;

  const [err, data] = await to(baiduMapSuggestion({
    q: keyword.value,
    region: city.value || '全国',
  }));

  listLoading.value = false;
  listLoaded.value = true;
  if (err) {
    console.error('搜索地点失败:', err);
    return;
  }

  const response = data as BaiduMapSearchResponse;
  if (response.status !== 0) {
    console.error('搜索地点失败:', response);
    return;
  }

  posList.value = response.result.filter((v: SearchResultItem) => v.location);
};

// 搜索关键字变化时，重新发起请求
const getPlaceListDebounce = debounce(() => {
  if (!keyword.value) {
    mapVisible.value = true;
    return;
  }

  posList.value = [];
  getPlaceList();
}, 400);

// 取消搜索
const cancelSearch = () => {
  keyword.value = '';
  mapVisible.value = true;
};

// 关闭弹窗事件
const onclose = () => {
  keyword.value = '';
};
// 手动关闭弹窗
const oncancel = () => {
  onclose();
  visible.value = false;
};

// 清空搜索条件后，重置为初始值
watch(keyword, (val) => {
  if (!val && markerPoint.value && !posList.value.length) {
    getPointGeocoder(markerPoint.value);
  }
});

// 标记并定位选中的点
const selectPOI = (item: MapPoint) => {
  markerPoint.value = item;
  center.value = item;
  zoom.value = 18;
  mapVisible.value = true;
};

// 获取搜索结果项实例（用于自动定位到所选项）
const itemRefs = ref<{ [key: string]: HTMLElement }>({});
const setItemRef = (el: HTMLElement | null, uid: string) => {
  if (el) itemRefs.value[uid] = el;
};

// 搜索结果项点击
const itemClick = async (item: SearchResultItem) => {
  selectItem.value = item;
  selectPOI(item.location);
  getPointGeocoder(item.location);

  await nextTick();
  itemRefs.value[item.uid]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
};

// 回显地址
watch(() => props.loc, (val) => {
  if (!val || !val.latLng || !mapInitialized.value) return;

  handleSelectedData(locPoint.value);
}, { immediate: false, deep: true });

// 监听地图初始化状态，初始化后处理地址回显
watch(mapInitialized, (initialized) => {
  if (initialized && props.loc?.latLng) {
    handleSelectedData(locPoint.value);
  }
});

// 提交地址
const submit = () => {
  if (markerInfo.value) {
    emit('confirm', markerInfo.value);
  }
  oncancel();
};
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    header="定位地址"
    width="672"
    attach="body"
    placement="center"
    prevent-scroll-through
    :footer="false"
    :close-on-overlay-click="false"
    :z-index="9999"
    class="b-map-selector-dialog"
    v-bind="$attrs"
    @close="onclose"
  >
    <div
      v-if="visible && !onlyShow"
      class="input-wrap"
    >
      <t-input
        v-model="keyword"
        placeholder="搜索地点"
        clearable
        autofocus
        @clear="cancelSearch"
        @input="getPlaceListDebounce"
      >
        <template #prefix-icon>
          <img
            src="./assets/icon_search.svg"
            alt=""
            class="icon-search"
          >
        </template>
      </t-input>

      <t-link
        v-if="keyword"
        theme="default"
        hover="color"
        class="ml-12"
        @click="cancelSearch"
      >
        取消
      </t-link>
    </div>

    <div
      v-if="visible"
      class="main-content"
    >
      <t-loading
        v-if="loading"
        class="my-20"
        text="搜索中..."
        size="small"
      />

      <div
        v-show="!loading && mapVisible"
        class="map-container"
      >
        <BMap
          :ak="BAIDU_AK"
          :api-url="BAIDU_API_URL"
          :center="center"
          :zoom="zoom"
          :height="214"
          enable-scroll-wheel-zoom
          @initd="mapInit"
          @click="mapClick"
        >
          <BZoom />
          <BMarker
            v-if="markerPoint"
            :position="markerPoint"
            :enable-clicking="false"
            :offset="{ x: -14, y: -48 }"
            :icon="markerIcon"
          />
        </BMap>
      </div>

      <!-- 搜索的列表 -->
      <div
        v-if="posList.length"
        class="search-result"
      >
        <div
          v-for="item in posList"
          :ref="(el) => setItemRef(el as HTMLElement, item.uid)"
          :key="item.uid"
          :class="['pos-item', { active: selectItem?.uid === item.uid }]"
          @click="itemClick(item)"
        >
          <img
            src="./assets/icon_orientation.svg"
            alt=""
            class="icon"
          >
          <div class="flex-1 w-0">
            <div
              class="line-1 mb-2"
              v-html="highlight(item.name, keyword)"
            />
            <div
              class="address line-1"
              v-html="highlight(item.address, keyword)"
            />
          </div>
        </div>

        <RkEmpty
          v-if="!posList.length && listLoaded && !mapVisible"
          name="no-result"
          center
        />
        <t-loading
          v-else-if="listLoading"
          text="加载中..."
          size="small"
        />
      </div>

      <!-- 地图上选点 -->
      <div
        v-else-if="markerInfo"
        class="search-result"
      >
        <div class="pos-item active">
          <img
            src="./assets/icon_orientation.svg"
            alt=""
            class="icon"
          >
          <div class="flex-1 w-0">
            <div
              v-if="markerInfo.name"
              class="line-1 mb-2"
            >
              {{ markerInfo.name }}
            </div>
            <div class="address line-1">
              {{ markerInfo.address }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="!onlyShow && (posList.length || markerInfo)"
      class="btns-wrap"
    >
      <t-button
        variant="outline"
        class="mr-8 min-w-80"
        @click="oncancel"
      >
        取消
      </t-button>
      <t-button
        theme="primary"
        :disabled="!(selectItem || markerInfo) || !mapVisible"
        class="min-w-80"
        @click="submit"
      >
        确定
      </t-button>
    </div>
  </t-dialog>
</template>

<style lang="less">
@import './styles.less';

.b-map-selector-dialog {
  .t-dialog {
    padding-bottom: 24px;
  }

  .t-dialog__close {
    color: var(--icon-kyy-color-icon-deep, #516082);
    width: 24px;
    height: 24px;
  }

  .t-dialog__body {
    display: flex;
    flex-direction: column;
    height: 488px;
    padding-top: 24px;
    padding-bottom: 0;
  }

  .main-content {
    flex: 1;
    // height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .input-wrap {
    margin-bottom: 16px;
    display: flex;
    .t-input__wrap {
      flex: 1;
    }
    input::placeholder {
      color: var(--text-kyy-color-text-5, #ACB3C0) !important;
    }
    .icon-search {
      width: 20px;
      height: 20px;
    }
    .btn-cancel {
      display: flex;
      align-items: center;
      padding-left: 12px;
      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
      text-align: center;
      font-size: 14px;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
    }
    .ml-12 {
      margin-left: 12px;
    }
  }

  .map-container {
    width: 100%;
    // height: 216px;
    // margin: 8px 0;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
    flex-shrink: 0;
    overflow: hidden;
    :deep(.amap-container) {
      border-radius: 8px;
    }
  }

  .search-result {
    overflow-y: auto;
    .pos-item {
      display: flex;
      align-items: center;
      padding: 12px;
      font-size: 16px;
      // height: 72px;
      gap: 12px;
      color: var(--text-kyy_color_text_1, #1A2139);
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      cursor: pointer;
      &.active {
        background: var(--bg-kyy_color_bg_list_foucs, #E1EAFF);
      }
      &:hover {
        background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      }
    }

    .icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      align-self: flex-start;
      margin-top: 3px
    }

    .address {
      font-size: 14px;
      color: var(--text-kyy_color_text_2, #516082);
    }

    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      color: var(--text-kyy-color-text-2, #516082);
      font-size: 14px;
      cursor: pointer;
    }
  }

  .t-loading {
    width: 100%;
    color: #2069E3;
    .t-loading__gradient-conic {
      background: conic-gradient(from 90deg at 50% 50%, rgba(161, 162, 164, 0) 0deg, rgb(4 85 248) 360deg)
    }
  }

  .btns-wrap {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
  }

  .scrollbar();
}
</style>
