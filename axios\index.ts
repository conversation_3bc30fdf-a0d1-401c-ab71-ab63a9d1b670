import axios from 'axios';
import CryptoJS from 'crypto-js';
import { getConfig } from '../utils';
import { getHeader, getOpenid, getTeamid } from '../utils/query';
import { getAccesstoken, getServerLang, getSignUrl } from './auth';
import { getApiConfig } from './baseUrl';

interface IRequest {
  method?: string;
  url: string;
  data?: any;
  params?: any;
  headers?: any;
}

/**
 * 封装方法
 * @param {*} method 请求方式
 * @param {*} url api
 * @param {*} source 来源: sd或者自己的来源
 * @param {*} type Content-type类型
 * @param {*} params get请求
 * @param {*} data
 * @param {*} headers
 * @returns
 */
export const createApiInstance = (baseUrl: string, request: IRequest) => {
  const { method = 'GET', url, params, data, headers = {} } = request;

  // 添加请求头；根据需求配饰即可
  let header: any = {
    // 'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    Authorization: `Bearer ${getAccesstoken()}`,
    'Phone-Type': 'web',
    'Accept-Language': getServerLang(),
    // 'Sm-Device-Id': '' // 待确定
  };

  // 添加请求头；根据需求配饰即可
  if (headers && Object.keys(headers).length) {
    header = Object.assign({}, header, headers);
  }

  // 是否有传递header，有就合并
  if (getHeader() && getHeader() !== '{}') {
    header = Object.assign({}, header, JSON.parse(getHeader() || '{}'));
  }

  // 拼装url-->pc是这样操作的
  const urls = params ? getSignUrl(url, params) : url;
  // 生成一个8位数的随机数
  const random = +String(Math.random()).slice(2, 10);
  // teamid,没有必须给空字符串
  const teamId = header?.teamId || header?.teamid || getTeamid() || '';
  // timestamp
  const timestamp = Date.now();
  // body
  const body = data ? JSON.stringify(data) : '';
  // openid 没有必须给空字符串
  const openid = getOpenid() || '';

  // 生成签名
  const hmac = CryptoJS.HmacSHA256(
    `${urls}${random}${teamId}${timestamp}${body}${openid}`,
    openid,
  );
  const sign = CryptoJS.enc.Base64.stringify(hmac);
  header['Signature'] = sign;

  return new Promise((resolve, reject) => {
    axios({
      url: baseUrl + url,
      method: method,
      data,
      params,
      headers: header,
    })
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      })
      .finally(() => {
        // console.log('不管是否成功都要执行')
      });
  });
};

const Env = () => getConfig().env;

export const iam_srvRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['iam-srv'], data);

export const im_syncRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['im-sync'], data);

export const organizeRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['organize'], data);

export const familiesRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['families'], data);

export const clientOrgRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['client-organize'], data);

export const simple_orgRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['client-organize'], data);

export const know_srvRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['know-srv'], data);

export const businessRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['business'], data);

export const orderRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['order'], data);

export const toolsRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['tools'], data);

export const globalRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['global'], data);

export const squareRequest = (data: IRequest) =>
  createApiInstance(getApiConfig(Env())['square'], data);
