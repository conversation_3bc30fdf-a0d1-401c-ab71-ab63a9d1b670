import { onMounted, onUnmounted } from 'vue';
import { getEnv } from '../base';
import type { MessageData } from '../types';

export type JsBridgeHandler = (
  messageData: MessageData,
  source: Window | null,
  origin: string,
) => Promise<void>;

/**
 * JSBridge消息监听 Hook
 * @param handler 消息处理函数
 */
export function useJsBridge(handler: JsBridgeHandler) {
  const messageListener = async (event: MessageEvent) => {
    try {
      const messageData: MessageData =
        typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

      if (!messageData?.action) return;

      const env = getEnv();
      await handler(messageData, env.isRingkolDesktopApp ? null : window, '*');
    } catch (error) {
      console.error('[useJsBridge] Message handler error:', error);
    }
  };

  onMounted(() => {
    window.addEventListener('message', messageListener);
  });

  onUnmounted(() => {
    window.removeEventListener('message', messageListener);
  });

  return () => {
    window.removeEventListener('message', messageListener);
  };
}
