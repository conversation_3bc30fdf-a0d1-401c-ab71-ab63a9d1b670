/**
 * title: 图片上传
 * description: 图片上传组件，支持图片选择、预览、删除、上传等功能（默认为单张上传）。
 */

<template>
  <div class="demo-wrap">
    <RkUploadImage
      v-model="imgUrls"
      root-dir="换成上传OSS根目录"
    />

    <div class="img-wrap">
      <img
        v-for="img in imgUrls"
        :key="img"
        :src="img"
        class="img"
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkUploadImage } from '@rk/unitPark';

const imgUrls = ref([]);
</script>

<style scoped lang="less">
.demo-wrap {
  display: flex;
  align-items: flex-start;
}

.upload-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.img {
  display: block;
  margin: 0 auto;
  width: 200px;
  height: 200px;
  object-fit: cover;
  border: 1px solid lightgray;
}
</style>
