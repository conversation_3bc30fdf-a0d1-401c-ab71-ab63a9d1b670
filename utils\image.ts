import { getFileExtension } from './file';

interface ImageInfo {
  width: number;
  height: number;
  size: number;
  ext: string | null;
  data: string;
}

/**
 * 获取图片信息
 * 该函数读取一个文件对象，并尝试获取其作为图片的信息，包括数据URL、宽度、高度和大小
 *
 * @param file 要读取的文件对象，应为图片文件
 * @returns 返回一个Promise对象，解析时包含图片的信息对象，包括data URL、宽度、高度和大小
 */
export const getImageInfo = (file: File): Promise<ImageInfo> =>
  new Promise((resolve, reject) => {
    // 创建FileReader对象用于读取文件
    const reader = new FileReader();
    // 读取文件为 ArrayBuffer
    reader.readAsArrayBuffer(file);

    // 当 FileReader 完成文件读取时触发
    reader.onload = (e) => {
      if (!e.target || !(e.target.result instanceof ArrayBuffer)) {
        reject(new Error('Failed to read file as ArrayBuffer'));
        return;
      }

      const arrayBuffer = e.target.result;

      // 创建图片对象并设置数据
      const imageObj = new Image();
      const imageUrl = URL.createObjectURL(file);
      imageObj.src = imageUrl;

      // 当图片对象成功加载图片时触发
      imageObj.onload = () => {
        try {
          // 将 ArrayBuffer 转换为 Blob 并生成 data URL
          const blob = new Blob([arrayBuffer], { type: file.type });
          const dataUrl = window.URL.createObjectURL(blob);

          const imageInfo: ImageInfo = {
            data: dataUrl,
            width: imageObj.width,
            height: imageObj.height,
            size: file.size,
            ext: getFileExtension(file.name),
          };

          // 释放对象 URL
          URL.revokeObjectURL(imageUrl);

          resolve(imageInfo);
        } catch (error) {
          URL.revokeObjectURL(imageUrl);
          reject(error);
        }
      };

      imageObj.onerror = (err) => {
        URL.revokeObjectURL(imageUrl);
        reject(new Error('Failed to load image'));
      };
    };

    reader.onerror = (evt) => {
      reject(evt.target.error || new Error('FileReader error'));
    };
  });

/**
 * 将 HTMLImageElement 转换为 Base64 编码的字符串。
 * @param img - HTMLImageElement 对象
 * @returns Base64 编码的字符串
 */
export const imageToBase64 = (img: HTMLImageElement): string => {
  if (!img.complete) {
    console.warn('图像未加载完成，无法转换为 Base64');
    return '';
  }

  try {
    const { canvas, context } = createCanvasContext(
      img.naturalWidth,
      img.naturalHeight,
    );
    context.drawImage(img, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('Canvas 操作失败', error);
    return '';
  }
};

/**
 * 将网络图片 URL 转换为 Base64 编码的字符串
 * @param url - 图片的 URL 地址
 * @returns Base64 编码的字符串 Promise
 */
export const urlToBase64 = (url: string): Promise<string> => {
  if (!url || typeof url !== 'string') return Promise.resolve('');

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';

    // 添加时间戳以解决缓存和跨域问题
    const timestampUrl = url.includes('?')
      ? `${url}&t=${+new Date()}`
      : `${url}?t=${+new Date()}`;

    img.onload = () => resolve(imageToBase64(img));
    img.onerror = (error) => {
      console.error('图片加载失败', error);
      reject('图片加载失败');
    };

    img.src = timestampUrl;
  });
};

/**
 * 将 Base64 编码的字符串转换为指定尺寸的 Data URL
 * @param base64Data - Base64 编码的字符串
 * @param width - 图片宽度
 * @param height - 图片高度
 * @returns 指定尺寸的 Data URL Promise
 */
export const convertBase64ToDataUrl = (
  base64Data: string,
  width: number,
  height: number,
  type = 'png',
): Promise<string> => {
  if (!base64Data || typeof base64Data !== 'string') {
    console.warn('无效的 Base64 数据');
    return Promise.resolve('');
  }

  if (width <= 0 || height <= 0) {
    console.warn('无效的宽度或高度');
    return Promise.resolve('');
  }

  return new Promise((resolve, reject) => {
    const image = new Image();
    image.src = base64Data;

    image.onload = () => {
      try {
        const { canvas, context } = createCanvasContext(width, height);
        context.drawImage(image, 0, 0, width, height);
        resolve(canvas.toDataURL(`image/${type}`));
      } catch (error) {
        console.error('Canvas 操作失败', error);
        reject(new Error('Canvas 操作失败'));
      }
    };

    image.onerror = (error) => {
      console.error('Base64 数据加载失败', error);
      reject(new Error('Base64 数据加载失败'));
    };
  });
};

/**
 * 创建并配置一个指定尺寸的 Canvas 元素及其 2D 上下文
 * @param width - Canvas 的宽度
 * @param height - Canvas 的高度
 * @returns 包含 Canvas 元素和 2D 上下文的对象
 */
const createCanvasContext = (width: number, height: number) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;

  const context = canvas.getContext('2d');
  if (!context) {
    console.error('无法获取 Canvas 上下文');
    throw new Error('无法获取 Canvas 上下文');
  }

  return { canvas, context };
};

/**
 * 检查给定的图片 URL 是否加载
 * @param url - 要检查的图像 URL
 * @returns Promise，为 true 表示加载成功
 */
export const checkImageLoaded = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};
