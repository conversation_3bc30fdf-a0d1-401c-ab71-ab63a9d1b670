<template>
  <div class="Tricks" v-if="show">
    <Draggable
      ref="DraggableRef"
      :class="className"
      :isDrag="isDrag"
      @click="onClick"
      @change="onChange"
      :styles="styles"
      >
    </Draggable>
    <!-- <Drawer
      v-model:visible="drawerVisible"
      :closeBtn="true"
      showOverlay
      closeOnOverlayClick
      :preventScrollThrough="false"
      :footer="false"
      :size="size"
      header="使用技巧"
      destroy-on-close
    >
      <iframe :src="drawerUrl" style="height:100%;width: 100%; border:none;"></iframe>
    </Drawer> -->
    <iframe v-if="drawerVisible" :src="drawerUrl" class="drawerIframe"></iframe>
  </div>
</template>

<script setup lang="ts" name="Tricks">
import { computed, onMounted, onUnmounted, PropType, ref, watch } from 'vue';
// import { Drawer } from 'tdesign-vue-next';
import jssdk from '@lynker-desktop/web';

import { getSkill } from '../../axios/api';
import { setAccesstoken } from '../../axios/auth';
import Draggable from '../../components/common/Draggable.vue';
import { getConfig } from '../../utils'
const props = defineProps({
  /**
   * 按钮样式
   */
   styles: {
    type: Object,
    default: () => {}
  },
  /**
   * 按钮位置 {x, y}
   */
   offset: {
    type: Object as PropType<{ x: number; y: number }>,
    default: () => ({ x: 0, y: 0 })
  },
  /**
   * uuid
   */
   uuid: {
    type: String,
    required: true
  },
  /**
   * 场景 - 2 桌面端 3 组织管理后台
   * (可不传，不传会自动判断)
   */
  scene: {
    type: String as PropType<'1' | '2' | '3'>,
  },
  /**
   * @description 按钮大小
   */
  size: {
    type: String as PropType<'small' | 'medium'> ,
    default: 'medium' ,
  },
  /**
   * 自定义点击事件
   */
  click: {
    type: Function as PropType<() => void>,
  },
  /**
   * 是否可以拖动
   */
  isDrag: {
    type: Boolean,
    default: true
  },
  /**
   * token（默认会获取， 也可以透传）
   */
   token: {
    type: String,
  },
  /**
   * 打开方式，web端默认drawer方式打开
   */
  openType: {
    type: () => 'drawer' || '',
  },
  /**
   * 不自动检查数据
   */
   noAutoCheck: {
    type: Boolean,
  },
  /**
   * 其他数据透传
   */
   data: {
    type: String,
  }
});
const emit = defineEmits<{
  /**
   * @description 按钮变化事件
   * @param event 变化信息
   */
  (e: 'change', event:any ): void
}>();
watch(() => props.uuid, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    getData()
  }
});
const show = ref(!!props.noAutoCheck);
const DraggableRef = ref<HTMLElement>(null);
const drawerVisible = ref(false);
const drawerUrl = ref('');
const styles = ref(null);
const className = computed(() => {
  const name:any = {
    btn: true,
    isDrag: !!props.isDrag
  }
  if (props.size){
    name[props.size] = true;
  }
  return name
});
const pageUrl = () => {
  const domain = getConfig().webUrl;
  let url = domain + '/common/index.html#/tricksPage?' + 'uuid=' + props.uuid ;
  const token = props.token || getConfig().token;
  url += '&scene=' + getScene();

  if (!getConfig().isRingkolDesktopApp) {
    url += '&openType=' + (props.openType || 'drawer');
    if (token) {
      url += '&token=' + token;
    }
  }
  if (props.data) {
    url += '&data=' + props.data;
  }
  return url;
}
const DraggableStyle = () => {
  const s = {
    transform: `translate3d(-52px, -40px, 0)`,
    ...props.styles
  }
  if (props.offset && typeof props.offset === 'object') {
    s.transform = `translate3d(${props.offset.x}px, ${props.offset.y}px, 0)`;
  }
  styles.value = s;
  return s
};

const getScene = () => {
  if (props.scene) return props.scene;

  return getConfig().isRingkolDesktopApp ? 2 : 3;
}
const onClick = () => {
  onChange({ status: 'click' })
  if (props.click) {
    props.click();
  } else {
   // 打开使用技巧页面
   // 判断内部外部
    if (getConfig().isRingkolDesktopApp) {
      const LynkerSDK = window.LynkerSDK;
      // 桌面端
      LynkerSDK.openNewWindow({
        url: pageUrl(),
        name: 'tricks',
        browserWindow: {
          titleBarStyle: "hidden",
          width: 728,
          height: 720,
          resizable: false,
          movable: true,
          useContentSize: true,
          autoHideMenuBar: true,
          frame: false,
          show: true,
          webPreferences: {
            preload: ``,
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false,
          },
        },
      }).then( res => {
        onChange({ status: 'openWindowDone' })
      })
    } else {
      // web
      drawerUrl.value = pageUrl();
      // drawerUrl.value = 'http://localhost:8080/common/index.html#/tricksPage?openType=drawer&uuid=IM-%E5%B8%B8%E7%94%A8&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHAiOiJSSU5HS09MIiwiem9uZSI6IkNOIiwib3BlbmlkIjoiMHVhZGMwMDAxYTdjeWs3eTI2IiwiZmVhdGhlciI6Ijc1ODQxOTMxNzMiLCJwbGF0Zm9ybSI6IlBDIn0.iMRZDLbQb80ePKdZhCV31YW5DJFnewsFLfAhq1sL8I4'
      drawerVisible.value = true;
    }
  }
}
const onChange = (e:any) => {
  emit('change', e);
};
const onPostMessage = (e:any) => {
  // 页面接收信息
  try{
    console.log('message=====>', e.origin, e.data) // origin 子页面所在的域 data子页面发送的消息, hello, parent!
    const data = JSON.parse(e?.data||{});
    if (data?.type === 'closeDrawer') {
      setTimeout(() => {
        drawerVisible.value = false;
      }, 200)
    }
  } catch(err){
    console.log('err', err)
  }
}
const getData = () => {
  if (props.noAutoCheck) {
    return;
  }
  if (props.token) {
    setAccesstoken(props.token);
  }
  getSkill({
    uuid: props.uuid,
    terminal: getScene(),
  }).then((res:any) => {
    const data = res.data;
    if (res.code === 0 && data.list && data.list.length > 0) {
      show.value = true;
    } else {
      show.value = false;
    }
    onChange({ show: show.value })
  })
}

onMounted(() => {
  getData();
  DraggableStyle();
  if (!getConfig().isRingkolDesktopApp) {
    window.addEventListener('message', onPostMessage, false);
  }
})
onUnmounted(() => {
  console.log('destroyed')
  // 销毁事件监听
  if (!getConfig().isRingkolDesktopApp) {
    window.removeEventListener('message', onPostMessage, false);
  }
})
</script>

<style lang="less" scoped>
.Tricks{
  .btn{
    background: url('./assets/medium_icon.png') no-repeat center;
    background-size: 100%;
    cursor: pointer;
    position: relative;
    z-index: 1008;
    &.medium{
      width: 48px;
      height: 48px;
    }
    &.small{
      background: url('./assets/small_icon.png') no-repeat center;
      width: 24px;
      height: 24px;
      background-size: 100%;
    }
    &.isDrag{
      position: fixed;
      bottom: 0;
      right: 0;
    }
  }
  .drawerIframe {
    position: fixed;
    top: 0;
    left: 0;
    height:100%;
    width: 100%;
    border:none;
    z-index: 10011;
  }
}
</style>
<style>
.Tricks{
  .t-drawer__header{
    padding: 16px 24px;
    height: 56px;
    color: #1A2139;
    border: none;
  }
  .t-drawer__body{
    padding: 0;
  }
  .t-drawer__close-btn{
    height: 24px;
    width: 24px;
    background: url('../../assets/icon_close.png') no-repeat center;
    background-size: 100%;
  }
}
</style>
