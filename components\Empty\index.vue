<!--
 * @Author: ziwen.ji <EMAIL>
 * @Date: 2024-06-19 14:40:48
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-06-21 10:05:59
 * @FilePath: /lynker-desktop/src/renderer/components/rk-common/rk-empty/index.vue
 * @Description: 缺省组件
-->
<template>
  <div class="rk-wrap">
    <img
      v-if="img"
      :src="img"
      ondragstart="return false"
      :style="iconStyles"
    >
    <slot v-else>
      <!-- <SvgIcon :name="iconName" :style="iconStyles" /> -->
      <img
        :src="getImgSrc()"
        :style="iconStyles"
      >
    </slot>

    <div class="rk-tip">
      <slot name="tip">
        {{ tip }}
      </slot>
    </div>

    <slot name="bottom" />
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { tipMap, emptyProps } from './constant';
// import SvgIcon from "../common/SvgIcon.vue";

const props = defineProps(emptyProps);

// const iconName = computed(() => `empty-${props.name}`);

// 获取图片路径
function getImgSrc() {
  const name = props.name || 'no-data';
  // 使用public目录下的图片
  return `https://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/empty/${name}.svg`;
}

const iconStyles = computed(() => ({ width: props.width, height: props.height }));
const tip = computed(() => props.tip || tipMap[props.name] || tipMap['no-data']);
</script>
<style lang="less" scoped>
.rk-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 16px;
  margin-bottom: 16px;
}

.rk-tip {
  margin-top: 12px;
  text-align: center;
  color: var(--lingke-contain-fonts, #516082);
}
</style>
