import { RouteRecordRaw } from 'vue-router'
import notFound from '@renderer/views/404.vue'
import landingPage from '@renderer/views/landing-page/landing-page.vue'
import cloudDisk from '@renderer/views/cloudDisk/App.vue'

const routes: Array<RouteRecordRaw> = [
  { path: '/cloudDiskApp', name: '云盘', component: cloudDisk },
  { path: '/:pathMatch(.*)*', component: notFound },
  { path: '/', name: '总览', component: landingPage },
]

export default routes
