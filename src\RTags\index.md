# RTags

标签组件

```jsx
import { ref } from 'vue';
import { RTags } from '@rk/unitPark';
import { TagNameEnum, TagTypeEnum } from './type.ts';
import './style.css';

const list = ref([
  {
    name: TagNameEnum.gray,
    label: '灰色标签',
    type: TagTypeEnum.round,
  },
  {
    name: TagNameEnum.purple,
    label: '紫色标签',
    type: TagTypeEnum.square,
  },
  {
    name: TagNameEnum.cyan,
    label: '青色标签',
    type: TagTypeEnum.round,
  },
  {
    name: TagNameEnum.success,
    label: '成功标签',
    type: TagTypeEnum.square,
  },
  {
    name: TagNameEnum.wrong,
    label: '错误标签',
    type: TagTypeEnum.round,
  },
  {
    name: TagNameEnum.warning,
    label: '警告标签',
    type: TagTypeEnum.square,
  },
  {
    name: TagNameEnum.brand,
    label: '品牌标签',
    type: TagTypeEnum.round,
  },
  {
    name: TagNameEnum.magenta,
    label: '洋红标签',
    type: TagTypeEnum.square,
  },
  {
    name: TagNameEnum.gray,
    label: '灰色方形标签',
    type: TagTypeEnum.square,
  },
  {
    name: TagNameEnum.success,
    label: '成功圆形标签',
    type: TagTypeEnum.round,
  },
  {
    name: TagNameEnum.warning,
    label: '警告圆形标签',
    type: TagTypeEnum.round,
  },
  {
    name: TagNameEnum.magenta,
    label: '洋红圆形标签',
    type: TagTypeEnum.round,
  },
]);
const click = (info) => {
  // console.log(info.data.label)
  alert(info.data.label);
};
export default () => (
  <>
    <RTags list={list} onClick={click}></RTags>
  </>
);
```

#### 自定义 CSS：

```css
.RK-Tags .tags-wrap .t-tag {
  margin-bottom: 10px;
}
```

### Props

<API id="RTags" type="props"></API>

### Events

<API id="RTags" type="events"></API>
