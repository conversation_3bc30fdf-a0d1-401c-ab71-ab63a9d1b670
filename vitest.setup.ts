import { vi } from 'vitest';

// 创建基础的 window mock
const createWindowMock = () => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  postMessage: vi.fn(),
  parent: null,
  opener: null,
  name: 'test-window',
  LynkerSDK: {
    ipcRenderer: {
      invoke: vi
        .fn()
        .mockResolvedValue({ code: 0, data: null, message: 'success' }),
    },
  },
  __APP_ENV__: {
    VITE_APP_CONFIG_INFO: true,
  },
});

// 设置全局 mock
beforeEach(() => {
  const windowMock = createWindowMock();
  // 设置自引用
  windowMock.parent = windowMock;

  // 设置全局 window
  vi.stubGlobal('window', windowMock);
});

// 清理 mock
afterEach(() => {
  vi.unstubAllGlobals();
});
