<template>
  <div class="demo-container">
    <h4>自定义高亮类名</h4>
    <div class="demo-item">
      <p>原文：{{ content }}</p>
      <p>
        红色高亮：
        <RkHighlight
          :content="content"
          keyword="Vue"
          highlight-class="red-highlight"
        />
      </p>
      <p>
        绿色高亮：
        <RkHighlight
          :content="content"
          keyword="JavaScript"
          highlight-class="green-highlight"
        />
      </p>
      <p>
        渐变高亮：
        <RkHighlight
          :content="content"
          keyword="框架"
          highlight-class="gradient-highlight"
        />
      </p>
    </div>

    <t-divider />

    <h4>自定义高亮样式</h4>
    <div class="demo-item">
      <p>原文：{{ content2 }}</p>
      <p>
        内联样式高亮：
        <RkHighlight
          :content="content2"
          keyword="前端"
          :highlight-style="{
            color: '#ffffff',
            backgroundColor: '#ff6b6b',
            padding: '2px 6px',
            borderRadius: '12px',
            fontWeight: 'bold',
            fontSize: '14px'
          }"
        />
      </p>
      <p>
        下划线样式：
        <RkHighlight
          :content="content2"
          keyword="开发"
          :highlight-style="{
            color: '#1890ff',
            textDecoration: 'underline',
            textDecorationColor: '#1890ff',
            textDecorationThickness: '2px',
            fontWeight: '600'
          }"
        />
      </p>
    </div>

    <t-divider />

    <h4>多样式组合</h4>
    <div class="demo-item">
      <p>原文：{{ content3 }}</p>
      <p>
        组合效果：
        <RkHighlight
          :content="content3"
          keyword="React"
          highlight-class="custom-highlight"
          :highlight-style="{ border: '1px solid #ff4d4f' }"
        />
      </p>
    </div>

    <t-divider />

    <h4>不同场景应用</h4>
    <div class="demo-item">
      <div class="scenario-card">
        <h5>搜索结果高亮</h5>
        <p>
          <RkHighlight
            :content="searchResult"
            keyword="搜索"
            highlight-class="search-highlight"
          />
        </p>
      </div>

      <div class="scenario-card">
        <h5>代码关键词高亮</h5>
        <p>
          <RkHighlight
            :content="codeExample"
            keyword="const"
            highlight-class="code-highlight"
          />
        </p>
      </div>

      <div class="scenario-card">
        <h5>警告文本高亮</h5>
        <p>
          <RkHighlight
            :content="warningText"
            keyword="注意"
            highlight-class="warning-highlight"
          />
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RkHighlight } from '../index';
import { Divider as TDivider } from 'tdesign-vue-next';

const content = 'Vue.js 是一个用于构建用户界面的渐进式 JavaScript 框架。';
const content2 = '前端开发需要掌握 HTML、CSS 和 JavaScript 等核心技术。';
const content3 = 'React 是由 Facebook 开发的用于构建用户界面的 JavaScript 库。';
const searchResult = '搜索到 15 个相关结果，包含关键词"搜索"的文档。';
const codeExample = 'const message = "Hello World"; const count = 42;';
const warningText = '注意：此操作不可逆，请谨慎操作。';
</script>

<style lang="less" scoped>
@import '../../demo-common.less';

.scenario-card {
  padding: 12px;
  margin: 8px 0;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  background-color: var(--td-bg-color-container);
}
</style>

<!-- 全局样式，无需使用 :deep() -->
<style lang="less">
// 自定义高亮样式
.red-highlight {
  color: #ffffff !important;
  background-color: #ff4d4f;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.green-highlight {
  color: #ffffff !important;
  background-color: #52c41a;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.gradient-highlight {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  color: #ffffff !important;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.custom-highlight {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f !important;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 500;
}

.search-highlight {
  background-color: #fff3cd;
  color: #856404 !important;
  padding: 1px 3px;
  border-radius: 2px;
  font-weight: 500;
}

.code-highlight {
  background-color: #f6f8fa;
  color: #d73a49 !important;
  padding: 1px 3px;
  border-radius: 2px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-weight: 600;
  border: 1px solid #e1e4e8;
}

.warning-highlight {
  background-color: #fff2f0;
  color: #cf1322 !important;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 600;
  border: 1px solid #ffccc7;
}
</style>
