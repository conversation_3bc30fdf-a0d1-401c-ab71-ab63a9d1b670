<script setup lang="ts">
import { getLocalLinkSquares } from '@renderer/api/square/square';
import { useQuery } from '@tanstack/vue-query';
import { computed } from 'vue';
import { useTabsStore } from '@renderer/components/page-header/store';
import { useRouter } from 'vue-router';
import LocalPostItem from './LocalPostItem.vue';
import LocalPostItemSkeleton from './LocalPostItemSkeleton.vue';
import { useSquareStore } from '../../store/square';

const router = useRouter();
const tabStore = useTabsStore();
const store = useSquareStore();

const point = computed(() => {
  if (!store.location?.point) return;
  const p = store.location?.point;
  return {
    'latLng.latitude': p.lat,
    'latLng.longitude': p.lng,
  };
});

const pageSize = 4;
const queryParams = computed(() => ({
  'pagination.number': 1,
  'pagination.size': pageSize,
  ...(point.value || {}),
}));

const { data, isLoading } = useQuery({
  queryKey: ['localLinkSquares', queryParams],
  enabled: computed(() => !!point.value),
  queryFn: () => getLocalLinkSquares(queryParams.value),
  select: (res) => res.data.data,
  refetchOnWindowFocus: false,
});

const total = computed(() => data.value?.pagination?.total || 0);

const handleGoMore = () => {
  const fullPath = '/square/local-post';
  tabStore.addTab({ label: '本地通', fullPath });
  router.push(fullPath);
  tabStore.activeIndex = tabStore.tabs.length - 1;
};
</script>

<template>
  <div class="local-post-lite">
    <div class="header">本地通<div v-if="total > pageSize" class="btn-more" @click="handleGoMore">更多<iconpark-icon name="iconarrowright" class="icon" /></div></div>
    <div class="content">
      <LocalPostItemSkeleton v-if="isLoading" :count="2" width="280px" />

      <LocalPostItem
        v-for="(item, index) in data?.squares || []"
        :key="index"
        :square="item"
        class="w-278"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.local-post-lite {
  display: flex;
  padding-bottom: 8px;
  flex-direction: column;
  gap: 8px;
  border-radius: 8px;
  background: #FFF;
  margin-top: 16px;
}

.btn-more {
  display: flex;
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  padding: 0 4px 0 8px;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  &:hover {
    cursor: pointer;
    background: var(--bg-kyy_color_bgBrand_foucs, #DBDFFF);
  }
  .icon {
    color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
    font-size: 20px;
  }
}

.header {
  display: flex;
  height: 48px;
  padding: 12px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 8px;
  background: linear-gradient(104deg, #FEE9D8 9.81%, rgba(255, 234, 216, 0.30) 29.31%);
  color: var(--text-kyy_color_text_1, #1A2139);
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}

.content {
  display: flex;
  width: 100%;
  padding: 0 8px;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
