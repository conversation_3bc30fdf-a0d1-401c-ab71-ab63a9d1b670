/**
 * @file JSBridge基础通信模块
 */

import { getConfig } from '../index';
import { CommunicationManager } from './communication/manager';
import { IpcResponse, MessageData } from './types';

export interface Environment {
  isElectron: boolean;
  isWeb: boolean;
  isRingkolDesktopApp: boolean;
}

export const getEnv = (): Environment => {
  const isRingkolDesktopApp = getConfig().isRingkolDesktopApp;
  const hasAppEnv = Boolean(window?.__APP_ENV__?.VITE_APP_CONFIG_INFO);
  const isParentWindow = !window.parent;

  return {
    isElectron: isRingkolDesktopApp,
    isWeb: !hasAppEnv,
    isRingkolDesktopApp: isParentWindow && hasAppEnv && isRingkolDesktopApp,
  };
};

export class JSBridgeBase {
  protected static instance: JSBridgeBase;
  private readonly communicationManager: CommunicationManager;

  protected constructor() {
    this.communicationManager = new CommunicationManager();
  }

  public static getInstance(): JSBridgeBase {
    if (!JSBridgeBase.instance) {
      JSBridgeBase.instance = new JSBridgeBase();
    }
    return JSBridgeBase.instance;
  }

  public getEnv() {
    return getEnv();
  }

  // 添加 sdk 方法
  public sdk(data?: any): any {
    return this.communicationManager.sdk(data);
  }

  public async send<T = any>(
    action: string,
    data?: any,
    target?: Window | HTMLIFrameElement,
  ): Promise<IpcResponse<T>> {
    return this.communicationManager.send<T>(action, data, target);
  }

  public onMessage(
    handler: ((messageData: MessageData) => Promise<void>) | null,
  ): void {
    this.communicationManager.onMessage(handler);
  }
}

export default JSBridgeBase;
