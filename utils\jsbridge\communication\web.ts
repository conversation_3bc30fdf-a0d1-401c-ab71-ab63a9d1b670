/**
 * @file Web环境通信实现
 */

import type {
  CommunicationTarget,
  IpcResponse,
  MessageData,
  MessageHandler,
} from '../types';

export class WebCommunicator {
  private messageHandlers: Set<MessageHandler> = new Set();
  private messageListener: (event: MessageEvent) => void;

  constructor() {
    this.messageListener = (event: MessageEvent) => {
      try {
        const messageData: MessageData =
          typeof event?.data === 'string' ? JSON.parse(event.data) : event.data;

        this.messageHandlers.forEach(async (handler) => {
          if (handler) {
            try {
              await handler(messageData);
            } catch (error) {
              console.error('[WebCommunicator] Handler error:', error);
            }
          }
        });
      } catch (error) {
        console.error('[WebCommunicator] Message parsing error:', error);
      }
    };

    window.addEventListener('message', this.messageListener);
  }

  public onMessage(handler: MessageHandler): void {
    if (handler === null) {
      this.messageHandlers.clear();
      window.removeEventListener('message', this.messageListener);
      return;
    }
    this.messageHandlers.add(handler);
    if (!this.messageListener) {
      window.addEventListener('message', this.messageListener);
    }
  }

  public removeMessageHandler(handler: MessageHandler): void {
    this.messageHandlers.delete(handler);
  }

  public destroy(): void {
    window.removeEventListener('message', this.messageListener);
    this.messageHandlers.clear();
  }

  /**
   * 发送消息
   */
  public async send<T = any>(
    messageData: MessageData,
    target?: CommunicationTarget,
  ): Promise<IpcResponse<T>> {
    console.log('web send:', messageData, target);
    try {
      if (target?.window) {
        await this.sendMessageWithRetry(messageData, target);
      } else if (window.parent !== window) {
        const message = JSON.stringify(messageData);
        window.parent.postMessage(message, '*');
      } else {
        // 如果既不是父窗口通信也没有指定目标，可能需要抛出错误或返回特定状态
        throw new Error('No valid message target found');
      }

      return {
        code: 0,
        data: null,
        message: 'Message sent successfully',
      };
    } catch (error) {
      console.error('[WebCommunicator] Send error:', error);
      return {
        code: -1,
        data: null,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async sendMessageWithRetry(
    messageData: MessageData,
    target: CommunicationTarget,
    retries = 0,
  ): Promise<void> {
    try {
      const message = JSON.stringify(messageData);
      if (target?.window?.postMessage) {
        target.window.postMessage(message, target.origin);
      } else {
        throw new Error('Invalid target window');
      }
    } catch (error) {
      if (retries < this.maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, this.retryDelay));
        return this.sendMessageWithRetry(messageData, target, retries + 1);
      }
      throw error;
    }
  }
}
