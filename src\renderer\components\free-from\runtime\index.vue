<template>
  <div class="init_apply">
    <div class="content-box">
      <t-form
        ref="formRef"
        :rules="typesObj.rules"
        :data="formData"
        label-align="top"
        scroll-to-first-error="smooth"
        @validate="onValidate"
        @submit="onSubmit"
      >
        <div class="content-box-top">
          <template
            v-for="(item, index) in approvalData.free_form?.filter(
              (f) => f.show
            )"
            :key="index"
          >
            <component
              :is="Runtimes[item.type]"
              :attrs="item"
              :free_form="approvalData.free_form"
              :form-data="formData"
              @controls-event="controlEvent"
              @extend-event="extendEvent"
              @contact-add-event="contactAddEvent"
              @contact-edit-event="contactEditEvent"
              @attrs-fn="attrsFn"
              @field-list-update="fieldListUpdate"
              @select-change="selectChange"
              @clear-validate="clearValidate"
              @deselect-change="deselectChange"
              @reset-valid="resetValid"
            />
          </template>
        </div>
      </t-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUpdated, watch, onActivated, Ref, onMounted } from "vue";

import { MessagePlugin } from "tdesign-vue-next";
import { useRoute } from "vue-router";

import lodash from "lodash";
import { Runtimes } from "./controls/index";
// import {
//   typesObj.setFreeForm,
//   typesObj.setCalculator,
//   selectChange,
//   deselectChange,
//   typesObj.fieldListUpdate,
//   typesObj.initApprovalData,
//   rules,
// } from "./types";

import { TypesObj } from "./typesObj";
const typesObj = new TypesObj(); // 避免多次引入共用一个数据源

const formData = ref({});
const formRef = ref(null);
const props = defineProps({
  widgets: {
    type: Array,
    default: () => [],
  },

  loading: {
    type: Boolean,
    default: false
  }
});
const approvalData: any = ref({ free_form: props.widgets });

watch(
  () => props.widgets,
  (newValue) => {
    console.log("newValue", newValue);

    approvalData.value.free_form = newValue;
    init();
  }
);

const selectChange = (e: any) => {
  typesObj.selectChange(e);
};

const clearValidate = (name) => {
  formRef.value.clearValidate([name]);
};

const deselectChange = (e: any) => {
  typesObj.deselectChange(e);
};

const fieldListUpdate = (e: any) => {
  typesObj.fieldListUpdate(e);
};

onMounted(() => {
  init();
});
onActivated(() => {});

onUpdated(() => {
  console.log("gengxin");
});

const depId: Ref<string | string[] | number> = ref(1);
const jobId: Ref<string | string[] | number> = ref("0");
const reSubmitFirst = ref(true);
const approvalInfoVisible = ref(false);

const init = async () => {
  const { departmentId } = route.query;
  if (departmentId) {
    depId.value = departmentId;
  }
  if (route.query.jobId) {
    jobId.value = route.query.jobId;
  }

  console.log("approvalData.value.free_form", approvalData.value.free_form);

  typesObj.setFreeForm(approvalData.value.free_form);
  // let rule =
  typesObj.setCalculator(approvalData.value.free_form);
  typesObj.initApprovalData(approvalData.value);

  console.log("formData", formData.value);
  console.log("approvalData", approvalData.value);
  const allFields = approvalData.value.free_form.concat(
    approvalData.value.free_form
      .filter((f) => f.type === "FieldList")
      .map((f) => f.value)
      .flat()
      .flat()
  );
  if (!allFields.find((item) => item.required)) {
    approvalInfoVisible.value = true;
  }
  dispFreeFormReadable();
  typesObj.fieldListUpdate(approvalData.value);
};

const isRequiredFieldsFilled = () => {
  const fields = approvalData.value.free_form;
  const allRequiredFields = fields
    .concat(
      fields
        .filter((f) => f.type === "FieldList")
        .map((f) => f.value)
        .flat()
        .flat()
    )
    .filter((f) => f.readable && f.required && f.show);
  for (const field of allRequiredFields) {
    // 特殊的field
    if (field.type === "DateRangePicker") {
      if (
        field.dateType === 0 &&
        (!field.startTimeValue || !field.endTimeValue || !field.durationValue)
      ) {
        return false;
      }
      if (
        field.dateType !== 0 &&
        (!field.startTimeValue ||
          !field.startTimeValue2 ||
          !field.endTimeValue ||
          !field.endTimeValue2 ||
          !field.durationValue)
      ) {
        return false;
      }
    } else if (lodash.isArray(field.value)) {
      // value为数组的field
      if (field.value.length === 0) {
        return false;
      }
    } else if (
      field.value === null ||
      field.value === undefined ||
      field.value === ""
    ) {
      // 普通的field
      return false;
    }
  }
  return true;
};

// 处理表单控件关联问题 控件被关联时passiveRelevance为true
const dispFreeFormReadable = () => {
  const allFields = approvalData.value.free_form;
  console.log("OAInitiateApproval", approvalData.value.free_form);
  for (const some of allFields) {
    some.show = !some.passiveRelevance;
  }
};

const route = useRoute();

const attrsFn = (value) => {
  console.log("attrs", value);
  if (value) {
    approvalData.value.free_form.forEach((item) => {
      if (item.id === value.id) {
        item = value;
        formData.value[value.id] = value.value;
      }
    });
  }
};

const onValidate = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    console.log("Validate Success");
  } else {
    console.log("Validate Errors: ", firstError, validateResult);
  }
};

const emits = defineEmits([
  "release",
  "contact-add-event",
  "contact-edit-event",
  "extend-event",
  "controls-event",
]);
const onSubmit = async ({ validateResult, firstError }) => {
  console.log("validateResult", validateResult);
  console.log("firstError", firstError);
  console.log("formData", formData.value);
  if (validateResult === true) {
    emits("release", approvalData.value);
  } else {
    console.log("Errors: ", validateResult);
    MessagePlugin.warning(firstError);
  }
};

const submitRun = () => {
  formRef.value.validate().then((validateResult) => {
    if (validateResult && Object.keys(validateResult).length) {
      const firstError = Object.values(validateResult)[0]?.[0]?.message;
      MessagePlugin.warning(firstError);
    } else {
      emits("release", approvalData.value);
    }
  });
};

const controlEvent = (e) => {
  console.log(e);
  emits("controls-event", e);
};

const extendEvent = (e) => {
  emits("extend-event", e);
};

const contactEditEvent = (e) => {
  emits("contact-edit-event", e);
};

const contactAddEvent = (e) => {
  emits("contact-add-event", e);
};

const resetValid = (arr) => {
  formRef.value.clearValidate({ rules: arr });
};

defineExpose({
  submitRun,
  clearValidate
});
</script>

<style lang="less" scoped>
:deep(.t-form__label) {
  // word-wrap: break-word;
  // white-space: wrap !important;
  label {
    display: flex;
    align-items: center;
    // margin-bottom: 8px;
  }
  color: var(--text-kyy-color-text-3, #828da5);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  // min-height: 22px;
}
:deep(.t-form:not(.t-form-inline)) .t-form__item:last-of-type {
  margin-bottom: 24px !important;
}

.init_apply {
  .head-text {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
    padding: 20px 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .items:hover {
    background: #daecff !important;
    color: #2069e3 !important;
  }

  .right-content {
    border-radius: 4px;
    //height: calc(100% - 143px);
    padding: 0 16px;

    .flex-wrap {
      display: flex;
      flex-wrap: wrap;
      //justify-content: space-between;
      //align-content: space-between;
    }

    .box-card {
      display: flex;
      height: 66px;
      width: 24%;
      border: 1px solid #e3e6eb;
      border-radius: 4px;
      background: #fff;
      padding: 13px 12px;
      margin-bottom: 8px;
      margin-right: 8px;

      .icon-img {
        width: 40px;
        height: 40px;
        margin-right: 12px;
      }
    }
  }

  .isactive {
    background: #daecff !important;
    color: #2069e3 !important;
    border-radius: 4px;
  }

  .top-box {
    position: relative;
    height: 64px;
    z-index: 999;
    width: 100%;
    background: #ffffff;
    box-shadow: 0px -1px 0px 0px #e3e6eb inset, -1px 0px 0px 0px #e3e6eb inset;
    display: flex;
    align-items: center;
    justify-content: center;

    .t-icon-left {
      position: absolute;
      left: 16px;
      top: 20px;
    }

    div {
      font-size: 16px;
      font-weight: 700;
      color: #13161b;
    }
  }

  .content-box {
    // overflow-y: scroll;
    // height: calc(100% - 100px);
    // padding: 16px 144px;
    // background: #f1f2f5;

    .content-box-top {
      // padding: 24px 32px;
      border-radius: 4px;
      //margin-top: 89px;
      background: #fff;

      .content-box-title {
        font-size: 24px;
        font-weight: 700;
        color: #13161b;
        line-height: 32px;
      }

      .organization-name,
      .department-name {
        display: inline-block;
        margin-top: 8px;
        font-size: 14px;
        font-weight: 400;
        text-align: left;
        color: #717376;
        line-height: 22px;
        margin-right: 48px;
      }
    }

    .content-box-bottom {
      margin-top: 12px;
      //padding: 24px 32px;
      border-radius: 4px;
      background: #fff;

      .title {
        font-size: 16px;
        font-weight: 700;
        text-align: left;
        color: #13161b;
        line-height: 24px;
      }

      .tips {
        font-size: 12px;
        font-weight: 400;
        text-align: left;
        color: #717376;
        line-height: 20px;
        margin-top: 8px;
      }
    }
  }

  .blue-head {
    display: flex;
    margin-bottom: 16px;

    .imgs {
      width: 16px;
      height: 16px;
      display: block;
      margin-top: 3px;
      margin-right: 8px;
    }

    .dis-text {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #13161b;
    }
  }

  .btn {
    cursor: pointer;
    color: #2069e3;
  }

  .blue-head {
    width: 440px;
    height: 60px;
    background: #f0f8ff;
    border-radius: 4px;
    padding: 8px 16px;
  }

  .lin {
    width: 1px;
    height: 80px;
    background: #e3e6eb;
    margin-right: 16px;
  }

  .head-right-content-box {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e3e6eb;
    margin-bottom: 24px;
    padding-top: 32px;
    padding-bottom: 32px;
    height: 153px;
  }

  .flexbox-j-s {
    display: flex;
    justify-content: space-between;
  }

  .flexbox {
    display: flex;
  }

  .head-table-title {
    height: 22px;
    font-size: 14px;

    font-weight: 700;
    color: #13161b;
    line-height: 22px;
    margin-bottom: 16px;
  }

  .dilatation {
    margin-bottom: 21px;
    // padding: 0 16px;
    .text-num {
      height: 20px;
      font-size: 12px;

      font-weight: 400;
      color: #717376;
      line-height: 20px;
    }

    .kr {
      margin: 0 12px 0 33px;
    }
  }

  .table-view-r-item-file-img {
    text-align: center;
    margin-top: -20px;

    img {
      width: 72px;
      height: 72px;
    }

    .textovf {
      width: 132px;
      font-size: 14px;

      font-weight: 400;
      text-align: center;
      margin: 0 auto;
      color: #13161b;
      line-height: 22px;
      // white-space: nowrap; // 强制一行显示
      // overflow: hidden; // 超出隐藏
      // text-overflow: ellipsis; // 省略号
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 超出几行省略 */
      overflow: hidden;
    }
  }

  .flex-align-jsb {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
  }

  .flex-align {
    display: flex;
    align-items: center;
  }

  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //background: #fff;
    padding: 16px 16px 12px;

    .all-text {
      font-size: 16px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      line-height: 24px;
    }
  }
}

:deep(.t-is-success) {
  //display: none;
  color: transparent !important;
  background-color: transparent !important;
}
</style>
