<template>
  <div style="height: 100%">
    <div class="linkTable">
      <div class="headBox">
        <div style="display: flex; align-items: center">
          <img src="../../assets/logo_44.png" alt="" />
          <div class="titleBox">
            <div class="titleText">另可云盘</div>
          </div>
        </div>
        <div style="display: flex; align-items: center">
          <t-button variant="outline" style="margin-right: 8px" theme="primary" @click="dowKyyApp">客户端下载</t-button>
          <div v-if="isToken" style="color: #366ef4" @click="logOutvisible1 = true">退出</div>
        </div>
      </div>
      <div class="bodyBox">
        <div v-if="resData.is_folder !== 1" class="fistBox">
          <div class="fileNameBox">
            <img :src="fileImage(resData.type)" alt="" />
            <div>
              <div class="fileNames">{{ resData.title }}</div>
              <div class="fileTimes">到期时间：{{ timestampToTime(resData.expired_at) }}</div>
            </div>
          </div>
        </div>
        <div v-if="resData.is_folder === 1" style="flex: 1" class="flex1overflow contentBox">
          <div class="mianbaoxie">
            <div class="flex-align">
              <!-- <span v-if="optionsBreadcrumb.length === 0" class="all-text">全部</span> -->
              <span v-if="optionsBreadcrumb.length !== 0" class="btn back" @click="
									changBreadcrumbItem(optionsBreadcrumb[optionsBreadcrumb.length - 2], optionsBreadcrumb.length - 2)
								">返回上一级</span>
              <div v-if="optionsBreadcrumb.length < 4" class="breadcrumb-box">
                <div v-for="(item, index) in optionsBreadcrumb" :key="item.id" class="click-btn color-black"
                  style="display: flex; align-items: center">
                  <img v-if="index !== 0" style="padding: 0 8px" src="../../assets/icon_arrowRight.svg" alt="" />
                  <t-tooltip :content="item.title">
                    <div class="title-ovfler" @click="changBreadcrumbItem(item, index)">
                      {{ item.title }}
                    </div>
                  </t-tooltip>
                </div>
              </div>
              <div v-if="optionsBreadcrumb.length >= 4" style="display: flex; align-items: center; padding-left: 12px">
                <div class="flex-align click-btn">
                  <span class="title-ovfler" @click="changBreadcrumbItem(optionsBreadcrumb[0], -1)"> 全部 </span>
                  <img style="padding: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
                </div>
                <div class="flex-align">
                  <span> ... </span>
                  <img style="padding: 0 8px" src="../../assets/svg/icon_arrowRight.svg" alt="" />
                </div>
                <div class="flex-align click-btn">
                  <t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 2].title">
                    <span class="title-ovfler" @click="
												changBreadcrumbItem(
													optionsBreadcrumb[optionsBreadcrumb.length - 2],
													optionsBreadcrumb.length - 2,
												)
											">
                      {{ optionsBreadcrumb[optionsBreadcrumb.length - 2].title }}
                    </span>
                  </t-tooltip>

                  <img v-if="optionsBreadcrumb.length - 2 > -1" style="padding: 0 8px"
                    src="../../assets/svg/icon_arrowRight.svg" alt="" />
                  <t-tooltip :content="optionsBreadcrumb[optionsBreadcrumb.length - 1].title">
                    <span class="title-ovfler" style="color: #13161b" @click="
												changBreadcrumbItem(
													optionsBreadcrumb[optionsBreadcrumb.length - 1],
													optionsBreadcrumb.length - 1,
												)
											">
                      {{ optionsBreadcrumb[optionsBreadcrumb.length - 1].title }}
                    </span>
                  </t-tooltip>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="sort-texts">
              <t-checkbox v-model="checkboxAllData"
                :indeterminate="selectedRowKeys.length > 0 && selectedRowKeys.length != data.length"
                @change="changeCheckboxAllData" />
              <span v-if="selectedRowKeys.length > 0"> 已选 {{ selectedRowKeys.length }} 项 </span>
              <span v-else> 共 {{ data.length }} 项 </span>
            </div>
          </div>
          <div class="tableBox">
            <!-- 取消表格 -->
            <t-checkbox-group v-model="selectedRowKeys" @change="columnChange">
              <!-- <t-checkbox :check-all="true" label="全选" /> -->

              <!-- <t-checkbox label="选项二" value="选项二" />
							<t-checkbox label="选项三" value="选项三" :disabled="true" /> -->
              <div v-for="row in data" :key="row.id">
                <div class="fistBox">
                  <div class="fileNameBox" @click="rowClick({ row: row }, false)">
                    <img :src="fileImage(row.type)" alt="" />
                    <div>
                      <div class="fileNames">{{ row.title }}</div>
                      <div class="fileTimes">更新时间:{{ timestampToTime(row.updatedAt) }}</div>
                    </div>
                  </div>
                  <t-checkbox :value="row.id"></t-checkbox>
                </div>
              </div>
            </t-checkbox-group>

            <!-- <t-table
							row-key="id"
							height="100%"
							class="clouddiskhome-talbe"
							:columns="columns"
							:data="data"
							:loading="isLoading"
							:selected-row-keys="selectedRowKeys"
							:select-on-row-click="false"
							@select-change="columnChange"
							@row-click="rowClick"
						>
							<template #title="{ row }">
								<div class="flex-align">
									<img style="width: 32px; height: 32px" :src="fileImage(row.type)" />
									<div v-if="row.tag" class="important-box">重要</div>
									<div class="tableFileName">
										<t-tooltip :content="row.title"> {{ row.title }} </t-tooltip>
									</div>
								</div>
							</template>
							<template #size="{ row }">
								<div>
									{{ KBTOMB(row.size) }}
								</div>
							</template>
							<template #empty>
								<div>
									<img
										style="width: 200px; height: 200px; display: block; margin: 49px auto 8px"
										src="@/assets/img/notdata.png"
									/>
									<div style="font-size: 14px; color: #13161b; text-align: center">暂无数据</div>
								</div>
							</template>

							<template #updatedAt="{ row }">
								<div style="display: flex; align-items: center">
									<div>{{ getTimes(new Date(row.updatedAt * 1000)) }}</div>
								</div>
							</template>
						</t-table> -->
          </div>
        </div>
        <div v-if="fileterFile() === '其他' && resData.is_folder !== 1" class="flex1overflow">
          <div class="noDataBox">
            <img src="../../assets/img/noteFile.png" alt="" />
            <div>该文件暂不支持预览，请下载后查看</div>
          </div>
        </div>
        <div v-if="fileterFile() === '文件'" ref="iframeWrapper" class="flex1overflow">
          <iframe ref="iframeRef" class="iframeImg" :src="viewFileData.file_url" :height="iframeHeight"
            style="position: fixed; text-align: center" @onload="setIframeHeight(this)" />
        </div>
        <div v-if="fileterFile() === '图片'" class="flex1overflow">
          <!-- <img style="margin: 0px auto; display: block" :src="viewFileData.file_url" alt="" /> -->
        </div>
        <div style="padding: 16px">
          <div class="advertisementBox">
            <div class="advertisementBoxTowText">
              <div class="advertisementBoxTitle">企业云盘惊喜上线！</div>
              <div class="advertisementBoxText">企业文件云保存，文件误删好找回 团队文件互分享，登录就送云存储</div>
            </div>
            <t-button variant="outline"
              style="height: 40px; width: 112px; color: var(--brand-kyy-color-brand-default, #4d5eff); font-weight: 600"
              @click="dowKyyApp">立即体验</t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录 -->
    <t-dialog v-model:visible="denglu" :cancel-btn="false" :close-btn="false" :header="true" :footer="false"
      width="440">
      <template #header>
        <div style="
						display: flex;
						align-items: center;
						width: 100%;
						justify-content: space-between;
						position: absolute;
						top: 42px;
						left: 380px;
					">
          <img style="width: 16px; cursor: pointer; height: 16px" src="@/assets/<EMAIL>"
            @click="denglu = false" />
        </div>
      </template>
      <div>
        <loginup v-if="store.loginFlag === 100" @closewin="closewin"></loginup>
        <registerup v-if="store.loginFlag === 200" @closewin="closewin"></registerup>
      </div>
    </t-dialog>

    <!-- 移动文件夹 -->
    <move-file ref="newMoveFile" :left-data="fistFileData" :tokens="tokens" :act-row="viewFileData"
      :title-flag="moveFileTitleFlag" :act-group="actGroup"
      @get-file-list-main-add-file-falg="getFileListMainAddFileFalg" @get-file-list="getFileList"
      @clear-selected-row-keys="clearSelectedRowKeys" @set-move-act-item="setMoveActItem"
      @get-move-left-list="getMoveLeftList"></move-file>
    <t-dialog v-model:visible="logOutvisible1" theme="info" header="退出登录" body="退出登录后,你将无法接收到通知" :close-btn="false"
      class="tishidengluclass login-out-btn" confirm-btn="退出登录" cancel-btn="取消" @confirm="onClickConfirm">
    </t-dialog>
    <div class="fistBtnBox">
      <t-button v-if="KBTOMB(resData.size) !== '--'" style="width: 100%; height: 44px" variant="outline" theme="primary"
        class="mr12" @click="dowFiles">下载({{ KBTOMB(resData.size) }})</t-button>

      <t-button v-if="KBTOMB(resData.size) === '--'" :disabled="selectedRowKeys.length === 0" variant="outline"
        style="width: 100%; height: 44px" theme="primary" @click="dowFiles">下载{{
        allOptionsBreadcrumb() === 0 || allOptionsBreadcrumb() === '--' ? '' : `(${allOptionsBreadcrumb()} ) `
        }}</t-button>
      <t-button theme="primary" style="width: 100%; height: 44px" @click="saveFile">保存到云盘</t-button>
    </div>

    <!-- 提示未登录 -->
    <t-dialog v-model:visible="tishidenglu" :cancel-btn="false" :close-btn="false" :header="true" :footer="false"
      class="tishidengluclass">
      <template #header>
        <div style="
						display: flex;
						align-items: center;
						width: 100%;
						justify-content: space-between;
						position: absolute;
						top: 42px;
						left: 380px;
					">
          <img style="width: 16px; cursor: pointer; height: 16px" src="@/assets/<EMAIL>"
            @click="tishidenglu = false" />
        </div>
      </template>
      <div>
        <img src="@/assets/img/<EMAIL>"
          style="width: 24px; cursor: pointer; height: 24px; position: absolute; top: -36px; right: 0"
          @click="tishidenglu = false" />
        <div class="fisttip">你还未登录</div>
        <div class="towtip">请登录另可云盘或者注册</div>
        <div class="btnbox">
          <div class="zhuce" @click="Gologin('login')">登录/注册</div>
        </div>
      </div>
    </t-dialog>
    <t-dialog v-model:visible="visible1" :cancel-btn="false" :close-btn="false" :header="true" :footer="false"
      class="tishidengluclass">
      <template #header>
        <div style="
						display: flex;
						align-items: center;
						width: 100%;
						justify-content: space-between;
						position: absolute;
						top: 42px;
						left: 380px;
					">
          <img style="width: 16px; cursor: pointer; height: 16px" src="@/assets/<EMAIL>"
            @click="tishidenglu = false" />
        </div>
      </template>
      <div>
        <img src="@/assets/<EMAIL>"
          style="width: 16px; cursor: pointer; height: 16px; position: absolute; top: -36px; right: 20px"
          @click="tishidenglu = false" />
        <div class="right-box">
          <div class="fisttip">提示</div>

          <div class="flie-max" style="width: 100%">
            <!-- <div class="value-text">文件夹名称</div> -->
            <div class="flex-align fileMaxBox">
              <!-- <img src="../../assets/svg/icon_staffFiles.svg" /> -->
              <img style="width: 48px; height: 48px" :src="fileImage(maxFileObj.type)" />
              <div class="maxFileName">
                {{ maxFileObj.title }}
              </div>
            </div>
            <div class="maxFileTip">
              你下载的{{ maxFileObj.isFolder === 0 ? '文件' : '文件夹' }}过大，请使用另可客户端
            </div>
          </div>
        </div>
        <div class="btnbox">
          <t-button style="width: 100%; height: 44px; font-size: 17px" @click="dowkyy"> 安装最新版客户端 </t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts" name="link">
  import { ref, onMounted, watchEffect, onUnmounted } from 'vue';
  import { MessagePlugin } from 'tdesign-vue-next';
  import { useRoute, useRouter } from 'vue-router';
  // import { saveAs } from 'file-saver';
  import { saveAs } from '@/utils/fileUtils';
  import loginup from '@/pages/login/login.vue';
  import registerup from '@/pages/login/register.vue';
  import kyyAvatar from '@/components/kyy-avatar/index.vue';
  import MoveFile from '@/components/MoveFile.vue';
  import { DiskFileList, fileShareFile, childPidToken } from '@/api/yunpanlink';
  import { fileImage } from '@/utils/myUtils';

  const router = useRouter();

  const route = useRoute();
  const iframeWrapper = ref(null);
  const iframeRef = ref(null);
  const maxFileObj = ref({});
  const iframeHeight = ref(0);
  const isToken = ref(null);
  // 计算当前父级元素的高度，当窗口大小改变时重新计算
  const onClickConfirm = () => {
    window.localStorage.removeItem('TokenData');
    window.localStorage.removeItem('refresh_token');
    window.localStorage.removeItem('main_token');
    window.localStorage.removeItem('openid');
    window.localStorage.removeItem('user');
    isToken.value = null;
    logOutvisible1.value = false;
    // store.userInfo = {};
    store.userInfo = { title: null, ID: null, avatar: null, telephone: null };
  };
  // 监听窗口高度变化
  watchEffect(() => {
    // 获取父级元素的高度
    if (iframeRef.value) {
      const parentHeight = window.getComputedStyle(iframeRef.value.parentElement).height;
      // 将 iframe 的高度设置为父元素的高度
      iframeRef.value.style.height = parentHeight;
    }
  });
  const moveFileTitleFlag = ref('选择移动目录');
  const actGroup = ref({
    id: 2,
  });
  const optionFlag = ref(false);
  const optionsBreadcrumb = ref([]);
  const documentOwnerArr = ref([]);
  const fistFileData = ref([]);
  const data = ref([]);
  const visible1 = ref(false);
  const logOutvisible1 = ref(false);
  const rowData = ref([]);
  const selectedRowKeys = ref([]);
  const isLoading = ref(false);
  const checkboxAllData = ref(false);
  const newMoveFile = ref(null);
  const tishidenglu = ref(false);
  const outLogin = () => {
    logOutvisible1.value = true;
  };

  const saveFile = () => {
    const main_token = window.localStorage.getItem('main_token');
    console.log(viewFileData.value, 'vviewFileDataviewFileData');
    store.loginFlag = 100;

    if (main_token) {
      // newMoveFile.value.openWin();
      router.push({
        path: '/MoveFile',
        query: {
          id: viewFileData.value.file_id,
          key: route.query.key,
          code: route.query.code,
          resData: JSON.stringify(resData.value),
        },
      });
    } else {
      tishidenglu.value = true;
    }
  };

  const clearSelectedRowKeys = () => {
    selectedRowKeys.value = [];
    value2.value = [];
  };

  const closewin = () => {
    const main_token = window.localStorage.getItem('main_token');

    if (main_token && window.localStorage.getItem('main_token') !== 'undefined') {
      isToken.value = true;
    } else {
      isToken.value = false;
    }

    denglu.value = false;
  };
  const getFileListMainAddFileFalg = () => {
    DiskFileList(actGroup.value.id).then((res) => {
      fistFileData.value = res.data.data.list;
    });
  };
  const getMoveLeftList = (id) => {
    DiskFileList(id).then((res) => {
      console.log(res, 'aaaaaaaaaaaaaa1111111');
      fistFileData.value = res.data.data.list;
    });
  };
  const setMoveActItem = (item) => {
    actGroup.value = item;
  };
  import { getUserStore } from '@/store';

  const store = getUserStore();

  const denglu = ref(false);

  onMounted(() => {
    if (route.query.resData) {
      isToken.value =
        window.localStorage.getItem('main_token') && window.localStorage.getItem('main_token') !== 'undefined';
      resData.value = JSON.parse(route.query.resData as any);
    } else {
      router.push({
        path: '/404',
      });
      return;
    }
    document.addEventListener('click', (e) => {
      if (e.target.dataset.id !== 'isclick') {
        optionFlag.value = false;
      }
    });
    // getFileListMainAddFileFalg();
    window.addEventListener('resize', handleResize);
    // if (tokens.value) {
    // getMoveLeftList(45);
    // }
    getFile();
  });
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });
  // 监听窗口高度变化
  watchEffect(() => {
    handleResize();
  });
  function handleResize() {
    if (iframeRef.value) {
      // 获取父级元素的高度
      const parentHeight = window.getComputedStyle(iframeWrapper.value).height;
      // 将 iframe 的高度设置为父级元素的高度
      iframeRef.value.style.height = parentHeight;
    }
  }
  const viewFileData = ref({});
  const resData = ref({});
  const tokens = ref(null);
  const getFile = () => {
    fileShareFile({
      key: route.query.key,
      code: route.query.code,
    }).then((res) => {
      if (res.status !== 200) {
        MessagePlugin.error(res.data.message);
      } else {
        console.log(res, 'resresresresresres');
        viewFileData.value = res.data.data;
        tokens.value = res.data.data.token;
        if (resData.value.is_folder === 1) {
          data.value = [];
          // 傻逼
          data.value.push({
            title: res.data.data.file_title,
            size: res.data.data.file_size,
            id: res.data.data.file_id,
            type: res.data.data.file_type,
            updatedAt: res.data.data.file_updated_at,
          });
        }
      }
    });
  };
  const getViewData = () => {
    childPidToken(viewFileData.value.file_id, viewFileData.value.token).then((ele) => {
      if (ele.status !== 200) {
        MessagePlugin.error(ele.data.data.message);

      } else {
        selectedRowKeys.value = [];

        // 11111111
        console.log(ele, 'eleeeeeeeeeeee');

        data.value = ele.data.data.list;
      }
      checkboxAllData.value = false;
    });
  };
  const dowkyy = () => {
    const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
    let url = 'https://img.kuaiyouyi.com/app/android/kyy_im_prod.exe';
    if (isMac) {
      url = 'https://img.kuaiyouyi.com/app/ios/kyy_im_prod.dmg';
    }
    window.open(url, '_blank');
  };
  const columnChange = (val) => {
    selectedRowKeys.value = val;
    value2.value = val;

    if (val.length === data.value.length) {
      checkboxAllData.value = true;
    } else {
      checkboxAllData.value = false;
    }
  };
  // const pros = ref(process.env.VITE_MANAGE_ENV);
  const pros = import.meta.env.VITE_MANAGE_ENV;

  const value2 = ref([]);
  const dowKyyApp = () => {
    // https://pre.ringkol.com/downloadCenter/

    console.log(pros, 'prosprospros');

    // const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
    let url = 'https://dev.ringkol.com/downloadCenter/';
    if (pros === 'PRE') {
      url = 'https://pre.ringkol.com/downloadCenter/';
    }
    if (pros === 'PROD') {
      url = 'https://ringkol.com/downloadCenter/';
    }
    window.open(url, '_blank');
  };
  const sortIndex = ref(null);
  // 下载文件夹
  const dowFiles = () => {
    if (resData.value.is_folder === 1) {
      const arr = selectedRowKeys.value.map((e) => data.value.find((ele) => e === ele.id));
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].type === 'folder' || arr[i].size > 10000) {
          maxFileObj.value = arr[i];
          visible1.value = true;
          break;
        } else {
          saveAs(arr[i].url, arr[i].title);
        }
      }
    } else {
      saveAs(viewFileData.value.file_url, viewFileData.value.file_title);
    }
  };
  const getFileList = async () => {
    value2.value = [];
    if (optionsBreadcrumb.value.length === 0) {
      // let DiskListRes = null;
      // if (!cloudDiskType.name) {
      // 	const res = await DiskList(); // 右上角菜单选项后端给接口
      // 	DiskListRes = res.data.data.list[0].id;
      // } else {
      // 	DiskListRes = cloudDiskType.id;
      // }
      // 首层
      getViewData().then((res) => {
        data.value = res.data.data.list;
        value2.value = [];
      });
    } else {
      rowClick(
        {
          row: {
            type: 'folder',
            permission: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].permission,
            title: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].title,
            id: optionsBreadcrumb.value[optionsBreadcrumb.value.length - 1].id,
          },
        },
        true,
      );
    }
  };
  const rowClick = (row, flag) => {
    rowData.value = row.row;
    if (row.row.type === 'folder') {
      value2.value = [];

      selectedRowKeys.value = [];
      isLoading.value = true;
      if (!flag) {
        optionsBreadcrumb.value.push(row.row);
      }

      childPidToken(row.row.id, tokens.value).then((res) => {
        if (res.status === 200) {
          if (sortIndex.value === 1) {
            data.value = JSON.parse(JSON.stringify((res.data.data.list, 'size', true)));
          } else if (sortIndex.value === 2) {
            data.value = JSON.parse(JSON.stringify((res.data.data.list, 'size', false)));
          } else if (sortIndex.value === 3) {
            data.value = JSON.parse(JSON.stringify((res.data.data.list, 'createdAt', false)));
          } else if (sortIndex.value === 4) {
            data.value = JSON.parse(JSON.stringify((res.data.data.list, 'createdAt', true)));
          } else {
            data.value = res.data.data.list;
          }
        } else {
          MessagePlugin.error(res.data.message);

        }

        isLoading.value = false;
      });
      checkboxAllData.value = false;
    }
  };

  const allOptionsBreadcrumb = () => {
    // let arr=[]
    const arr = selectedRowKeys.value.map((e) => data.value.find((ele) => e === ele.id).size);
    if (arr.length > 0) {
      const sum = arr.reduce((accumulator, currentValue) => accumulator + currentValue);
      console.log(sum, 'summmmmmmmmmmmm');
      return KBTOMB(sum);
    }
    return 0;
  };
  const KBTOMB = (bytes) => {
    if (bytes === 0) return '--';
    const k = 1000; // or 1024
    const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    if (!sizes[i]) {
      return `${(bytes / k ** i).toPrecision(3)}B`;
    }
    return `${(bytes / k ** i).toPrecision(3)} ${sizes[i]}`;
  };
  const timestampToTime = (timestamp) => {
    // 时间戳为10位需*1000，时间戳为13位不需乘1000
    const date = new Date(timestamp * 1000);
    const Y = `${date.getFullYear()}-`;
    const M = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
    const D = `${date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()} `;
    const h = `${date.getHours()}:`;
    const m = `${date.getMinutes()}:`;
    const s = date.getSeconds();
    return Y + M + D + h + m + s;
  };
  const Gologin = (flag) => {
    tishidenglu.value = false;
    console.log(route, 'routeroute');
    if (flag === '注册') {
      router.push({
        path: '/registerMo',
        query: {
          key: route.query.key,
          code: route.query.code,
          resData: JSON.stringify(resData.value),
        },
      });
    } else {
      router.push({
        path: '/loginMo',
        query: {
          key: route.query.key,
          code: route.query.code,
          resData: JSON.stringify(resData.value),
        },
      });
    }
    // router.push('go')
  };
  const changeCheckboxAllData = (e) => {
    checkboxAllData.value = e;
    if (e) {
      const arr = [];
      data.value.forEach((item) => {
        arr.push(item.id);
      });
      value2.value = arr;

      selectedRowKeys.value = arr;
    } else {
      value2.value = [];
      selectedRowKeys.value = [];
    }
  };
  const changBreadcrumbItem = (item, index) => {
    if (index < 0) {
      optionsBreadcrumb.value = [];
      documentOwnerArr.value = [];

      // getFileList();
      getFile();
    } else if (index !== optionsBreadcrumb.value.length - 1) {
      optionsBreadcrumb.value.splice(index + 1);
      rowClick(
        {
          row: item,
        },
        true,
      );
    }
  };
  const fileterFile = () => {
    console.log(resData, 'resDataresDataresData');

    const validTypes = ['file', 'pdf', 'ppt', 'pptts', 'excel', 'text'];
    const arr = ['jpg', 'png', 'image', 'gif', 'svg'];
    if (arr.includes(resData.value.type)) {
      return '图片';
    }
    if (validTypes.includes(resData.value.type)) {
      return '文件';
    }
    return '其他';
  };
</script>

<style lang="less" scoped>
  .noDataBox {
    text-align: center;
    margin-top: 132px;

    img {
      height: 160px;
      margin-bottom: 10px;
    }

    div {
      height: 22px;
      font-size: 14px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      text-align: center;
      color: #13161b;
      line-height: 22px;
    }
  }

  .qcode {
    width: 200px;
    height: 200px;
    border-radius: 8px;
    margin: -4px -8px;
    box-shadow: 0px 8px 24px 0px rgba(19, 22, 27, 0.16);
    padding: 16px 40px 8px;

    img {
      width: 100%;
    }

    .qcodeText {
      width: 120px;
      height: 44px;
      font-size: 14px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      text-align: center;
      color: #717376;
      line-height: 22px;
    }
  }

  .footText {
    height: 22px;
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: var(--text-kyy-color-text-2, #516082);

    line-height: 22px;
    position: fixed;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
  }

  .back {
    width: 100px;
    font-size: 17px;
    font-family: Microsoft YaHei, Microsoft YaHei-Regular;
    font-weight: 400;
    text-align: left;
    color: #2069e3;
    position: relative;
    padding-right: 12px;
  }

  .back::after {
    content: '';
    width: 1px;
    height: 12px;
    position: absolute;
    background: #e3e6eb;
    top: 5px;
    right: 0;
  }

  .all-text {
    font-size: 14px;
    font-family: Microsoft YaHei, Microsoft YaHei-Bold;
    font-weight: 700;
    text-align: left;
    color: #13161b;
    line-height: 22px;
  }

  .breadcrumb-box {
    display: flex;
    align-items: center;
    margin-left: 12px;
  }

  .title-ovfler {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tableFileName {
    margin-left: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .hy {
    margin-left: 16px;
    cursor: pointer;
  }

  .btnHead {
    display: flex;
    align-items: center;
  }

  .name-option-box-lang {
    background: #ffffff;
    border-radius: 6px;
    z-index: 9999;
    position: absolute;

    box-shadow: 0px 8px 24px 0px rgba(19, 22, 27, 0.16);

    // display: none;
    .option-box {
      padding: 8px;

      .option-item:hover {
        background: #f0f8ff;
      }

      .option-item {
        cursor: pointer;
        width: 200px;
        height: 32px;
        padding: 8px 5px;
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        margin-bottom: 4px;
        align-items: center;
        justify-content: space-between;

        span {
          height: 22px;
          font-size: 14px;
          font-family: Microsoft YaHei, Microsoft YaHei-Regular;
          font-weight: 400;
          color: #13161b;
          line-height: 22px;
        }
      }
    }

    .head-box {
      display: flex;
      align-items: flex-start;
      padding: 16px 0 22px 16px;
      border-bottom: 1px solid #e3e6eb;

      img {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        margin-right: 12px;
      }

      span {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei-Bold;
        font-weight: 700;
        color: #13161b;
      }
    }
  }

  .name-option-box {
    width: 216px;
    height: 115px;
    background: #ffffff;
    border-radius: 6px;
    z-index: 9999;
    position: absolute;
    right: -200px;
    top: 30px;
    box-shadow: 0px 8px 24px 0px rgba(19, 22, 27, 0.16);

    .out-login {
      height: 22px;
      font-size: 14px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      color: #da2d19;
      padding: 0 16px;
      line-height: 33px;
      cursor: pointer;
    }

    .option-box {
      padding: 8px;
      border-bottom: 1px solid #e3e6eb;

      .option-item:hover {
        background: #f0f8ff;
      }

      .option-item {
        cursor: pointer;
        width: 200px;
        height: 32px;
        padding: 8px 5px;
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        margin-bottom: 4px;
        align-items: center;
        justify-content: space-between;

        span {
          height: 22px;
          font-size: 14px;
          font-family: Microsoft YaHei, Microsoft YaHei-Regular;
          font-weight: 400;
          color: #13161b;
          line-height: 22px;
        }
      }
    }

    .head-box {
      display: flex;
      align-items: flex-start;
      padding: 16px 0 22px 16px;
      border-bottom: 1px solid #e3e6eb;

      img {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        margin-right: 12px;
      }

      span {
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei-Bold;
        font-weight: 700;
        color: #13161b;
      }
    }
  }

  .table-view-r-item-file-img {
    text-align: center;
    margin-top: -20px;

    img {
      width: 72px;
      height: 72px;
    }

    .textovf {
      width: 132px;
      font-size: 14px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-weight: 400;
      text-align: center;
      margin: 0 auto;
      color: #13161b;
      line-height: 22px;
      // white-space: nowrap; // 强制一行显示
      // overflow: hidden; // 超出隐藏
      // text-overflow: ellipsis; // 省略号
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      /* 超出几行省略 */
      overflow: hidden;
    }
  }

  .advertisementBox {
    width: 100%;
    background-image: url(../../assets/img/<EMAIL>);
    background-repeat: no-repeat;
    background-position: 100%;
    background-size: 100%;
    height: 106px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    // bg_pic_foot.svg
  }

  .color-black:last-child {
    color: #13161b;
  }

  .flie-max {
    img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
  }

  .btn {
    cursor: pointer;
    color: #2069e3;
  }

  .click-btn {
    cursor: pointer;
  }

  .breadcrumb-box {
    display: flex;
    align-items: center;
    margin-left: 12px;
  }

  .sort-texts {
    // height: 56px;
    // line-height: 56px;
    // font-size: 12px;
    // display: flex;
    // align-items: center;
    // color: #717376;
    // background: #fff;

    height: 26px;
    font-size: 17px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    text-align: left;
    color: #13161b;
    line-height: 26px;
  }

  .fileMaxBox {
    margin-top: 8px;
    margin-bottom: 16px;

    img {
      width: 32px;
      height: 32px;
    }

    .maxFileName {
      font-size: 17px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      color: #13161b;
      line-height: 26px;
      width: 200px;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      text-align: left;
      color: #13161b;
      line-height: 22px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }

  .flex-align {
    display: flex;
    align-items: center;
  }

  .iframeImg {
    img {
      text-align: center;
    }
  }

  .contentBox {
    // margin: 32px 116px 0;
    padding: 16px;
  }

  .towtip {
    height: 22px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: center;
    color: #717376;
    line-height: 22px;
    margin-top: 12px;
  }

  .fisttip {
    height: 26px;
    font-size: 17px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    text-align: center;
    color: #13161b;
    line-height: 26px;
  }

  .advertisementBoxTitle {
    height: 26px;
    font-size: 17px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    color: #ffffff;
    line-height: 26px;
    margin-bottom: 8px;
  }

  .advertisementBoxText {
    height: 40px;
    width: 12rem;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .flex1overflow {
    flex: 1;
    overflow: auto;
  }

  .fistBtnBox {
    display: flex;
    height: 68px;
    padding: 12px 16px;
    align-items: center;
    gap: 8px;
  }

  .linkTable {
    background: #f1f2f5;
    height: calc(100vh - 70px);

    .headBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f1f2f5;
      padding: 17px 8px 17px 17px;

      img {
        width: 48px;
        height: 48px;
      }

      .titleText {
        width: 80px;
        height: 28px;
        font-size: 20px;
        font-family: PingFang SC, PingFang SC-Semibold;
        font-weight: 600;
        color: #13161b;
        line-height: 28px;
      }

      .titleBox {
        margin-left: 8px;
      }

      .titleSlogan {
        width: 112px;
        height: 22px;
        font-size: 14px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: left;
        color: #717376;
        line-height: 22px;
      }
    }

    .menu-icons {
      width: 16px;
      height: 16px;
      font-size: 16px;
      margin-right: 4px;
    }

    .bodyBox {
      display: flex;
      flex-direction: column;
      height: calc(100% - 96px);
      background: #fff;

      .fileNameBox {
        display: flex;
        align-items: center;

        img {
          width: 48px;
          height: 48px;
          margin-right: 12px;
        }

        .fileNames {
          width: 247px;
          height: 26px;
          font-size: 17px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          color: #13161b;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowra;
          line-height: 26px;
        }

        .fileTimes {
          height: 22px;
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          text-align: left;
          color: #a1a2a4;
          line-height: 22px;
        }
      }

      .fistBox {
        border-bottom: 1px solid #e3e6eb;
        height: 78px;
        margin-left: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    .head1200box {
      height: 80px;
      display: flex;
      margin: 0 auto;
      align-items: center;
      justify-content: space-between;

      .passwordBoxHeadBox {
        display: flex;
        align-items: center;

        img {
          width: 40px;
          height: 40px;
        }

        .passwordBoxtitleText {
          font-size: 16px;
          font-family: Microsoft YaHei, Microsoft YaHei-Bold;
          font-weight: 700;
          text-align: left;
          color: #13161b;
        }

        .passwordBoxtitleBox {
          margin-left: 20px;
        }

        .passwordBoxtitleSlogan {
          height: 22px;
          font-size: 14px;
          font-family: Microsoft YaHei, Microsoft YaHei-Regular;
          font-weight: 400;
          text-align: left;
          color: #717376;
          line-height: 22px;
        }
      }
    }
  }

  .ml12 {
    margin-left: 12px;
  }

  .mianbaoxie {
    margin-top: 4px;
    margin-bottom: 12px;
  }

  .btnbox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 32px;

    div {
      height: 44px;
      border: 1px solid #e3e6eb;
      border-radius: 6px;
      width: 100%;
      font-size: 17px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: center;
      color: #13161b;
      line-height: 44px;
    }

    .lgonbtn {
      background: #ffffff;
      color: #13161b;
    }

    .zhuce {
      background: #4d5eff;
      color: #fff;
    }
  }

  .mr12 {
    margin-right: 12px;
  }

  :deep(.tishidengluclass) {
    .t-dialog__wrap {
      .t-dialog__position {
        .t-dialog {
          margin: 0 28px !important;
          padding: 32px 24px 20px;
        }
      }
    }
  }

  .maxFileTip {
    height: 22px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #717376;
    line-height: 22px;
  }

  // .tishidengluclass {
  // }
</style>
