<script setup lang="ts">
/**
 * 文本省略组件
 *
 * 功能特性：
 * - 支持单行/多行文本省略显示
 * - 支持展开/收起功能
 * - 支持省略位置选择（开始/中间/结尾）
 * - 自适应窗口大小变化重新计算
 * - 动态计算最佳省略位置
 */

import {
  ref,
  watch,
  computed,
  onActivated,
  onMounted,
  nextTick,
  type PropType,
  useSlots,
} from 'vue';
import { windowWidth } from './utils';

const props = defineProps({
  /** 展示的行数 */
  rows: {
    type: Number as PropType<number>,
    default: 1
  },
  /** 省略符号 */
  dots: {
    type: String as PropType<string>,
    default: '...'
  },
  /** 需要展示的文本内容 */
  content: {
    type: String as PropType<string>,
    default: ''
  },
  /** 展开操作的文案 */
  expandText: {
    type: String as PropType<string>,
    default: ''
  },
  /** 收起操作的文案 */
  collapseText: {
    type: String as PropType<string>,
    default: ''
  },
  /** 省略位置 */
  position: {
    type: String as PropType<'start' | 'middle' | 'end'>,
    default: 'end'
  }
});

const emit = defineEmits<{
  'click-action': [event: MouseEvent];
}>();

const slots = useSlots();

// 响应式状态
const text = ref(props.content); // 当前展示的文本（可能是省略后的）
const expanded = ref(false); // 是否已展开
const hasAction = ref(false); // 是否需要显示展开/收起按钮
const root = ref<HTMLElement>(); // 组件根元素引用
const actionRef = ref<HTMLElement>(); // 操作按钮元素引用
let needRecalculate = false; // 标记是否需要重新计算（用于keep-alive场景）

// 计算当前操作按钮的文案
const actionText = computed(() =>
  expanded.value ? props.collapseText : props.expandText,
);

/**
 * 将px值转换为数字
 * @param value - CSS样式值（如 "16px"）
 * @returns 数字值
 */
const pxToNum = (value: string | null) => {
  if (!value) return 0;
  const match = value.match(/^\d*(\.\d*)?/);
  return match ? Number(match[0]) : 0;
};

/**
 * 克隆容器元素用于计算
 * 创建一个与原始元素样式相同但位置固定在屏幕外的临时元素
 * 用于在不影响页面布局的情况下进行文本高度计算
 * @returns 克隆的容器元素
 */
const cloneContainer = () => {
  if (!root.value || !root.value.isConnected) return;

  const originStyle = window.getComputedStyle(root.value);
  const container = document.createElement('div');
  const styleNames: string[] = Array.prototype.slice.apply(originStyle);

  // 复制所有样式到临时容器
  styleNames.forEach((name) => {
    container.style.setProperty(name, originStyle.getPropertyValue(name));
  });

  // 设置临时容器的位置，使其不影响页面布局
  container.style.position = 'fixed';
  container.style.zIndex = '-9999';
  container.style.top = '-9999px';
  container.style.height = 'auto';
  container.style.minHeight = 'auto';
  container.style.maxHeight = 'auto';

  container.innerText = props.content;
  document.body.appendChild(container);

  return container;
};

/**
 * 计算省略后的文本内容
 * 使用二分查找算法找到最佳的省略位置
 * @param container - 用于计算的临时容器元素
 * @param maxHeight - 允许的最大高度
 * @returns 省略后的文本
 */
const calcEllipsisText = (container: HTMLDivElement, maxHeight: number) => {
  const { content, position, dots } = props;
  const end = content.length;
  const middle = (0 + end) >> 1;
  const actionHTML = actionRef.value?.outerHTML ?? props.expandText;

  /**
   * 计算开始或结尾省略的文本
   * 使用二分查找算法递归寻找最佳省略位置
   */
  const calcEllipse = () => {
    // 二分查找最佳省略位置
    const tail = (left: number, right: number): string => {
      if (right - left <= 1) {
        if (position === 'end') {
          return content.slice(0, left) + dots;
        }
        return dots + content.slice(right, end);
      }

      const middle = Math.round((left + right) / 2);

      // 根据省略位置设置截取内容
      if (position === 'end') {
        container.innerText = content.slice(0, middle) + dots;
      } else {
        container.innerText = dots + content.slice(middle, end);
      }

      container.innerHTML += actionHTML;

      // 如果当前高度仍然超出限制，继续缩短文本
      if (container.offsetHeight > maxHeight) {
        if (position === 'end') {
          return tail(left, middle);
        }
        return tail(middle, right);
      }

      // 如果当前高度符合要求，尝试显示更多文本
      if (position === 'end') {
        return tail(middle, right);
      }

      return tail(left, middle);
    };

    return tail(0, end);
  };

  /**
   * 计算中间省略的文本
   * 同时调整左右两部分的长度
   * @param leftPart - 左侧部分的范围 [开始位置, 结束位置]
   * @param rightPart - 右侧部分的范围 [开始位置, 结束位置]
   */
  const middleTail = (
    leftPart: [number, number],
    rightPart: [number, number],
  ): string => {
    if (
      leftPart[1] - leftPart[0] <= 1 &&
      rightPart[1] - rightPart[0] <= 1
    ) {
      return (
        content.slice(0, leftPart[0]) +
        dots +
        content.slice(rightPart[1], end)
      );
    }

    const leftMiddle = Math.floor((leftPart[0] + leftPart[1]) / 2);
    const rightMiddle = Math.ceil((rightPart[0] + rightPart[1]) / 2);

    container.innerText =
      content.slice(0, leftMiddle) +
      dots +
      content.slice(rightMiddle, end);
    container.innerHTML += actionHTML;

    // 如果高度超出限制，继续缩短两侧文本
    if (container.offsetHeight >= maxHeight) {
      return middleTail(
        [leftPart[0], leftMiddle],
        [rightMiddle, rightPart[1]],
      );
    }

    // 如果高度符合要求，尝试显示更多文本
    return middleTail(
      [leftMiddle, leftPart[1]],
      [rightPart[0], rightMiddle],
    );
  };

  return position === 'middle'
    ? middleTail([0, middle], [middle, end])
    : calcEllipse();
};

/**
 * 计算是否需要省略以及省略后的文本
 * 主要逻辑：
 * 1. 创建临时容器
 * 2. 计算允许的最大高度
 * 3. 判断是否需要省略
 * 4. 如需省略则计算最佳省略文本
 */
const calcEllipsised = () => {
  // 创建用于计算的临时容器
  const container = cloneContainer();

  if (!container) {
    needRecalculate = true;
    return;
  }

  const { paddingBottom, paddingTop, lineHeight } = container.style;
  // 计算允许的最大高度（行数 * 行高 + 内边距）
  const maxHeight = Math.ceil(
    (Number(props.rows) + 0.5) * pxToNum(lineHeight) +
      pxToNum(paddingTop) +
      pxToNum(paddingBottom),
  );

  // 判断是否需要省略
  if (maxHeight < container.offsetHeight) {
    hasAction.value = true;
    text.value = calcEllipsisText(container, maxHeight);
  } else {
    hasAction.value = false;
    text.value = props.content;
  }

  // 清理临时容器
  document.body.removeChild(container);
};

/**
 * 切换展开/收起状态
 * @param isExpanded - 指定展开状态，默认为切换当前状态
 */
const toggle = (isExpanded = !expanded.value) => {
  expanded.value = isExpanded;
};

/**
 * 点击操作按钮的处理函数
 * @param event - 鼠标事件
 */
const onClickAction = (event: MouseEvent) => {
  toggle();
  emit('click-action', event);
};

onMounted(() => {
  calcEllipsised();

  if (slots.action) {
    nextTick(calcEllipsised);
  }
});

onActivated(() => {
  if (needRecalculate) {
    needRecalculate = false;
    calcEllipsised();
  }
});

// 监听窗口大小变化和相关属性变化，重新计算省略
watch(
  [windowWidth, () => [props.content, props.rows, props.position]],
  calcEllipsised,
);

defineExpose({
  /**
   * @public
   */
  toggle
});
</script>

<template>
  <div
    ref="root"
    class="rk-text-ellipsis"
  >
    {{ expanded ? props.content : text }}<!--
    --><span
      v-if="hasAction"
      ref="actionRef"
      class="rk-text-ellipsis__action"
      @click="onClickAction"
    >
      {{ slots.action ? slots.action({ expanded }) : actionText }}
    </span>
  </div>
</template>

<style scoped lang="less">
.rk-text-ellipsis {
  line-height: 22px;
  white-space: pre-wrap;
  overflow-wrap: break-word;

  &__action {
    cursor: pointer;
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);

    &:hover {
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_hover, #707EFF);
    }

    &:active {
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_active, #3E4CD1);
    }
  }
}
</style>
