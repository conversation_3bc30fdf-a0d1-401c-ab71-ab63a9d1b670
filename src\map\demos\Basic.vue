/**
 * title: 百度地图卡片
 * description: 显示地图卡片
 */

<template>
  <div class="map-demo">
    <div class="demo-section">
      <h4>1. 基本地图卡片</h4>
      <RkBaiduMapCard
        :loc="mapLocation"
        :can-update="true"
        :removable="true"
        @confirm="handleMapConfirm"
        @remove="handleMapRemove"
      />
    </div>

    <div class="demo-section">
      <h4>2. 小尺寸地图卡片</h4>
      <RkBaiduMapCard
        :loc="mapLocation"
        :mini="true"
        :can-update="true"
        @confirm="handleMapConfirm"
      />
    </div>

    <div class="demo-section">
      <h4>3. 大尺寸地图卡片</h4>
      <RkBaiduMapCard
        :loc="mapLocation"
        :large="true"
        :can-update="true"
        @confirm="handleMapConfirm"
      />
    </div>

    <div
      class="demo-section"
      style="height: 420px"
    >
      <h4>4. 自定义高度地图卡片</h4>
      <RkBaiduMapCard
        :loc="mapLocation"
        :height="300"
        :can-update="true"
        @confirm="handleMapConfirm"
      />
    </div>

    <div class="demo-section">
      <h4>5. 只读模式地图卡片</h4>
      <RkBaiduMapCard
        :loc="mapLocation"
        :can-update="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import RkBaiduMapCard from '../BaiduMapCard.vue';
import type { Location, MapConfirmData } from '../types';

// 地图位置数据
const mapLocation = ref<Location>({
  name: '珠海市政府',
  address: '广东省珠海市香洲区人民东路2号',
  latLng: {
    latitude: 22.265842,
    longitude: 113.543005,
  },
});

// 地图选择确认回调
const handleMapConfirm = (data: MapConfirmData) => {
  console.log('地图选择确认:', data);

  // 更新位置数据
  mapLocation.value = {
    name: data.name,
    address: data.address || data.name,
    latLng: {
      latitude: data.location.lat,
      longitude: data.location.lng,
    },
  };
};

// 移除地图卡片
const handleMapRemove = () => {
  console.log('移除地图卡片');
  mapLocation.value = {};
};
</script>

<style scoped>
.map-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  height: 320px
}

.demo-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}
</style>
