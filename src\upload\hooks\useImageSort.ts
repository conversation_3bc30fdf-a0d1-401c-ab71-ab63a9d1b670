import {
  ComputedRef,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  unref,
  watch,
} from 'vue';
import { Sortable, useSortable } from '../../../composables/useSortable';

type ImageProps = {
  sortable?: boolean;
  containerClass?: string;
};

/**
 * 图片排序
 * @param props 图片排序的配置
 * @param sortHandler 排序处理函数
 */
export function useImageSort(
  props: ImageProps | ComputedRef<ImageProps>,
  sortHandler: (oldIndex: number, newIndex: number) => void,
) {
  const sortableInstance = ref<null | Sortable>(null);

  async function initImageSortable() {
    await nextTick();

    const propsValue = unref(props);
    if (!propsValue.sortable) return;

    const el = document.querySelectorAll(
      `.${propsValue.containerClass}`,
    )?.[0] as HTMLElement;
    if (!el) {
      console.warn(
        'Element not found for sortable initialization:',
        propsValue.containerClass,
      );
      return;
    }

    // 重置元素状态
    const resetElState = async () => {
      el.style.cursor = 'default';
      el.querySelector('.draggable')?.classList.remove('dragging');
    };

    const { initializeSortable } = useSortable(el, {
      filter: (_evt, _target: HTMLElement) => !propsValue.sortable,
      onStart: () => {
        el.style.cursor = 'grabbing';
        el.querySelector('.draggable')?.classList.add('dragging');
      },
      onEnd(evt) {
        const { newIndex, oldIndex } = evt;

        if (
          oldIndex !== undefined &&
          newIndex !== undefined &&
          !Number.isNaN(oldIndex) &&
          !Number.isNaN(newIndex) &&
          oldIndex !== newIndex
        ) {
          sortHandler(oldIndex, newIndex);
        }

        resetElState();
      },
    });

    sortableInstance.value = await initializeSortable();
  }

  async function init() {
    await nextTick();
    initImageSortable();
  }

  // 监听 props 变化，重新初始化排序
  watch(
    () => unref(props),
    () => {
      sortableInstance.value?.destroy();
      init();
    },
    { deep: true },
  );

  onMounted(init);

  onUnmounted(() => {
    sortableInstance.value?.destroy();
  });
}
