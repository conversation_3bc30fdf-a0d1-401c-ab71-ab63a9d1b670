import '@renderer/_jssdk';
import { createApp } from 'vue';
import './utils/umen';
import { createPinia } from 'pinia';
import { createPersistedState } from 'pinia-plugin-persistedstate';
import TDesign from 'tdesign-vue-next';
import 'tdesign-vue-next/es/style/index.css';
import 'vue3-tree-org/lib/vue3-tree-org.css';
import './permission';
import { BAIDU_AK } from '@renderer/constants/common';
import { Boot } from '@wangeditor/editor';
import mentionModule from '@wangeditor/plugin-mention';
import { lkLoading } from '@renderer/directives/loading/index';
import clickOutside from '@renderer/directives/click-outside/index';
import replaceSvg from '@renderer/directives/replace-svg/index';
import vue3TreeOrg from 'vue3-tree-org';
import Vue3BaiduMapGL from 'vue3-baidu-map-gl';
import { VueQueryPlugin } from '@tanstack/vue-query';
import { checkNewVersion, configInfo } from '@renderer/views/setting/util';
import { useAppStore } from '@renderer/store/modules/appStore';
import errImage from '@renderer/assets/business/Rectangle.png';
import loadingImage from '@renderer/assets/loading.png';
import infiniteScroll from 'vue3-infinite-scroll-better';
import lazyPlugin from 'vue3-lazy';
import LkEditor from '@rk/editor';
import { Tricks, RTable } from '@rk/unitPark';
import { compareVersionAndNumber } from '@/utils/auth';
import router from './router';
// import { errorHandler, logHandler } from "./log";
import GlobalErrorHandler from './plugins/errorHandlers';
import { i18n, setLanguage } from './i18n';
import filters from './utils/filters';
// import { lkLoading } from "../renderer/directives/loading/index";
import TitleBar from './components/common/TitleBar.vue';
import AccountTitleBar from './components/account/TitleBar.vue';
// import * as fundebug from "fundebug-javascript";
// import FundebugVue from "fundebug-vue";
import 'wc-waterfall';
import '@rk/editor/index.css';
import App from './App.vue';
import 'virtual:svg-icons-register';
import './style/index.less';
import '@rk/unitPark/dist/assets/style.css';
import 'virtual:uno.css';

// 注册。要在创建编辑器之前注册，且只能注册一次，不可重复注册。
Boot.registerModule(mentionModule);

// fundebug.init({ apikey: "3bdf893f1bce3833ffad055cc785269f9992af6ae7802d2b89c875cb523d4969" });

// 初始化vue-amap
// initAMapApiLoader({
//   // 高德的key
//   key: 'cb3c186ecac3d238434fd23edbf20c99',
//   securityJsCode: '6b03f991508723cf9f6cb731263daf9d', // 新版key需要配合安全密钥使用
//   // Loca:{
//   //  version: '2.0.0'
//   // } // 如果需要使用loca组件库，需要加载Loca
// });

const app = createApp(App);
// 将 Vue 实例挂载到全局
(window as any).__RINGKOL_VUE_APP__ = app;
// app.use(new FundebugVue(fundebug))
app.use(Vue3BaiduMapGL, {
  // ak: __APP_ENV__.VITE_MAP_KEY,
  apiUrl: `https://api.map.baidu.com/api?type=webgl&v=1.0&ak=${BAIDU_AK}&callback=_initBMap_`,
});

// app.mixin({
//   mounted() {
//     const eleArr = document.querySelectorAll(".t-input__suffix-icon");
//     eleArr.forEach((iconElement) => {
//       const svgElement = iconElement.querySelector("svg");
//       if (svgElement) {
//         const str = svgElement.getAttribute("class");
//         if (str.includes("t-fake-arrow") && !iconElement.classList.contains("exclude-downfill")) {
//           const newImgElement = document.createElement("img");
//           newImgElement.src = downfill;
//           iconElement.innerHTML = "";
//           iconElement.appendChild(newImgElement);
//         }
//       }
//     });
//   },
// });
const store = createPinia();
// 固化信息
store.use(createPersistedState());
app.use(VueQueryPlugin);


app.use(TDesign);
app.use(router);
app.use(store);
app.use(infiniteScroll);
app.use(GlobalErrorHandler);
app.use(vue3TreeOrg);
app.use(LkEditor);
app.directive('lkloading', lkLoading);
app.directive('click-outside', clickOutside);
app.directive('replace-svg', replaceSvg);

app.use(lazyPlugin, {
  loading: loadingImage, // 图片加载时默认图片
  error: errImage, // 图片加载失败时默认图片
});
// app.use(VueAMap);

app.use(i18n);
// errorHandler(app);
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';
setLanguage(window.localStorage.getItem('lang') || 'zh-cn');
// 全局引入 TitleBar 组件
app.component('TitleBar', TitleBar);
app.component('AccountTitleBar', AccountTitleBar);
app.component('Tricks', Tricks);
app.component('RTable', RTable);
filters(app);
app.mount('#app');

// logHandler(
//   {
//     info: 'test22',
//     name: 'a2',
//     desc: 'c',
//     mod: '12'
//   }
// );

// 检查版本，如果当前版本大于等于本地数据版本则清空本地数据
const checkVersion = () => {
  checkNewVersion().then((res) => {
    if (res.status === 200) {
      const data = res.data?.data;
      const newVersion = data?.external_version || '';
      const appStore = useAppStore();
      const newPackConfig:any = {};
      if (data?.package_info_two) {
        const config = data?.package_info_two.find((v) => v && v.type === 'config');
        newPackConfig.buildNumber = config?.buildNumber;
        newPackConfig.version = newVersion;
      }
      const version = {
        version: configInfo.version,
        number: configInfo.buildNumber,
      };
      console.log('main checkVersion3', appStore.updatePack);
      // 没有新版本清空
      if (!compareVersionAndNumber(newPackConfig, version) && appStore.updatePack && appStore.updatePack.show) {
        console.log('main checkVersion4');
        appStore.setUpdateAll('updatePack', {});
      }
    }
  });
};
checkVersion();
