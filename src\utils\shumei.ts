export const initSM = () => {
	window._smReadyFuncs = [];
	window.SMSdk = {
		onBoxDataReady(boxData) {
			// 非必填
			console.log('此时拿到的数据为boxData或者boxId', boxData);
		},
		ready(fn) {
			fn && _smReadyFuncs.push(fn);
		},
	};
	window.SMSdkError = false;
	// 1. 通用配置项
	window._smConf = {
		organization: 'OioUFNPPerhbBNzpgJoi', // 必填，组织标识，邮件中organization项
		appId: 'default', // 必填，应用标识，默认传值default，其他应用标识提前联系数美协助定义
		publicKey:
			'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCbkXhIXYNLFtBl91+pKZIaiElM0dxi3LZWsal13AtvwLGD2HI3maECRIHQvk8ehaRgbnq3KRDTGKbU2Nuw8jsQDTsb/bHu16XEYJX0MVxQo0R+rLRlWAwKT6laC1K4XbznFlYGTHghBuU+SK2MOrvMQb7HoYsRfTIgsHej8C6grwIDAQAB', // 必填，私钥标识，邮件中publicKey项
		staticHost: 'static.portal101.cn', // 必填, 设置JS-SDK文件域名，建议填写static.portal101.cn
		protocol: 'https', // 如果使用https，则设置，如不使用，则不设置这个字段

		// 2. 连接海外机房特殊配置项，仅供设备数据上报海外机房客户使用
		// 2.1 业务机房在国内
		// 1) 用户分布：国内（默认设置）
		// apiHost:'fp-it.portal101.cn'
		// 2) 用户分布：全球
		// apiHost:'fp-it-acc.portal101.cn'

		// 2.2 业务机房在欧美（弗吉尼亚）
		// 1) 用户分布：欧美
		// apiHost: 'fp-na-it.portal101.cn'
		// 2) 用户分布：全球
		// apiHost: 'fp-na-it-acc.portal101.cn'

		// 2.3 业务机房在欧美（法兰克福）
		// apiHost: 'api-device-eur.portal101.cn'

		// 2.4 业务机房在东南亚
		// 1) 用户分布：东南亚
		// apiHost:'fp-sa-it.portal101.cn'
		// 2) 用户分布：全球
		// apiHost:'fp-sa-it-acc.portal101.cn'

		// 2.5 私有化特殊配置
		// staticHost: 'xxxxxx' // 私有化客户自己引入线上cdn地址，此项必填；如果客户本地引入js文件，此项不填
		// apiHost: 'xxxxxx';  // 私有化部署的服务域名
	};
	const url = (function () {
		const isHttps = document.location.protocol === 'https:';
		const protocol = isHttps ? 'https://' : 'http://';
		const fpJsPath = '/dist/web/v3.0.0/fp.min.js';
		const url = protocol + _smConf.staticHost + fpJsPath;
		return url;
	})();
	const sm = document.createElement('script');
	const s = document.getElementsByTagName('script')[0];
	sm.src = url;
	sm.onerror = () => {
		window.SMSdkError = true;
	};
	s.parentNode.insertBefore(sm, s);
};

/**
 * 绑定事件
 * @param element
 * @param eventType
 * @param func
 */
export const bindEvent = (element, eventType, func) => {
	if (element.addEventListener) {
		element.addEventListener(eventType, func, false);
	} else if (element.attachEvent) {
		eventType = `on${eventType}`;
		element.attachEvent(eventType, func);
	} else {
		eventType = `on${eventType}`;
		element[eventType] = func;
	}
};
/**
 * cb业务逻辑
 * 使用数美设备标识逻辑
 */
export const dealSmDeviceId = (cb) =>
	new Promise((resolve, reject) => {
		let smDeviceId = '';
		let smDeviceIdReady = false;
		if (window.SMSdkError) {
			reject(new Error('数美加载js失败'));
			return;
		}
		SMSdk.ready(() => {
			if (SMSdk.getDeviceId) {
				smDeviceId = SMSdk.getDeviceId();
			}
			if (!smDeviceIdReady) {
				smDeviceIdReady = true;
				// 执行业务逻辑
				if (cb) {
					cb(smDeviceId);
					resolve(smDeviceId);
				} else {
					reject(new Error('未传入回调函数'));
				}
			}
		});
	});

/**
 *  数美验证码回调
 *  @param SMCaptcha
 */
export const getSMCaptchaResult = (SMCaptcha, onSuccessCallback) => {
	// 重置验证状态
	SMCaptcha.reset();
	// 重置成功回调
	SMCaptcha.resetSuccessCallback();
	// 打开验证
	SMCaptcha.verify();
	// 只绑定一次事件监听器
	SMCaptcha.onSuccess((data) => {
		console.log(data);
		if (data.pass) {
			if (onSuccessCallback) {
				onSuccessCallback(data);
			}
		}
	});

	SMCaptcha.onError((errType, errMsg) => {
		console.error('onError', errType, errMsg);
	});

	SMCaptcha.onClose(() => {
		console.log('关闭弹框了');
	});
};
export const getSMCaptcha = (args) =>
	new Promise((resolve, reject) => {
		if (window?.initSMCaptcha) {
			window.initSMCaptcha(
				{
					organization: 'OioUFNPPerhbBNzpgJoi',
					product: 'popup',
					...args,
				},
				(SMCaptcha) => {
					resolve(SMCaptcha);
				},
			);
		} else {
			reject(new Error('initSMCaptcha is not defined'));
		}
	});
