.t-dropdown {
  border-color: transparent !important;
  border-radius: var(--kyy_radius_dropdown_m, 8px);
  color: var(--select-kyy_color_select_cascade_item_text, #1A2139);
  background: var(--kyy_color_dropdown_bg_default, #FFF);
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.12) !important;
  padding: 4px;
}

.t-dropdown__menu {
  padding: 0 !important;
}

.t-dropdown__item {
  padding: 5px 16px;
  border-radius: var(--kyy_radius_dropdown_s, 4px);

  &:hover {
    background: var(--kyy_color_dropdown_bg_hover, #F3F6FA) !important;
  }

  &:active {
    background: var(--kyy_color_dropdown_bg_active, #E1EAFF) !important;
  }
}

.t-dropdown__item--theme-default.t-dropdown__item--suffix:hover {
  color: var(--select-kyy_color_select_cascade_item_text, #1A2139);
  background-color: var(--kyy_color_dropdown_bg_hover, #F3F6FA);
}



