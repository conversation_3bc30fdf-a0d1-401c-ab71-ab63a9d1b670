/**
 * 检查文件是否是图片类型
 * @param file - 要检查的文件对象
 * @returns 是否为图片类型
 */
export const isImageFile = (file: File): boolean =>
  file.type.startsWith('image/');

/**
 * 检查文件是否符合accept条件
 * @param file - 要检查的文件对象
 * @param accept - accept字符串，如 "image/*" 或 "image/png,image/jpeg"
 * @returns 是否符合accept条件
 */
export const isFileAccepted = (file: File, accept?: string): boolean => {
  if (!accept) return true;

  const fileType = file.type.toLowerCase();
  const fileName = file.name.toLowerCase();

  // 将accept字符串分割成数组
  const acceptedTypes = accept
    .toLowerCase()
    .split(',')
    .map((type) => type.trim());

  return acceptedTypes.some((acceptType) => {
    // 精确匹配MIME类型 (如: image/png)
    if (acceptType === fileType) return true;

    // 通配符匹配 (如: image/*)
    if (acceptType.endsWith('/*')) {
      const prefix = acceptType.slice(0, -2);
      return fileType.startsWith(prefix);
    }

    // 文件扩展名匹配 (如: .png, .jpg)
    if (acceptType.startsWith('.')) {
      return fileName.endsWith(acceptType);
    }

    return false;
  });
};

/**
 * 验证单个文件是否符合图片要求
 * @param file - 要验证的文件
 * @param accept - 接受的文件类型
 * @returns 是否验证通过
 */
const validateSingleImageFile = (file: File, accept?: string): boolean => {
  return isImageFile(file) && isFileAccepted(file, accept);
};

/**
 * 生成支持格式的错误提示文本
 * @param accept - 接受的文件类型
 * @returns 格式化的错误提示文本
 */
const getSupportTypesErrorMessage = (accept?: string): string => {
  const supportTypesStr = accept
    ?.split(',')
    .map((type) => type.split('/')[1])
    .join(', ');
  return `仅支持 ${supportTypesStr} 格式上传`;
};

// 函数重载：支持单个文件验证
export function validateImageFile(
  file: File,
  accept?: string,
): { valid: boolean; error?: string };

// 函数重载：支持多个文件验证
export function validateImageFile(
  files: File[],
  accept?: string,
): { valid: boolean; error?: string; failedFiles?: File[] };

/**
 * 验证上传的图片文件
 * @param fileOrFiles - 要验证的文件对象或文件数组
 * @param accept - 接受的文件类型
 * @returns 验证结果，包含是否通过和错误信息
 */
export function validateImageFile(
  fileOrFiles: File | File[],
  accept?: string,
): { valid: boolean; error?: string; failedFiles?: File[] } {
  // 处理单个文件的情况
  if (!Array.isArray(fileOrFiles)) {
    const file = fileOrFiles;
    if (!validateSingleImageFile(file, accept)) {
      return {
        valid: false,
        error: getSupportTypesErrorMessage(accept),
      };
    }
    return { valid: true };
  }

  // 处理多个文件的情况
  const files = fileOrFiles;
  const failedFiles: File[] = [];

  for (const file of files) {
    if (!validateSingleImageFile(file, accept)) {
      failedFiles.push(file);
    }
  }

  if (failedFiles.length > 0) {
    return {
      valid: false,
      error: getSupportTypesErrorMessage(accept),
      failedFiles,
    };
  }

  return { valid: true };
}
