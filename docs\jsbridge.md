## JSBridge

#### 作为对桌面端功能的封装，意在抹平各端的差异，提供统一的接口，降低心智负担, 提高开发效率

#### 在桌面端无论是在 web 形态还是原生形态都可以使用 jsbridge 方法，方便开发者使用。

```js

// 方式 1: 推荐，从包根目录导入
import { jsbridge } from '@rk/unitPark';

// 方式 2: 如果需要直接从 jsbridge 模块导入
import { jsbridge } from '@rk/unitPark/utils/jsbridge';

1. =====发送消息=====

  // web端iframe发送消息给父级
  await jsbridge.send('web-message', { a: '123' });

  // ringkol electron端发送消息给指定iframe
  await jsbridge.send('electron-message', { a: '123' }, document.querySelector('#iframe'));

2. =====ringkol electron中调用sdk=====

  // electron端使用sdk , sdk 等同于桌面端LynkerSDK方法
  // 打开我的订单
  jsbridge.sdk().openMyOrderWindow();

  // 打开另可开发者控制台
  jsbridge.sdk().openDebugTools();

3. =====消息监听=====

  const messageHandler = (data) => {
    console.log('messageHandler', data);
  }
  jsbridge.onMessage(messageHandler)

  // vue hooks ， 注意：必须在setup中使用，hooks自带生命周期，不需要手动移除
  jsbridge.useJsBridge(async (messageData: MessageData) => {
    console.log('Received message via hook:', messageData);

    switch (messageData.type) {
      case 'child-to-parent':
        // 处理来自子窗口的消息
        break;
      case 'iframe':
        // 处理来自iframe的消息
        break;
      case 'parent-to-child':
        // 处理来自父窗口的消息
        console.log('Received message from parent:', messageData.data);
        break;
    }
  });

4. =====消息移除=====

  jsbridge.onMessage(null);

```

### 示例

demo:

```jsx
// 1.引入组件
import { jsbridge } from '@rk/unitPark';

console.log('web -------------1');
// 发送消息
await jsbridge.send('custom-action', { a: '123' });
console.log('web -------------2');
const messageHandler = (data) => {
  console.log('web messageHandler====>', data);
};
jsbridge.onMessage(messageHandler);
```
