/**
 * title: 基础裁剪
 * description: 裁剪基础组件，其它弹窗组件基于此组件实上层封装（一般不会在业务中用到此组件）
 */

<template>
  <div class="demo-wrap">
    <div class="cropper-wrap">
      <RkImageCropper
        :key="refreshKey"
        ref="refCropper"
        v-bind="cropProps"
        @crop="onCrop"
        @readied="onCropReady"
        @confirm="onCropConfirm"
      />

      <img
        v-if="imgBase64"
        :src="imgBase64"
        alt=""
        class="img"
      >
    </div>

    <div class="config-wrap">
      <p class="item">
        圆形
        <t-switch
          v-model="cropProps.circled"
          :custom-value="[true, false]"
          @change="refreshKey++"
        />
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkImageCropper } from '@rk/unitPark';
import { Switch as TSwitch } from 'tdesign-vue-next';

const loading = ref(true);
const refCropper = ref();
const cropProps = ref({
  src: 'https://tdesign.gtimg.com/demo/demo-image-1.png',
  options: {
    aspectRatio: 1,
  },
  width: '296px',
  height: '296px',
  circled: false,
})

const imgBase64 = ref('');
const onCrop = (data: any) => {
  imgBase64.value = data.base64;
};

const onCropReady = () => {
  loading.value = false;
  refCropper.value.handleCropper('setCropBoxData', { top: '50%' });
};

const onCropConfirm = (file: File) => {
  console.log(file);
}

const refreshKey = ref(1);
</script>

<style scoped lang="less">
.demo-wrap {
  display: flex;
  align-items: flex-start;
}

.cropper-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.img {
  display: block;
  margin: 0 auto;
  width: 200px;
  height: 200px;
  object-fit: cover;
  border: 1px solid lightgray;
}

.config-wrap {
  width: 200px;
  padding: 0 8px;
  border: 1px solid lightgray;
  border-radius: 4px;
  background-color: #f5f5f5;
  p {
    display: flex;
    justify-content: space-between;
  }
}
</style>
