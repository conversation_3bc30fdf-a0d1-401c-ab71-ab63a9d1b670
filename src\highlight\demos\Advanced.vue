<template>
  <div class="demo-container">
    <h4>全词匹配</h4>
    <div class="demo-item">
      <p>原文：{{ wordContent }}</p>
      <p>
        普通匹配 "script"：
        <RkHighlight
          :content="wordContent"
          keyword="script"
        />
      </p>
      <p>
        全词匹配 "script"：
        <RkHighlight
          :content="wordContent"
          keyword="script"
          whole-word
        />
      </p>
    </div>

    <t-divider />

    <h4>动态高亮</h4>
    <div class="demo-item">
      <div class="search-box">
        <t-input
          v-model="searchKeyword"
          placeholder="输入要高亮的关键词"
          clearable
        />
      </div>
      <p>原文：{{ dynamicContent }}</p>
      <p>
        高亮结果：
        <RkHighlight
          :content="dynamicContent"
          :keyword="searchKeyword"
          :ignore-case="false"
        />
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkHighlight } from '../index';
import { Input as TInput, Divider as TDivider } from 'tdesign-vue-next';

const wordContent = 'JavaScript 和 TypeScript 都使用 script 标签。ECMAScript 是标准。';
const dynamicContent = 'Vue.js 是一个优秀的前端框架，具有响应式数据绑定和组件系统的特点。';

const searchKeyword = ref('Vue');
</script>

<style lang="less" scoped>
@import '../../demo-common.less';

.search-box {
  margin-bottom: 12px;
  max-width: 300px;
}
</style>
