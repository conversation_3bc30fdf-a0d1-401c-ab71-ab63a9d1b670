<script setup lang="ts">
import RkTextEllipsis from '../index.vue';

const text1 = '这是一段很长的文本内容，用来测试文本省略组件的基础功能。当文本超过指定行数时，会自动进行省略处理，并显示展开按钮。';

const text2 = `这是一段更长的文本内容，包含多个段落。
第一段：Vue 3 是一个渐进式的 JavaScript 框架，用于构建用户界面。它在保持易用性的同时，提供了更好的性能和更小的包体积。
第二段：组合式 API 是 Vue 3 的一个重要特性，它提供了一种更加灵活的方式来组织组件逻辑。通过 setup 函数，我们可以更好地复用逻辑。
第三段：这个文本省略组件支持多行省略，可以根据指定的行数来进行截断，并提供展开和收起的功能。`;
</script>

<template>
  <div class="demo-container">
    <h3>基础用法</h3>
    <p>默认展示 1 行，超过 1 行显示省略号。</p>
    <div class="demo-section">
      <TextEllipsis :content="text1" />
    </div>

    <h3>多行省略</h3>
    <div class="demo-section">
      <RkTextEllipsis
        :rows="3"
        :content="text2"
        expand-text="展开"
        collapse-text="收起"
      />
    </div>

    <h3>自定义省略符</h3>
    <div class="demo-section">
      <RkTextEllipsis
        :rows="1"
        :content="text1"
        dots=">>>"
        expand-text="展开"
        collapse-text="收起"
      />
    </div>

    <h3>头部省略</h3>
    <div class="demo-section">
      <RkTextEllipsis
        :rows="1"
        :content="text1"
        position="start"
        expand-text="展开"
        collapse-text="收起"
      />
    </div>

    <h3>中间省略</h3>
    <div class="demo-section">
      <RkTextEllipsis
        :rows="1"
        :content="text1"
        position="middle"
        expand-text="展开"
        collapse-text="收起"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
@import '../../demo-common.less';
</style>
