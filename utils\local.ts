// 将键值对保存到 LocalStorage
const saveToLocal = (key: string, value: string) => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    if (
      error instanceof DOMException &&
      ['QuotaExceededError', 'NS_ERROR_DOM_QUOTA_REACHED'].includes(error.name)
    ) {
      console.error('LocalStorage 存储空间不足');
      return;
    }
    console.error(`保存 ${key} 到 LocalStorage 时发生错误:`, error);
  }
};

// 从 LocalStorage 中获取指定键的值
const getFromLocal = (key: string): string => {
  try {
    return localStorage.getItem(key) || '';
  } catch (error) {
    console.error(`从 LocalStorage 获取 ${key} 时发生错误:`, error);
    return '';
  }
};
