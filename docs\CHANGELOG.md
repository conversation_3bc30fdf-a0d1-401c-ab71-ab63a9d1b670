## [0.0.11-beta.34](/compare/v0.0.11-beta.18...v0.0.11-beta.34) (2025-06-24)

### Bug Fixes

- 1 1281463
- 2 728c976
- 版本号 b69fa38
- 修复 Select 默认选中问题 7d415c1
- 修复 Select 默认选中问题 5b40070
- could not resolve entry module src/types/index.ts ad7e846
- **RkHighlight:** 增加文本高亮支持换行 3cfcee0
- tab 增加 popup confirm 84a392b
- **UploadImage:** 图片上传进度显示及状态管理优化 36c8ccc

### Features

- 新增头部以上固定 c029581
- 新增头部以上固定 19ec606
- 新增文本高亮组件，支持关键词高亮、忽略大小写和自定义样式 453ed6f
- 修改 UI 84a4340
- 增加 env 和 token 处理功能，支持从 URL 参数获取并存储 token c3bead4
- **EllipsisTooltip:** 新增 EllipsisTooltip 组件，支持文本溢出检测及 tooltip 显示，更新相关文档和示例 1aa9603
- filter 显示优化 fb3fa30
- **Map:** 更新百度地图组件，优化位置选择逻辑，增加地图初始化状态管理 a13f931
- **Map:** 更新外部地图打开逻辑，增加回调函数以支持桌面端处理 9c04ab6
- **Map:** 修改地图点击事件，增加用户点击标识以控制外部地图打开逻辑 0aa7caa
- **RkBaiduMapSelector:** 地图选择器弹窗默认垂直居中 6f2e3fc
- **RkUploadImage:** 增加错误提示显示控制功能 086fc27
- select&tabs 支持 defaultInfo 数据变化监听 998cb7f
- select 初始监听 b8ba037
- select 初始监听 6b48cc8
- **TextEllipsis:** 添加文本省略组件，支持多行文本省略及展开/收起功能，并更新相关示例 472f1e9
- **UploadImage:** 优化上传逻辑，重构组件属性，增加类型支持 5e4c6db

## [0.0.11-beta.33](/compare/v0.0.11-beta.18...v0.0.11-beta.33) (2025-06-23)

### Bug Fixes

- 1 1281463
- 2 728c976
- 版本号 b69fa38
- 修复 Select 默认选中问题 7d415c1
- 修复 Select 默认选中问题 5b40070
- could not resolve entry module src/types/index.ts ad7e846
- **RkHighlight:** 增加文本高亮支持换行 3cfcee0
- tab 增加 popup confirm 84a392b
- **UploadImage:** 图片上传进度显示及状态管理优化 36c8ccc

### Features

- 新增头部以上固定 c029581
- 新增头部以上固定 19ec606
- 新增文本高亮组件，支持关键词高亮、忽略大小写和自定义样式 453ed6f
- 修改 UI 84a4340
- 增加 env 和 token 处理功能，支持从 URL 参数获取并存储 token c3bead4
- filter 显示优化 fb3fa30
- **Map:** 更新百度地图组件，优化位置选择逻辑，增加地图初始化状态管理 a13f931
- **Map:** 更新外部地图打开逻辑，增加回调函数以支持桌面端处理 9c04ab6
- **Map:** 修改地图点击事件，增加用户点击标识以控制外部地图打开逻辑 0aa7caa
- **RkBaiduMapSelector:** 地图选择器弹窗默认垂直居中 6f2e3fc
- **RkUploadImage:** 增加错误提示显示控制功能 086fc27
- select&tabs 支持 defaultInfo 数据变化监听 998cb7f
- select 初始监听 b8ba037
- select 初始监听 6b48cc8
- **UploadImage:** 优化上传逻辑，重构组件属性，增加类型支持 5e4c6db

## [0.0.11-beta.24](/compare/v0.0.11-beta.18...v0.0.11-beta.24) (2025-06-17)

### Bug Fixes

- 版本号 b69fa38
- 修复 Select 默认选中问题 7d415c1
- 修复 Select 默认选中问题 5b40070
- could not resolve entry module src/types/index.ts ad7e846

### Features

- 新增文本高亮组件，支持关键词高亮、忽略大小写和自定义样式 453ed6f
- **RkBaiduMapSelector:** 地图选择器弹窗默认垂直居中 6f2e3fc
- select&tabs 支持 defaultInfo 数据变化监听 998cb7f
- select 初始监听 b8ba037
- select 初始监听 6b48cc8

## [0.0.11-beta.20](/compare/v0.0.11-beta.18...v0.0.11-beta.20) (2025-06-16)

### Bug Fixes

- could not resolve entry module src/types/index.ts ad7e846

### Features

- 新增文本高亮组件，支持关键词高亮、忽略大小写和自定义样式 453ed6f
- select&tabs 支持 defaultInfo 数据变化监听 998cb7f

## [0.0.11-beta.18](/compare/v0.1.0-beta.1...v0.0.11-beta.18) (2025-06-10)

### Bug Fixes

- 1 ([4fc73c1](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/4fc73c1ab2563d821dca79a53b49f8cc0b86ea4a))
- 1 ([4c46513](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/4c465136d180b207a16b8ff2992d5ca54fcb5d71))
- 1 ([61ba548](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/61ba548f52fed986e5fc22699654721d85c9b306))
- 2 ([433f917](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/433f917a60041665608c3f45c292abb75ddf52de))
- RkBaiduMapRouteCard ([f4a9e7e](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/f4a9e7e16e2f200513a2d27a40737df2fe5a9b03))

### Features

- 新增图片预览功能，更新上传组件以支持文件大小限制和预览事件 ([3b822d5](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/3b822d5f26f7fd74689179634db912b60b676dd3))
- filter 的 select 支持多选 ([9457ee4](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/9457ee4e175245002f98e5d5e296c9af427c707c))
- routeMap ([878a340](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/878a340300beb6e9c44862fa226619fdb7bcd4ac))
- **Upload:** 为上传组件添加图片裁剪功能，更新相关文档和示例，优化上传逻辑 ([e5e211e](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/e5e211e413c2e1da9ba9149ff8d0c6836ccd69b3))
- **Upload:** 为上传组件添加唯一 ID 生成和响应式排序配置，优化自动排序和手动排序功能 ([cce8a83](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/cce8a835c4405d3d5bbf2ef6884cb10795fd69e0))
- **Upload:** 新增图片拖拽排序功能，更新上传组件以支持自动排序和手动排序模式 ([ad5735a](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/ad5735a3e60931176c0a5a51bc26f3a05c3e18b2))

### Reverts

- version ([611f9ff](https://codehub-cn-south-1.devcloud.huaweicloud.com/ed8fa6717b3d4a9aa2e23215d2084afd/ringkol-unitPark/commit/611f9ff822f34a44ff41a08a4b7d66ce1aba39c8))
