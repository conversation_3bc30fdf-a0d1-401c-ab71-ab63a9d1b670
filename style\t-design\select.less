.t-select-option.t-is-selected {
  background: #fff;
}

body .t-select__dropdown {
  z-index: 99999999999999;
}

.t-select-option.t-is-selected:hover {
  background-color: var(--td-bg-color-container-hover);
}


.t-select__dropdown-inner {
  .opt {
    display: flex;
    span {
      // flex: 1;
      width: 100%;
    }
    .con {
      display: flex;
      align-items: center;
      padding: 9px 0;
      gap: 8px;
      width: 100%;
      
      &-icon {
        flex: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
      &-text {
        color: var(--text-kyy_color_text_1, #1A2139);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }
}