{"editor.defaultFormatter": "esbenp.prettier-vscode", "npm.packageManager": "yarn", "editor.formatOnSave": true, "prettier.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "files.exclude": {"**/node_modules": true}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.associations": {"*.env": "env", "*.env.*": "env"}, "typescript.tsdk": "node_modules\\typescript\\lib"}