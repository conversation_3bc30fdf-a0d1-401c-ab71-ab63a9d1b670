/**
 * @file Electron环境通信实现
 * @description 处理Electron环境下的IPC通信
 */

import { getEnv } from '../base';
import { IpcResponse, MessageData } from '../types';

export class ElectronCommunicator {
  private messageHandler?: (messageData: MessageData) => Promise<void>;

  public onMessage(
    handler: (messageData: MessageData) => Promise<void> | null,
  ): void {
    if (handler === null) {
      this.messageHandler = undefined;
      window.removeEventListener('message', this.handleWebMessage.bind(this));
      return;
    }
    this.messageHandler = handler;
    window.addEventListener('message', this.handleWebMessage.bind(this));
  }

  public async send<T = any>(
    messageData: MessageData,
  ): Promise<IpcResponse<T>> {
    if (!getEnv().isRingkolDesktopApp) {
      throw new Error('Not in Ringkol Desktop App environment');
    }
    console.log('electron handleMessage:', messageData);
    if (!window.LynkerSDK?.ipcRenderer?.invoke) {
      console.error(
        '[ElectronCommunicator] LynkerSDK.ipcRenderer.invoke not available',
      );
      throw new Error('LynkerSDK.ipcRenderer not initialized');
    }

    try {
      console.log('[ElectronCommunicator] Sending message:', messageData);
      const response = await window.LynkerSDK.ipcRenderer.invoke(
        messageData.action,
        messageData.data,
      );
      console.log('[ElectronCommunicator] Received response:', response);

      return response;
    } catch (error) {
      console.error('[ElectronCommunicator] Error sending message:', error);
      throw error;
    }
  }

  private async handleWebMessage(event: MessageEvent): Promise<void> {
    console.log('electron handleMessage:', event);
    try {
      const messageData: MessageData =
        typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

      if (this.messageHandler) {
        await this.messageHandler(messageData);
      }
    } catch (error) {
      console.error(
        '[ElectronCommunicator] Error handling web message:',
        error,
      );
    }
  }

  /**
   * 获取 SDK 实例
   * @param data 可选的初始化数据
   * @returns SDK 实例
   */
  public sdk(data?: any) {
    if (!window.LynkerSDK) {
      console.warn('LynkerSDK not found in window object');
      return null;
    }
    return window.LynkerSDK;
  }
}
