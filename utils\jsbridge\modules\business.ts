import type { IpcResponse } from '../types';
import type { BusinessConfig, UserInfo } from '../types/business';
import { BaseModule } from './base';

export class BusinessModule extends BaseModule {
  public getUserInfo(): Promise<IpcResponse<UserInfo>> {
    return this.send<UserInfo>('getUserInfo');
  }

  public getBusinessConfig(): Promise<IpcResponse<BusinessConfig>> {
    return this.send<BusinessConfig>('getBusinessConfig');
  }

  public updateBusinessConfig(
    config: Partial<BusinessConfig>,
  ): Promise<IpcResponse<BusinessConfig>> {
    return this.send<BusinessConfig>('updateBusinessConfig', config);
  }
}
