/**
 * title: 只读
 * description: 设置 readonly 属性后，图片将不可编辑。
 */

<template>
  <div class="demo-wrap">
    <RkUploadImage
      v-model="imgUrls"
      root-dir="换成上传OSS根目录"
      readonly
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkUploadImage } from '@rk/unitPark';

const imgUrls = ref([
  'https://tdesign.gtimg.com/demo/demo-image-1.png',
  'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/shop_apply_bg.png',
  'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/upload_logo_example.png',
]);
</script>

<style scoped lang="less">
.demo-wrap {
  display: flex;
  align-items: flex-start;
}
</style>
