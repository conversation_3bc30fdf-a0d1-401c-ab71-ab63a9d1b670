import { RequestMethodResponse, UploadFile } from 'tdesign-vue-next';
import { ref } from 'vue';
import { multipartUpload } from '../../../utils/upload';

type UploadOptions = {
  rename: boolean;
  rootDir?: string;
  partSize?: number;
  onProgress?: (progress: number) => void;
};

/**
 * 图片上传
 * @param uploadSuccess - 上传成功回调
 * @param opts - 上传选项
 * @returns 图片上传
 */
export function useImageUploadWithProgress(
  uploadSuccess?: (response: any, fileList: { url: string }[]) => void,
  opts: UploadOptions = { rename: true, rootDir: 'common' },
) {
  const images = ref<{ url: string }[]>([]);
  const uploading = ref(false);
  const uploadProgress = ref(0);

  const uploadImage = (file: UploadFile): Promise<RequestMethodResponse> =>
    new Promise((resolve) => {
      uploading.value = true;
      uploadProgress.value = 0;

      const uploadFile = Array.isArray(file) ? file[0] : file;
      const fileList = images.value || [];

      multipartUpload(uploadFile, {
        ...opts,
        partSize: 102400,
        onProgress: (p) => {
          const progress = Math.floor(p * 100);
          uploadProgress.value = progress;
          opts.onProgress?.(progress);
        },
      })
        .then((res: any) => {
          const response = { url: res.url };
          const newFileList = [...fileList, { url: res.url }];
          images.value = newFileList;
          uploadSuccess?.({ ...response, file }, newFileList);
          resolve({ status: 'success', response });
        })
        .catch((response) => {
          resolve({ status: 'fail', response });
        })
        .finally(() => {
          uploading.value = false;
          uploadProgress.value = 0;
        });
    });

  const removeImage = (index: number) => {
    if (images.value) {
      images.value.splice(index, 1);
    }
  };

  return {
    images,
    uploading,
    uploadProgress,
    uploadImage,
    removeImage,
  };
}
