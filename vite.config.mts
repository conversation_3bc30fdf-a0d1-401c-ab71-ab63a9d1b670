import { defineConfig } from 'vite';
import { resolve } from 'node:path';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import fs from 'fs';

// 排除的外部依赖
const externals = ['vue', 'tdesign-vue-next'];

// 获取所有 utils 目录下的文件（包括子文件夹）
const getAllUtilsEntries = () => {
  const utilsPath = resolve(__dirname, 'utils');

  // 递归获取所有 .ts 文件
  const getFiles = (dir: string, baseDir: string = ''): Record<string, string> => {
    const entries: Record<string, string> = {};

    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const fullPath = resolve(dir, file);
      const relativePath = baseDir ? `${baseDir}/${file}` : file;

      if (fs.statSync(fullPath).isDirectory()) {
        // 递归处理子目录
        Object.assign(entries, getFiles(fullPath, relativePath));
      } else if (file.endsWith('.ts') && !file.endsWith('.d.ts')) {
        // 排除 .d.ts 文件，只包含 .ts 文件
        const name = relativePath.replace('.ts', '');
        entries[`utils/${name}`] = fullPath;
      }
    });

    return entries;
  };

  return getFiles(utilsPath);
};

// 获取所有 type.ts 文件
const getTypeFiles = () => {
  const typeEntries: Record<string, string> = {};
  
  const processDir = (dir: string) => {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const fullPath = resolve(dir, file);
      if (fs.statSync(fullPath).isDirectory()) {
        processDir(fullPath);
      } else if (file === 'type.ts') {
        const relativePath = fullPath.replace(__dirname + '/src/', '').replace('/type.ts', '');
        typeEntries[`types/${relativePath}/type`] = fullPath;
      }
    });
  };

  processDir(resolve(__dirname, 'src'));
  return typeEntries;
};

// 获取所有 src 目录下的入口
const getSrcEntries = () => {
  return ['index', ...fs.readdirSync(resolve(__dirname, 'src'))
    .filter(dir => fs.statSync(resolve(__dirname, 'src', dir)).isDirectory())]
    .reduce((entries, dir) => {
      entries[dir] = resolve(__dirname, 'src', dir === 'index' ? 'index.ts' : `${dir}/index.ts`);
      return entries;
    }, {});
};

export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
  ],
  build: {
    minify: 'terser',
    lib: {
      entry: {
        ...getSrcEntries(),
        ...getAllUtilsEntries(),
        ...getTypeFiles(),
      },
      name: 'index',
      formats: ['es'], // 只使用 ES Module 格式
      fileName: (_, entryName) => `${entryName}.js`, // 简化文件名，不再需要格式后缀
    },
    rollupOptions: {
      external: [...externals],
      output: {
        globals: {
          'vue': 'Vue',
        },
        // 确保 chunk 名称不包含 hash
        chunkFileNames: 'chunks/[name].js',
        assetFileNames: 'assets/[name].[ext]'
      },
    },
    outDir: 'dist',
  },
  resolve: {
    dedupe: ['vue'],
  },
  optimizeDeps: {
    include: [...externals],
  },
});
