.t-tag {
  padding: 0 4px !important;
  border-radius: var(--kyy_radius_tag_s, 4px);
  height: 20px;
}

.t-tag--default {
  color: var(--kyy_color_tag_text_gray, #516082);
  background: var(--kyy_color_tag_bg_gray, #ECEFF5);
}

.t-tag--primary {
  &.t-tag--outline,
  &.t-tag--light-outline {
    color: var(--kyy_color_tag_text_brand, #4D5EFF) !important;
  }
  &.t-tag--light {
    background: var(--kyy_color_tag_bg_brand, #EAECFF) !important;
  }
}

.t-tag--success {
  &.t-tag--light {
    color: var(--kyy_color_tag_text_success, #499D60) !important;
    background: var(--kyy_color_tag_bg_success, #E0F2E5) !important;
  }
}

.t-tag--warning {
  &.t-tag--light {
    color: var(--kyy_color_tag_text_warning, #FC7C14) !important;
    background: var(--kyy_color_tag_bg_warning, #FFE5D1) !important;
  }
}


.TagColorsRed {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #F7D5DB;
  }
}

.TagColorsOrange {
  &:hover {
    background-color: #FFE5D1 !important;
  }
}

.TagColorsGreen {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #E0F2E5 !important;
  }
}

.TagColorsMagenta {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #FFE3F1 !important;
  }
}

.TagColorsPurple {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #FAEDFD !important;
  }
}


.TagColorsYellow {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #FFF9E8 !important;
  }
}

.TagColorsCyan {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #E6F9F8 !important;
  }
}

.TagColorsBlue {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #EAECFF !important;
  }
}

.TagColorsMediumBlue {
  transition: all 0.15s linear ;
  &:hover {
    transition: all 0.15s linear ;
    background-color: #E8F0FB !important;
  }
}

.TagColorsLightBlue {
  &:hover {
    background-color: #E4F5FE !important;
  }
}