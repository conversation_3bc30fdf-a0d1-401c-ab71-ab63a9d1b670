// 对部分样式进行重置
body {
  color: var(--td-text-color-secondary);
  font-family: -apple-system, BlinkMacSystemFont, var(--td-font-family);
  font: var(--td-font-body-medium);
  -webkit-font-smoothing: antialiased;
  padding: 0;
  margin: 0;
}

pre {
  font-family: var(--td-font-family);
}

ul,
dl,
li,
dd,
dt {
  margin: 0;
  padding: 0;
  list-style: none;
}

figure,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

* {
  box-sizing: border-box;
}

.t-button-link,
a {
  color: var(--kyy_cyan);
  text-decoration: none;
  cursor: pointer;

  &:hover {
    // color: var(--kyy_cyan_hover);
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-hover, #707EFF);
  }

  &:active {
    // color: var(--kyy_cyan_active);
    color: var(--color-button-text-secondray-kyy-color-button-text-secondray-font-active, #3E4CD1);
  }

  &--active {
    color: var(--kyy_cyan_active);
  }

  &:focus {
    text-decoration: none;
  }
}

.t-button-link {
  margin-right: var(--td-comp-margin-xxl);

  &:last-child {
    margin-right: 0;
  }
}

.narrow-scrollbar::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

// 预览样式兼容主页顶部位置
.t-image-viewer.t-image-viewer-preview-image {
    top: 48px;
}

.t-is-error .t-form__controls-content .t-input{
  border: 1px solid var(--color-button-secondary-error-kyy-color-button-secondary-error-border-dedault, #D54941) !important;
}
