import type { BusinessConfig, UserInfo } from './business';
import type {
  CommunicationConfig,
  CommunicationTarget,
  MessageData,
  MessageType,
} from './communication';

export type {
  BusinessConfig,
  CommunicationConfig,
  CommunicationTarget,
  MessageData,
  MessageType,
  UserInfo,
};

export interface IpcResponse<T = any> {
  code: number;
  data: T;
  message?: string;
  callbackId?: string;
}
