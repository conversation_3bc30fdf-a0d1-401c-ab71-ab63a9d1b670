<template>
  <div
    v-if="innerSrc"
    ref="cropperRef"
    :class="{ 're-circled': inCircled }"
    :style="wrapperStyle"
    @contextmenu.prevent="onContextmenu"
  >
    <img
      v-show="isReady"
      ref="imgRef"
      :src="innerSrc"
      :alt="alt"
      :crossorigin="crossorigin"
      :style="imageStyle"
    >
  </div>
</template>

<script setup lang="tsx">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  type CSSProperties,
  type PropType,
  watchEffect,
} from 'vue';
import Cropper from 'cropperjs';
import 'cropperjs/dist/cropper.css';
import { useResizeObserver } from '@vueuse/core';
import './tippy.less';
import debounce from 'lodash/debounce';
import { blobToImage } from '../../utils/file';
import { useTippyMenu } from './useTippyMenu';

type Options = Cropper.Options;
type Nullable<T> = T | null;
type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>;

/**
 * cropperjs 默认选项
 * https://github.com/fengyuanchen/cropperjs/tree/main#options
 */
const defaultOptions: Options = {
  // 宽高比
  aspectRatio: 1,
  // 是否允许缩放
  zoomable: true,
  // 是否允许通过触摸缩放
  zoomOnTouch: true,
  // 是否允许通过滚轮缩放
  zoomOnWheel: true,
  // 是否允许移动裁剪框
  cropBoxMovable: true,
  // 是否允许调整裁剪框大小
  cropBoxResizable: true,
  // 是否允许通过双击切换拖动模式
  toggleDragModeOnDblclick: true,
  // 是否自动裁剪
  autoCrop: true,
  // 是否显示背景
  background: true,
  // 是否高亮裁剪框
  highlight: true,
  // 是否在初始化时将裁剪框居中
  center: true,
  // 是否响应窗口大小变化
  responsive: true,
  // 是否在重新初始化时恢复裁剪框
  restore: true,
  // 是否检查跨域
  checkCrossOrigin: true,
  // 是否检查图像方向
  checkOrientation: true,
  // 是否允许缩放图像
  scalable: true,
  // 是否显示模态
  modal: true,
  // 是否显示裁剪框的虚线
  guides: true,
  // 是否允许移动图像
  movable: true,
  // 是否允许旋转图像
  rotatable: true,
};

const props = defineProps({
  /**
   * @description 图片的源地址
   * @default 必填
   */
  src: {
    type: String,
    required: true
  },
  /**
   * @description 图片的替代文本
   * @default ""
   */
  alt: {
    type: String,
    default: ''
  },
  /**
   * @description 是否显示为圆形裁剪区域
   * @default false
   */
  circled: {
    type: Boolean,
    default: false
  },
  /**
   * @description 是否显示为椭圆形裁剪区域
   * @default false
   */
  ellipse: {
    type: Boolean,
    default: false
  },
  /**
   * @description 是否实时预览裁剪效果
   * @default true
   */
  realTimePreview: {
    type: Boolean,
    default: true
  },
  /**
   * @description 图片的宽度
   * @default "360px"
   */
  width: {
    type: [String, Number],
    default: '100%'
  },
  /**
   * @description 图片的高度
   * @default "360px"
   */
  height: {
    type: [String, Number],
    default: '360px'
  },
  /**
   * @description 图片的跨域属性
   * @default undefined
   */
  crossorigin: {
    type: String as PropType<'' | 'anonymous' | 'use-credentials' | undefined>,
    default: undefined,
  },
  /**
   * @description 图片的样式
   * @default {}
   * @type {CSSProperties} Object
   */
  imageStyle: {
    type: Object as PropType<CSSProperties>,
    default: () => ({})
  },
  /**
   * @description Cropper.js 的配置选项
   * @default {}
   */
  options: {
    type: Object as PropType<Options>,
    default: () => ({})
  },
});

type Emits = {
  /**
   * @description 当图片加载并准备好裁剪时触发
   * @param cropper Cropper.js 实例
   */
  (e: 'readied', cropper: Nullable<Cropper>): void;

  /**
   * @description 当裁剪操作开始时触发
   * @param payload 包含裁剪后的图片信息
   */
  (
    e: 'crop',
    payload: {
      base64: string;
      blob: Blob;
      info: {
        size: number;
        [key: string]: any;
      };
    },
  ): void;

  /**
   * @description 当发生错误时触发
   */
  (e: 'error'): void;

  /**
   * @description 当裁剪操作开始时触发
   */
  (e: 'crop-start'): void;
};
const emit = defineEmits<Emits>();

const imgRef = ref<ElRef<HTMLImageElement>>();
// 可裁剪图像的引用
const cropper = ref<Nullable<Cropper>>();
// 图像是否已加载完成并准备进行裁剪
const isReady = ref(false);
// 处理后的图像的 Base64 编码字符串
const imgBase64 = ref<string>();
// 图像是否需要圆形裁剪
const inCircled = ref(props.circled);
// 图像是否需要椭圆形裁剪
const inEllipse = ref(props.ellipse);
// 图像的内部源路径
const innerSrc = ref(props.src);

// 图像的水平缩放比例，默认为1
let scaleX = 1;
// 图像的垂直缩放比例，默认为1
let scaleY = 1;

// 监听 src 的变化，更新 innerSrc
watchEffect(() => {
  innerSrc.value = props.src;
  inCircled.value = props.circled;
  inEllipse.value = props.ellipse;
});

// 裁剪函数的防抖版本，以避免频繁触发裁剪操作
const debounceRealTimeCropped = debounce(realTimeCropped, 200);

// 图片样式
const imageStyle = computed<CSSProperties>(() => ({
  width: props.width,
  height: props.height,
  maxWidth: '100%',
  ...props.imageStyle,
}));

// 容器样式
const wrapperStyle = computed<CSSProperties>(() => ({
  width: `${String(props.width).replace(/px/, '')}px`,
  height: `${String(props.height).replace(/px/, '')}px`,
}));

onMounted(init);

onUnmounted(() => cropper.value?.destroy());

/**
 * 初始化 Cropper.js 实例
 */
function init() {
  const imgEl = imgRef.value;
  if (!imgEl) return;

  cropper.value = new Cropper(imgEl, {
    ...defaultOptions,
    ...props.options,
    ready: () => {
      isReady.value = true;
      realTimeCropped();
      setTimeout(() => emit('readied', cropper.value));
    },
    crop: debounceRealTimeCropped,
    zoom: debounceRealTimeCropped,
    cropmove: debounceRealTimeCropped,
  });
}

/**
 * 实时裁剪图片并触发事件
 */
function realTimeCropped() {
  props.realTimePreview && handleCrop();
}

/**
 * 处理裁剪操作，生成裁剪后的图片并触发事件
 */
function handleCrop() {
  if (!cropper.value) return;

  // 触发裁剪开始事件
  emit('crop-start');

  // 选择合适的画布处理是否需要圆形或椭圆形裁剪
  const canvas =
    inCircled.value || inEllipse.value
      ? getRoundedCanvas()
      : cropper.value.getCroppedCanvas();

  canvas?.toBlob((blob: Blob) => {
    if (!blob) return;

    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onloadend = (e) => {
      if (!e.target?.result) return;

      imgBase64.value = e.target.result as string;
      emit('crop', {
        base64: imgBase64.value,
        blob,
        info: {
          size: blob.size,
          ...cropper.value!.getData(),
        },
      });
    };

    reader.onerror = () => emit('error');
  });
}

/**
 * 获取圆形或椭圆形裁剪后的 canvas
 *
 * @returns {HTMLCanvasElement} 返回裁剪后的 canvas 元素
 */
function getRoundedCanvas(): HTMLCanvasElement {
  // 裁剪后的原始 canvas
  const sourceCanvas = cropper.value!.getCroppedCanvas();
  // 原始 canvas 的宽高
  const width = sourceCanvas.width;
  const height = sourceCanvas.height;

  // 创建一个新的 canvas 元素
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;

  // 设置新 canvas 的宽高与原始 canvas 一致
  canvas.width = width;
  canvas.height = height;

  // 启用图像平滑处理
  ctx.imageSmoothingEnabled = true;

  // 将原始 canvas 绘制到新 canvas 上
  ctx.drawImage(sourceCanvas, 0, 0, width, height);

  // 设置全局合成操作为 "destination-in"，用于裁剪图像
  ctx.globalCompositeOperation = 'destination-in';
  ctx.beginPath();

  // 根据配置绘制圆形或椭圆形路径
  const centerX = width / 2;
  const centerY = height / 2;
  if (inEllipse.value) {
    const radiusY = centerX / (props.options.aspectRatio || 1);
    ctx.ellipse(centerX, centerY, centerX, radiusY, 0, 0, Math.PI * 2);
  } else {
    const radius = Math.min(width, height) / 2;
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
  }

  // 填充路径，完成裁剪
  ctx.fill();

  // 返回裁剪后的 canvas
  return canvas;
}

/**
 * 调用 Cropper.js 的方法
 * @param action 方法名
 * @param args 方法参数
 */
function callCropperMethod(action: string, ...args: any[]) {
  (cropper.value as any)?.[action]?.(...args);
}

/**
 * 处理裁剪工具栏的操作
 * @param action 操作类型
 * @param args 操作参数
 */
function handleCropper(action: string, ...args: any[]) {
  const methods: Record<string, Function> = {
    scaleX: () => callCropperMethod(action, (scaleX *= -1)),
    scaleY: () => callCropperMethod(action, (scaleY *= -1)),
    reset: () => {
      scaleX = 1;
      scaleY = 1;
      callCropperMethod(action);
    },
    default: () => callCropperMethod(action, ...args),
  };

  // 如果 action 在 methods 中，则调用对应方法，否则调用 default
  ;(methods[action] || methods.default)();
}

// 添加右键菜单
const cropperRef = ref<ElRef<HTMLDivElement>>();
const { onContextmenu } = useTippyMenu(cropperRef, handleCropper);
useResizeObserver(cropperRef, () => handleCropper('reset'));

type PropsType = {
  // 弹窗标题
  title?: string;
  // 输出图片宽度（未指定则根据图片大小和 aspectRatio 自动确定输出尺寸）
  outputWidth?: number;
  // 输出图片高度（未指定则根据图片大小和 aspectRatio 自动确定输出尺寸）
  outputHeight?: number;
}

/**
 * 获取裁剪后的图片文件
 *
 * @param {Function} cb 回调函数，用于接收处理后的图像文件
 * @param {PropsType} options 包含输出图像宽度和高度的选项对象，默认为空对象
 */
const getCroppedFile = (cb: Function, options: PropsType = {}) => {
  const cropData: Cropper.GetCroppedCanvasOptions = {
    imageSmoothingQuality: 'high',
  };

  const { outputWidth, outputHeight } = options;
  if (outputWidth) cropData.width = outputWidth;
  if (outputHeight) cropData.height = outputHeight;

  cropper.value.getCroppedCanvas(cropData).toBlob(async (blob: Blob) => {
    cb?.(blobToImage(blob, 'cropper-img'));
  });
};

defineExpose({
  /**
   * @public
   * @description 裁剪实例
   */
  cropper,
  /**
   * @public
   * @description 处理裁剪工具栏的操作
   */
  handleCropper,
  /**
   * @public
   * @description 获取裁剪后的图片文件
   */
  getCroppedFile,
});
</script>

<style scoped lang="less">
.re-circled {
  border-radius: 50%;
  overflow: hidden;
}
</style>
