import { useTippy } from 'vue-tippy';
import MenuContext from './MenuContext.vue';

type HandleCropper = (action: string, ...args: any[]) => void;

export function useTippyMenu(tippyElRef: any, handleCropper: HandleCropper) {
  // 处理右键菜单事件的函数
  function onContextmenu(event: MouseEvent) {
    const { show, setProps } = useTippy(tippyElRef, {
      content: <MenuContext onCrop={handleCropper} />, // 菜单内容
      arrow: false, // 不显示箭头
      theme: 'light', // 使用浅色主题
      trigger: 'manual', // 手动触发显示
      interactive: true, // 允许用户与菜单交互
      appendTo: 'parent', // 将菜单添加到父元素中
      animation: 'perspective', // 使用透视动画
      placement: 'bottom-end', // 菜单显示在触发点的右下角
    });

    // 设置菜单的显示位置为鼠标点击的位置
    setProps({
      getReferenceClientRect: () => ({
        width: 0,
        height: 0,
        top: event.clientY,
        bottom: event.clientY,
        left: event.clientX,
        right: event.clientX,
      }),
    });

    // 显示菜单
    show();
  }

  return {
    onContextmenu,
  };
}
