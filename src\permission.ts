import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { profilesMe } from '@/api/account';

import { getPermissionStore, getUserStore } from '@/store';
import router from '@/router';

function isWechat(UA: string): boolean {
	return !!/MicroMessenger/i.test(UA);
}

function isWeibo(UA: string): boolean {
	return !!/Weibo/i.test(UA);
}

function isQQ(UA: string): boolean {
	return !!/QQ/i.test(UA);
}

function isMoible(UA: string): boolean {
	return !!/(Android|webOS|iPhone|iPod|tablet|BlackBerry|Mobile)/i.test(UA);
}

function isIOS(UA: string): boolean {
	return !!/iPhone|iPad|iPod/i.test(UA);
}

function isAndroid(UA: string): boolean {
	return !!/Android/i.test(UA);
}

function deviceType(UA) {
	if (isMoible(UA)) {
		if (isIOS(UA)) {
			if (isWechat(UA)) {
				return {
					type: 'ios',
					env: 'wechat',
					masklayer: true,
				};
			}
			if (isWeibo(UA)) {
				return {
					type: 'ios',
					env: 'weibo',
					masklayer: true,
				};
			}
			if (isQQ(UA)) {
				return {
					type: 'ios',
					env: 'qq',
					masklayer: true,
				};
			}
			return {
				type: 'ios',
			};
		}
		if (isAndroid(UA)) {
			if (isWechat(UA)) {
				return {
					type: 'android',
					env: 'wechat',
					masklayer: true,
				};
			}
			if (isWeibo(UA)) {
				return {
					type: 'android',
					env: 'weibo',
					masklayer: true,
				};
			}
			if (isQQ(UA)) {
				return {
					type: 'android',
					env: 'qq',
					masklayer: true,
				};
			}
			return {
				type: 'android',
			};
		}
		return {
			type: 'mobile',
		};
	}
	return {
		type: 'pc',
	};
}
NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, from, next) => {
	const deviceInfo = deviceType(navigator.userAgent);
	const routes: string = to.fullPath.replace('/link', '/molink');
	const moroutes: string = to.fullPath.replace('/molink', '/link');
	const ishasM = to.fullPath.indexOf('molink') !== -1;
	const ishasPC = to.fullPath.indexOf('link') !== -1;
	const params = to.query;
	console.log(ishasM, 'ishasMishasMishasM');

	console.log(`当前路由是否为移动端${deviceInfo.type}`);
	// 客户端←这个判断贼重要
	console.log(to.name, 'tototo');

	if (deviceInfo.type === 'pc' && to.name === 'molink') {
		// 设备为pc
		console.log(ishasM, 'ishasMishasM');

		if (ishasM) {
			next({ path: moroutes, query: params });
			console.log(moroutes, 'routesroutes');
		} else {
			next();
		}
		// console.log('设备：pc；路由：pc；跳转：pc');
		// console.log('不用跳转');
	} else if (deviceInfo.type !== 'pc' && !ishasM && to.name === 'link') {
		// 当前为移动端
		next({ path: routes, query: params });
	} else {
		next();
	}

	// console.log('设备：手机；路由：pc；跳转：手机');
	// console.log('跳转至：'+"/mobile"+routes);
	// return
	NProgress.start();

	const userStore = getUserStore();
	const permissionStore = getPermissionStore();
	const { whiteListRouters } = permissionStore;

	const { token } = userStore;
	console.log(token, 12211111111111);
	console.log(userStore, 3333333333);
	// next();
	const mainToken = window.localStorage.getItem('main_token');

	if (!userStore.userInfo.title && mainToken) {
		profilesMe().then((rs) => {
			userStore.userInfo.title = rs.data.title;
			userStore.userInfo.avatar = rs.data.avatar;
			console.log(rs, 'ssssssssssssssss');
		});
	}
});

router.afterEach((to) => {
	if (to.path === '/login') {
		const userStore = getUserStore();
		const permissionStore = getPermissionStore();

		userStore.logout();
		permissionStore.restore();
	}
	NProgress.done();
});
