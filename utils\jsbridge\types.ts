export interface MessageData {
  type:
    | 'child-to-parent'
    | 'parent-to-child'
    | 'iframe'
    | 'default'
    | 'electron';
  action: string;
  data?: any;
  callbackId?: string;
  source?: string;
  target?: Window;
}

export interface IpcResponse<T = any> {
  code: number;
  data: T | null;
  message: string;
}

export interface CommunicationTarget {
  window: Window;
  origin: string;
}

export type MessageHandler =
  | ((messageData: MessageData) => Promise<void>)
  | null;

// 环境配置接口
export interface LynkerSDK {
  ipcRenderer?: {
    invoke: (action: string, data?: any) => Promise<any>;
    send: (channel: string, data: any) => void;
  };
  config?: {
    env: string;
  };
}

declare global {
  interface Window {
    LynkerSDK?: LynkerSDK;
    __APP_ENV__?: {
      VITE_APP_CONFIG_INFO?: any;
    };
  }
}
