import { getConfig } from '../utils';
export const getErrorStatusMsg = (error: any) => {
  const status: number = error?.response?.status;
  const statusMsgMap: any = {
    301: '资源已被移除',
    303: '重定向',
    304: '资源没有被修改',
    404: '资源，服务未找到',
    405: '不允许的http方法',
    409: '资源冲突，或者资源被锁定',
    415: '不支持的数据（媒体）类型',
    418: error?.response?.message || '你输入的信息包含敏感内容，请修改后重试',
    429: '请求过多被限制',
    500: '系统内部错误',
    502: '网络异常',
  };
  return statusMsgMap[status] || '';
};

export function getServerLang() {
  const localLang = window.localStorage.getItem('lang') as 'zh-cn';
  const lang = { 'zh-hk': 'zh-hant-MO', 'zh-cn': 'zh-hans-CN' };
  return lang?.[localLang] || lang['zh-cn'];
}

// 获取token
export function getAccesstoken() {
  return window.localStorage.getItem('main_token') || getConfig().token;
}

// 设置token
export function setAccesstoken(token: string) {
  window.localStorage.setItem('main_token', token);
}

export const getSignUrl = (url: string, query?: any) => {
  if (query) {
    const keyArr = Object.keys(query);
    let str = '';
    keyArr.forEach((key, index) => {
      if (index === 0) {
        str += `${key}=${query[key]}`;
      } else {
        str += `&${key}=${query[key]}`;
      }
    });
    return `${url}?${str}`;
  }
  return url;
};
