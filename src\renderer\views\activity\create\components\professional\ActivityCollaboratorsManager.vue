<template>
  <div :style="`--scroll-height: ${tableMaxHeight}px`">
    <RTable
      :table="table"
      :filter="filter"
      @change="onTableChange"
    >
      <template #toolbarContent>
        <div v-if="isAllowEdit && hasEditPermission" class="toolbar-right">
          <!--如果个人在组织下有外部身份，则可以选择外部组织架构人员-->
          <t-dropdown
            v-if="selectedTeam.hasCards"
            overlay-class-name="activity-add-staffs-dropdown"
            :max-column-width="120"
          >
            <t-button
              class="w-108"
              theme="primary"
            >
              <iconpark-icon class="text-[#fff] text-20" name="iconadd" />
              <span class="ml-4">添加人员</span>
            </t-button>

            <t-dropdown-menu>
              <t-dropdown-item @click="addActors('inner')">内部组织架构</t-dropdown-item>
              <t-dropdown-item @click="addActors('outer')">外部组织架构</t-dropdown-item>
            </t-dropdown-menu>
          </t-dropdown>

          <!--无外部身份，只能选择组织内部人员-->
          <t-button
            v-else
            class="w-108"
            theme="primary"
            @click="addActors('inner')"
          >
            <iconpark-icon class="text-[#fff] text-20" name="iconadd" />
            <span class="ml-4">添加人员</span>
          </t-button>
        </div>
      </template>
    </RTable>

    <!-- 选人组件 -->
    <select-member
      v-model:visible="selectMemberVisible"
      :disabled-card="true"
      :show-dropdown-menu="false"
      :active-card-id="selectedTeamCardId"
      :change-menus="false"
      :menu="menus"
      :diy-data="diyData"
      :select-list="selectedIds"
      :attach="'body'"
      :extend-from="['activity']"
      :disable-list="disabledList"
      @confirm="onSelectMemberConfirm"
    />

    <activity-permission-dialog
      ref="activityPermissionDialogRef"
      @add-success="onPermissionMenuAddSuccess"
      @update-success="onPermissionMenuUpdateSuccess"
      @delete="onPermissionMenuDelete"
    />

    <Poster ref="generatePosterRef" />
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, onBeforeUnmount, onMounted, reactive, ref, watchEffect } from 'vue';
import selectMember from '@renderer/components/rk-business-component/select-member/common-add-members.vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { nanoid } from 'nanoid';
import { getIdCardListAxios } from '@/api/outer/api';
import chartGraph from '@/assets/svg/icon_organize.svg';
import { cardIdType } from '@/views/identitycard/data';
import ActivityCollaboratorTypeSelector from './ActivityCollaboratorTypeSelector.vue';
import ActivityCollaboratorRemarkInput
  from '@/views/activity/create/components/professional/ActivityCollaboratorRemarkInput.vue';
import ActivityPermissionDialog from '@/views/activity/create/components/professional/ActivityPermissionDialog.vue';
import Poster from '@/views/activity/manage/promotionStatistics/Poster.vue';
import {
  addCollaborators,
  listCollaborators,
  listCollaboratorTypes,
  listMenu,
  removeCollaborator,
  removeMenu,
  updateCollaborator,
} from '@/api/activity/collaborator';
import { getOpenid } from '@/utils/auth';
import ActivityPermissionSelector from '@/views/activity/create/components/professional/ActivityPermissionSelector.vue';

const emit = defineEmits(['change']);

const activityPermissionDialogRef = ref(null);
const generatePosterRef = ref(null);

const isManage = inject('isManage');
const activityDetailData = inject('activityDetailData');
const activityFormData = inject('activityFormData');
const selectedTeam = inject('selectedTeam');

const isAllowEdit = inject('isAllowEdit');
const hasEditPermission = inject('hasEditPermission');

// 是否是新增数据
const isCreate = computed(() => !activityFormData.id);

// 计算表格高度需要减去的高度，如果在详情管理中，则不需要减去底部操作栏的64
const clientHeightDiff = inject('isInDetail') ? 195 : 259;

// 表格高度
const tableMaxHeight = ref(document.body.clientHeight - clientHeightDiff);

// 筛选参数
const filterParams = reactive({
  collaboratorName: '',
  colTypId: '',
  menuId: '',
});

const table = ref({
  attrs: {
    'row-key': 'cardId',
  },
  list: [],
  columns: [
    {
      title: '姓名',
      colKey: 'collaboratorName',
      width: '15.2%',
      cell: (h, { row }) => <t-tooltip content={row.collaboratorName} placement="top"><div class="inline-block truncate w-full">{row.collaboratorName ?? '--'}</div></t-tooltip>,
    },
    {
      title: '类型',
      colKey: 'collaboratorTypeId',
      width: '20.1%',
      cell: (h, { row, rowIndex }) => ((hasEditPermission && isAllowEdit) ? <ActivityCollaboratorTypeSelector
          modelValue={row.collaboratorTypeId}
          change={(val) => onCollaboratorTypeChange(val, rowIndex)}
          addSuccess={onCollaboratorTypeAddSuccess}
          deleteSuccess={onCollaboratorTypeDeleteSuccess}
          options={getUsedCollaboratorTypes(row)}
      /> : activityFormData.majorSuppliment.collaboratorTypes.find((collaboratorType) => collaboratorType.id === row.collaboratorTypeId)?.typeName ?? '--'),
    },
    {
      title: '权限',
      colKey: 'menuId',
      cell: (h, { row, rowIndex }) => ((hasEditPermission && isAllowEdit) ? <ActivityPermissionSelector
          modelValue={row.menuId}
          change={(val) => onCollaboratorPermissionMenuChange(val, rowIndex) }
      /> : activityFormData.majorSuppliment.menus.find((menu) => menu.id === row.menuId)?.menuName ?? '--'),
    },
    {
      title: '职责',
      colKey: 'remark',
      cell: (h, { row, rowIndex }) => ((hasEditPermission && isAllowEdit) ? <ActivityCollaboratorRemarkInput modelValue={row.remark} change={(val) => onCollaboratorRemarkChange(val, rowIndex) } /> : row.remark ?? '--'),
    },
    {
      title: '操作',
      colKey: 'operate',
      // 推广人在管理侧显示推广海报按钮
      cell: (h, { row }) => (
          <div class="flex gap-8">
            {
                row.menuId && <div class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
                                   onClick={() => reviewCollaboratorPermission(row)}>查看权限
                </div>
            }
            {
              isManage && activityFormData.majorSuppliment.collaboratorTypes.find((collaboratorType) => collaboratorType.id === row.collaboratorTypeId)?.systemType === 'SysTypePromoter'
              && <div class="cursor-pointer text-[#4D5EFF] hover:bg-[#EAECFF] rounded-4 p-4 inline-block"
                   onClick={() => generatePoster(row)}>推广海报
              </div>
            }
            <div class="cursor-pointer text-[#D54941] hover:bg-[#FDF5F6] rounded-4 p-4 inline-block"
                 onClick={() => handleRemoveCollaborator(row)}>删除
            </div>
          </div>
      ),
      isShow: () => isAllowEdit && hasEditPermission,
    },
  ],
});

const filter = computed(() => ({
  attrs: {
    size: 'small',
    labelWidth: '80px',
    placeholder: '搜索姓名',
  },
  advanced: {
    form: {
      list: [
        {
          label: '类型',
          name: 'colTypId',
          value: filterParams.colTypId,
          defaultValue: '',
          type: 'select',
          attrs: {
            placeholder: '请选择类型',
            options: activityFormData.majorSuppliment.collaboratorTypes.map((item) => ({
              label: item.typeName,
              value: item.id,
            })),
          },
        },
        {
          label: '权限',
          name: 'menuId',
          value: filterParams.menuId,
          defaultValue: '',
          type: 'select',
          attrs: {
            placeholder: '请选择权限',
            options: activityFormData.majorSuppliment.menus.map((item) => ({
              label: item.menuName,
              value: item.id,
            })),
          },
        },
      ],
    },
    attrs: {
      attach: '.activity-member-manager-wrapper',
    },
  },
}));

// 已选中成员id列表
const selectedIds = computed(() => activityFormData.members.collaborators.map((collaborator) => collaborator.targetId));

// 选人组件显示状态
const selectMemberVisible = ref(false);

// 用于选人组件自定义成员，在选择外部组织架构成员时使用
const diyData = ref([]);

// 选人组件选择的成员类型
const selectMemberType = ref(null);

// 选人组件禁用列表，当活动已创建时，将选中人员都禁用
const disabledList = computed(() => (isCreate.value ? [] : selectedIds.value));

// 当前选中的活动归属组织下的身份卡id
const selectedTeamCardId = computed(() => [selectedTeam.value.uuid]);

// 选人组件展示的菜单列表
const menus = computed(() => {
  if (selectMemberType.value === 'inner') {
    // 组织身份
    return ['organize', 'platform', 'recent', 'tags'];
  }

  return ['activityExternal'];
});

const addActors = async (type) => {
  selectMemberType.value = type;

  diyData.value = [];

  if (selectMemberType.value === 'outer') {
    const res = await getIdCardListAxios({
      internal_teamId: activityFormData.basic.teamId,
    });

    diyData.value = res?.data?.data
      .filter((cur) => cur.staffOpenId)
      .map((cur) => ({
        id: cur.teamId,
        name: cur.name,
        teamId: cur.internal_teamId,
        team: cur.teamName,
        cardId: cur.cardId,
        targetId: cur.cardId,
        openId: cur.staffOpenId,
        type: `exorganize${cur.teamId}`,
        avatar: cur.avatar,
        icon: chartGraph,
      })) || [];
  }

  selectMemberVisible.value = true;
};

// 确认选中的回调
const onSelectMemberConfirm = async (members) => {
  if (isCreate.value) {
    const selectedMembers = members.map((member) => {
      const existMember = activityFormData.members.collaborators.find((collaborator) => collaborator.openId === member.openId && collaborator.cardId === member.cardId);

      return {
        id: existMember?.id || null, // 已存在的参与人，带上主键
        collaboratorName: member.name,
        targetId: member.targetId,
        cardId: member.teamId ? member.cardId : '',
        teamId: member.teamId,
        openId: member.openId,
        collaboratorTypeId: existMember?.collaboratorTypeId || activityFormData.majorSuppliment.collaboratorTypes.find((collaboratorType) => collaboratorType.systemType === 'SysTypeFieldMan')?.id, // 协作人类型默认为现场工作人员
        menuId: existMember?.menuId || activityFormData.majorSuppliment.menus.find((menu) => menu.isDefault === 1)?.id, // 权限默认为默认权限
        promoterKey: existMember?.promoterKey || null,
        remark: existMember?.remark ?? null,
      };
    });

    if (selectMemberType.value === 'inner') {
      // 清空内部组织架构人员，再在顶部插入
      activityFormData.members.collaborators = activityFormData.members.collaborators.filter((actor) => cardIdType(actor.cardId) === 'outer');
      activityFormData.members.collaborators.unshift(...selectedMembers);
    } else if (selectMemberType.value === 'outer') {
      // 清空外部人员，再在尾部插入
      activityFormData.members.collaborators = activityFormData.members.collaborators.filter((actor) => cardIdType(actor.cardId) !== 'outer');
      activityFormData.members.collaborators.push(...selectedMembers);
    }
  } else {
    // 通过targetId 比较差异得到新添加人员列表
    const newMembers = members.filter(
      (member) => !activityFormData.members.collaborators.some((collaborator) => collaborator.targetId === member.targetId),
    );

    await addCollaborators({
      activityId: activityFormData.id,
      collaborators: newMembers.map((member) => ({
        collaboratorName: member.name,
        targetId: member.targetId,
        cardId: member.teamId ? member.cardId : '',
        teamId: member.teamId,
        openId: member.openId,
        collaboratorTypeId: activityFormData.majorSuppliment.collaboratorTypes.find((collaboratorType) => collaboratorType.systemType === 'SysTypeFieldMan')?.id, // 协作人类型默认为现场工作人员
        menuId: activityFormData.majorSuppliment.menus.find((menu) => menu.isDefault === 1)?.id, // 权限默认为默认权限
        promoterKey: null,
      })),
      me: {
        openId: getOpenid(),
        cardId: selectedTeam.value.uuid,
        teamId: selectedTeam.value.teamId,
      },
    });

    loadData();
  }

  emit('change');
};

// 查看协作人权限
const reviewCollaboratorPermission = (row) => {
  activityPermissionDialogRef.value.reviewPermission(row.menuId);
};

// 生成推广海报
const generatePoster = (row) => {
  generatePosterRef.value?.open({
    detailInfo: activityDetailData.value,
    extend: { name: row.collaboratorName, avatar: row?.collaboratorAvatar, promoterId: row?.id },
  });
};

// 移除协作人
const handleRemoveCollaborator = (row) => {
  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: `确定删除 ${row.collaboratorName}？`,
    theme: 'info',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: async () => {
      if (isCreate.value) {
        activityFormData.members.collaborators = activityFormData.members.collaborators.filter(
          (member) => member.targetId !== row.targetId,
        );
      } else {
        await removeCollaborator({
          activityId: activityFormData.id,
          me: {
            openId: getOpenid(),
            cardId: selectedTeam.value.uuid,
            teamId: selectedTeam.value.teamId,
          },
          collaborator: {
            id: row.id,
          },
        });

        loadData();
      }

      MessagePlugin.success('删除成功');
      confirmDia.destroy();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

// 加载协作人类型
const loadCollaboratorTypes = async () => {
  const res = await listCollaboratorTypes({
    id: isCreate.value ? 0 : activityFormData.id,
    'me.openId': getOpenid(),
    'me.cardId': selectedTeam.value.uuid,
    'me.teamId': selectedTeam.value.teamId,
  });
  activityFormData.majorSuppliment.collaboratorTypes = res.data.data.collaboratorTypes;
};

// 加载权限菜单列表
const loadPermissionMenus = async () => {
  // 如果是新建，则活动id传0
  const res = await listMenu(isCreate.value ? 0 : activityFormData.id);
  activityFormData.majorSuppliment.menus = res.data.data.menus;
};

const loadData = async () => {
  const params = {
    activityId: activityFormData.id,
    menuId: filterParams.menuId || null,
    colTypId: filterParams.colTypId || null,
    keyword: filterParams.collaboratorName ?? null,
    'me.openId': getOpenid(),
    'me.cardId': selectedTeam.value.uuid,
    'me.teamId': selectedTeam.value.teamId,
  };
  const res = await listCollaborators(params);

  activityFormData.members.collaborators = res.data.data.collaborators.map((collaborator) => ({
    ...collaborator,
    collaboratorTypeId: collaborator.collaboratorTypeId || null,
    menuId: collaborator.menuId || null,
    targetId: collaborator.cardId || collaborator.openId,
  }));
};

const updateData = async (row) => {
  await updateCollaborator({
    activityId: activityFormData.id,
    collaborator: row,
    me: {
      openId: getOpenid(),
      cardId: selectedTeam.value.uuid,
      teamId: selectedTeam.value.teamId,
    },
  });
};

// 协作人类型添加成功回调
const onCollaboratorTypeAddSuccess = (collaboratorType) => {
  activityFormData.majorSuppliment.collaboratorTypes.push(collaboratorType);
};

const onCollaboratorTypeDeleteSuccess = ({ id }) => {
  activityFormData.majorSuppliment.collaboratorTypes = activityFormData.majorSuppliment.collaboratorTypes.filter((collaboratorType) => collaboratorType.id !== id);
};

// 新建权限回调
const onPermissionMenuAddSuccess = (menu) => {
  activityFormData.majorSuppliment.menus.push(menu);
};

// 编辑权限回调
const onPermissionMenuUpdateSuccess = (menu) => {
  const index = activityFormData.majorSuppliment.menus.findIndex((item) => item.id === menu.id);
  activityFormData.majorSuppliment.menus.splice(index, 1, menu);
};

// 删除权限回调
const onPermissionMenuDelete = ({ menu, index }) => {
  // 权限已绑定协作人则无法删除
  const isSelected = activityFormData.members.collaborators.some((collaborator) => collaborator.menuId === menu.id);

  if (isSelected) {
    const alertDia = DialogPlugin.alert({
      header: '提示',
      body: '当前权限已被绑定，请前往 【人员管理-协作人】 解除绑定后删除',
      theme: 'info',
      confirmBtn: '确定',
      onConfirm: async () => {
        alertDia.hide();
      },
      onClose: () => {
        alertDia.hide();
      },
    });
    return;
  }

  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: `确定删除 ${menu.menuName}`,
    theme: 'info',
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      await removeMenu({
        menu: {
          id: menu.id,
        },
        activityId: isCreate.value ? 0 : activityFormData.id,
        me: {
          openId: getOpenid(),
          cardId: selectedTeam.value.uuid,
          teamId: selectedTeam.value.teamId,
        },
      });

      MessagePlugin.success('删除成功');

      activityFormData.majorSuppliment.menus.splice(index, 1);

      confirmDia.destroy();
    },
    onCancel: () => {
      confirmDia.destroy();
    },
    onCloseBtnClick: () => {
      confirmDia.destroy();
    },
  });
};

// 打开权限弹窗
const openActivityPermissionDialog = () => {
  activityPermissionDialogRef.value.open();
};

// 选择协作人类型回调
const onCollaboratorTypeChange = (val, rowIndex) => {
  const row = activityFormData.members.collaborators[rowIndex];
  row.collaboratorTypeId = val;

  // 如果选择类型为推广人，则为其添加唯一标识，否则清空标识
  const selectedCollaboratorType = activityFormData.majorSuppliment.collaboratorTypes.find((item) => item.id === val);

  if (selectedCollaboratorType?.systemType === 'SysTypePromoter' && !row.promoterKey) {
    row.promoterKey = nanoid();
  } else {
    row.promoterKey = null;
  }

  if (!isCreate.value) {
    updateData(row);
  }
};

// 选择协作人权限回调
const onCollaboratorPermissionMenuChange = (val, rowIndex) => {
  const row = activityFormData.members.collaborators[rowIndex];
  row.menuId = val;

  if (!isCreate.value) {
    updateData(row);
  }
};

// 编辑协作人职责回调
const onCollaboratorRemarkChange = (val, rowIndex) => {
  const row = activityFormData.members.collaborators[rowIndex];
  row.remark = val;

  if (!isCreate.value) {
    updateData(row);
  }
};

const onTableChange = ({ filter }) => {
  filterParams.collaboratorName = filter?.searchVal;
  filterParams.colTypId = filter?.colTypId;
  filterParams.menuId = filter?.menuId;

  if (!isCreate.value) {
    loadData();
  }
};

// 获取可选择的协作人类型
const getUsedCollaboratorTypes = (row) => {
  // 内部人员不显示外部联系人选项，外部人员不显示内部联系人选项
  if (cardIdType(row.cardId) === 'outer') {
    return activityFormData.majorSuppliment.collaboratorTypes.filter((item) => item.systemType !== 'SysTypeInnerContact');
  } if (cardIdType(row.cardId) === 'inner') {
    return activityFormData.majorSuppliment.collaboratorTypes.filter((item) => item.systemType !== 'SysTypeOuterContact');
  }
  return activityFormData.majorSuppliment.collaboratorTypes;
};

watchEffect(() => {
  // 如果是新建数据，则筛选条件前端过滤
  let list = activityFormData.members.collaborators;

  if (isCreate.value) {
    if (filterParams.collaboratorName !== '') {
      list = list.filter((item) => item.collaboratorName.includes(filterParams.collaboratorName));
    }
    if (filterParams.colTypId) {
      list = list.filter((item) => item.collaboratorTypeId === filterParams.colTypId);
    }
    if (filterParams.menuId) {
      list = list.filter((item) => item.menuId === filterParams.menuId);
    }
  }

  table.value.list = list;
});

// 重置表格最大高度
const resizeTableMaxHeight = () => {
  tableMaxHeight.value = document.body.clientHeight - clientHeightDiff;
};

onMounted(() => {
  loadCollaboratorTypes();
  loadPermissionMenus();

  if (!isCreate.value) {
    // 当非新建时，加载表格数据
    loadData();
  }

  window.addEventListener('resize', resizeTableMaxHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeTableMaxHeight);
});

defineExpose({
  openActivityPermissionDialog,
});
</script>

<style lang="less">
.activity-add-staffs-dropdown {
  .t-popup__content {
    padding: 4px;

    .t-dropdown__item {
      padding: 4px 8px;
      width: 120px;
    }
  }
}

:deep(.advanced-form) {
  .t-form__label {
    float: none;
    width: 0!important;
  }
  .t-form__controls {
    margin-left: 0!important;
  }
}
</style>

<style lang="less" scoped>
:deep(.RTable){
  .header{
    padding-bottom: 16px;
  }

  .toolbar-wrap{
    display: flex;
    justify-content: space-between;
    padding-top: 0;
    padding-left: 24px;
    padding-right: 24px;

    .toolbar-box{
      flex:1;
      align-items: center;
      text-align: left;
    }
  }

  .table-wrap{
    padding: 0 24px;
    height: calc(var(--scroll-height) - 56px);
    overflow: auto;
  }
}

:deep(.t-table){
  td:last-child{
    padding: 8px !important;
  }
}
</style>
