<template>
  <div class="refund-container">
    <!-- 引入自定义的iframe组件 -->
    <iframe-component
      v-if="iframeUrl && iframeOnlyId"
      :key="iframeOnlyId"
      :id="`${iframeOnlyId}`"
      ref="iframeRef"
      :url="iframeUrl"
      :is-load-in-component="false"
      @load="handleLoad"
      @error="handleError"
      @message="handleMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import md5 from 'md5';
const route = useRoute();
import LynkerSDK from '@renderer/_jssdk';
import IframeComponent from '@renderer/_jssdk/components/iframe/index.vue';

const iframeUrl = ref();
const iframeOnlyId = ref();
const loading = ref(true);
const loadError = ref(false);
const iframeRef = ref<{ postMessage: (data: any) => void }>(null);

const handleLoad = () => {
  loading.value = false;
};

const handleError = () => {
  loadError.value = true;
};

const handleMessage = (data: any) => {
  console.log('=====>data', data);
  if (data?.type === 'work-bench-open-tab') {
    const { path_uuid, url, title, icon } = data.data;
    const id = md5(`${url}`);
    LynkerSDK.workBench.openTab({
      path_uuid,
      icon: icon || "government",
      title: title,
      pathName: 'work_bench_iframe',
      fullPath: "/workBenchIndex/work_bench_iframe/" + id,
      query: {
        url: encodeURIComponent(url),
      },
    });
  } else if(data?.type === 'work-bench-open-tab-for-receive-detail'){
    const { url, title } = data.data;
    LynkerSDK.workBench.openTabForReceiveDetail({
      title,
      url: encodeURIComponent(url),
    });
  }
};

const loadIframe = () => {
  console.log('=====>loadIframe');
  // const token = LynkerSDK.config.token;
  // const teamId = LynkerSDK.workBench.getActiveTeamId();
  // const userInfo = LynkerSDK.getUserInfo();
  // const env = LynkerSDK.config.env;
  // 有时url会在route.url中
  // @ts-ignore
  iframeUrl.value = decodeURIComponent(`${route.query.url || route.url}`);
  iframeOnlyId.value = route.params.iframeOnlyId;
}

watch(route, () => {
  console.log('=====>route.query 11111', route.query);
  // 有时url会在route.url中
  // @ts-ignore
  if (route.url || route.query.url) {
    loadIframe();
  }
});

onMounted(() => {
  loadIframe();
});
</script>

<style scoped>
.refund-container {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
}
</style>
