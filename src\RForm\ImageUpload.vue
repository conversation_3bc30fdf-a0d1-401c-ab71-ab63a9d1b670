<template>
  <div label="产品主图">
    <div class="upload-container">
      <label class="upload-box">
        <div class="upload-content">
          <div v-html="uploadIcon"></div>
          <span class="upload-text">点击上传</span>
        </div>
        <t-upload class="image-file" :value="imageFile" @select-change="handleImageUpload" />
      </label>
    </div>
    <p class="upload-guidelines">
      <span>温馨提示：</span><br />
      <span>1、建议上传图片尺寸640*640，大小不超过2M</span><br />
      <span>2、支持格式： jpg/jpeg/png/bmp/webp</span>
    </p>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits } from "vue";
import { Upload as TUpload } from 'tdesign-vue-next';
const uploadIcon = `<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="5.60039" y="6.40039" width="20.8" height="19.2" rx="3.2" stroke="#828DA5" stroke-width="1.5" stroke-linecap="round"></rect>
<path d="M14.0948 11.1994C14.0948 12.2598 13.1635 13.1194 12.0148 13.1194C10.866 13.1194 9.93477 12.2598 9.93477 11.1994C9.93477 10.139 10.866 9.27941 12.0148 9.27941C13.1635 9.27941 14.0948 10.139 14.0948 11.1994Z" fill="#828DA5"></path>
<path d="M6.50078 23.001L18.4282 14.3711C19.6841 13.4624 21.4107 13.5846 22.5262 14.661L26.4014 18.4008" stroke="#828DA5" stroke-width="1.5" stroke-linecap="round"></path>
</svg>`;

const imageFile = ref(null);

const emit = defineEmits<{
  (e: "update:image", file: File): void;
  (e: "change", file: File): void;
}>();

const handleImageUpload = (files, context) => {
  console.log('handleImageUpload', files, context);
  emit("update:image", files);
  emit("change", files);
};
</script>

<style scoped>
.upload-container {
  display: flex;
  width: 78px;
  height: 78px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid #d5dbe4;
  background-color: #fff;
}

.image-file {
  height: 0;
  width: 0;
  visibility: hidden;
}

.upload-box {
  cursor: pointer;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.upload-text {
  color: #516082;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-weight: 400;
}

.hidden-input {
  display: none;
}

.upload-guidelines {
  color: #acb3c0;
  font-family: "PingFang SC";
  font-size: 12px;
  font-weight: 400;
}

@media (max-width: 991px) {
  .upload-container {
    width: 70px;
    height: 70px;
  }
  .upload-text {
    font-size: 11px;
  }
  .upload-guidelines {
    font-size: 11px;
  }
}

@media (max-width: 640px) {
  .upload-container {
    width: 64px;
    height: 64px;
  }
  .upload-text {
    font-size: 10px;
  }
  .upload-guidelines {
    font-size: 10px;
  }
}
</style>
