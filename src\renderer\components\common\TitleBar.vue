<template>
  <div class="win-title">
    <div class="flex flex-row gap-[10px] items-center" :style="`min-width: 138px;padding-left: ${platform === 'darwin' ? 70 : 20}px;`">
      <avatar
        class="window-avatar"
        :image-url="imageUrl"
        :user-name="userName"
        shape="circle"
        @mouseup.stop="showDialog('personal')"
      />
      <div v-if="LynkerSDK.checkIsManualEnv()" :class="['env-info', { 'env-info-win32': platform === 'win32' }]">
        {{ LynkerSDK.config.env }}
        <span>(自动更新已被禁用，恢复默认设置后可重新开启)</span>
      </div>
    </div>
    <div class="win-title-center">
      <div class="app-title-search" @mouseup.stop="showDialog('dialogSearch')">
        <t-icon name="search" />
        {{ t('im.public.search') }}
      </div>
      <div v-if="appStore.updatePack && appStore.updatePack.show" class="update-box">
        <div v-if="appStore.updatePack.status === 'newPack'" class="update-install" @click="handelUpdate('newPack')">
          <div class="text">软件更新</div>
        </div>
        <div v-else="appStore.updatePack.status === 'ing'" :class="{'update-ing':true, 'active': !activeBackDownLoad}" @click="handelUpdate('ing')">
          <div class="percent">
            {{t('zx.setting.backInstall')}} {{appStore.updatePack.percent}}%
          </div>
        </div>
      </div>
    </div>
    <!-- windows 自定义关闭、最大化、最小化按钮 -->
    <div class="app-title-controls">
      <t-tooltip v-if="showtTeam && !commonStore.allTeamData.length" :show-arrow="false" placement="bottom" :content="t('contacts.createOrgTip')">
        <div class="package" @click="goCreatOrganization">
        </div>
      </t-tooltip>
      <div class="btn-top ringing-btn" @mouseup.stop="changeRinging">
        <!-- <svg-icon v-if="ringing" name="icon_remind" class='btn-top-icon'/>
        <svg-icon v-else name="icon_close_remind" class='btn-top-icon'/> -->
        <div v-if="ringing" class="i-svg:icon_remind btn-top-icon" />
        <div v-else class="i-svg:icon_close_remind btn-top-icon" />
        <div class="tips">
          <div>{{ ringing ? '点击可关闭新消息铃声' :'点击可打开新消息铃声' }}</div>
        </div>
      </div>
      <div class="btn-top cart-entry" @click="openCart">
        <!-- <svg-icon name="icon_cart" class='btn-top-icon' /> -->
        <i class="i-svg:icon_cart btn-top-icon" />
        <div class="cart-number" v-show="cartNumber">
          {{ Number(cartNumber) > 99 ? '99+' : cartNumber }}
         </div>
      </div>
      <!-- <t-tooltip :content="t('im.public.addContact4')" placement="bottom"> -->
        <div class="btn-top" @mouseup.stop="showDialog('add')">
          <!-- <svg-icon name="icon_add_top" class='btn-top-icon'/> -->
          <i class="i-svg:icon_add_top btn-top-icon" />
        </div>
      <!-- </t-tooltip> -->

      <template v-if="platform === 'win32'">
        <div class="border-line"></div>
        <div class="no-hide-bv windows-icon-bg" @click="Mini">
          <img src="@renderer/assets/icons/svg/mini_new.svg" class="icon-size" />
        </div>
        <div class="no-hide-bv windows-icon-bg" @click="MixOrReduction">
          <img src="@renderer/assets/icons/svg/mix_new.svg" class="icon-size" />
        </div>
        <div class="no-hide-bv windows-icon-bg close-icon" @click="Close">
          <img src="@renderer/assets/icons/svg/close_new.svg" class="icon-size" />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { showDialog } from "@renderer/utils/DialogBV";
import { removeAppTeamId, getProfilesInfo, setProfilesInfo, getIsTeamShow, setIsTeamShow, getShouldRinging, setShouldRinging } from "@renderer/utils/auth";
import { useI18n } from "vue-i18n";
import { getCommonTeams } from "@renderer/api/workBench";
import { useCommonStore } from '@renderer/store/modules/common';
import SvgIcon from '@/components/SvgIcon.vue';
import avatar from "../kyy-avatar/index.vue";
import { useAppStore } from '@renderer/store/modules/appStore';
import LynkerSDK from '@renderer/_jssdk';
import { getProfile } from "@renderer/api/account";
import { getAccesstoken, getOpenid } from '@renderer/utils/auth';
import { getCartStats } from "@renderer/api/shop";

const activeBackDownLoad = ref(false);
const { t } = useI18n();
const commonStore = useCommonStore();

const platform = ref(process.platform);
const ringing = ref(getShouldRinging())
const appStore = useAppStore();

const imageUrl = ref('');
const userName = ref('');
const cartNumber = ref(0);

watch(() => appStore.updatePack, (val) => {
  if (val) {
    activeBackDownLoad.value = val.isBack;
  }
})

LynkerSDK.ipcRenderer.on('refresh-profiles-info', (event, arg) => {
  imageUrl.value = arg?.avatar || getProfilesInfo()?.avatar;
  userName.value = arg?.title || getProfilesInfo()?.title;
  setProfilesInfo({
    ...getProfilesInfo(),
    avatar: imageUrl.value,
    title: userName.value
  });
});
const showHover = ref(false);
LynkerSDK.ipcRenderer.on('showAddHover', (e, val) => {
  showHover.value = val.type === 'add';
});

LynkerSDK.ipcRenderer.on('checkTeams', () => {
  checkTeams();
});

const Mini = () => {
  LynkerSDK.ipcRenderer.invoke("windows-mini");
};

const MixOrReduction = () => {
  LynkerSDK.ipcRenderer.invoke("window-max");
};

const Close = () => {
  LynkerSDK.ipcRenderer.invoke("window-close");
  removeAppTeamId();
};

const goCreatOrganization = () => {
  const isTeamShow = JSON.parse(getIsTeamShow() || 'false');

  LynkerSDK.ipcRenderer.invoke('all-dialog-org', { show: !isTeamShow, type: 'teams' });
  setIsTeamShow(!isTeamShow);
};
const showtTeam = ref(false);
const checkTeams = async () => {
  const teamRes = await getCommonTeams();
  if (teamRes.data?.code === 0 && teamRes.data?.data?.length) {
    commonStore.setKeyValue('allTeamData', teamRes.data.data);
    return;
  }
  if (teamRes.data?.code === 0 && !teamRes.data?.data?.length) {
    showtTeam.value = true;
  }
};
const changeRinging = () => {
  ringing.value = !ringing.value
  setShouldRinging(ringing.value)
  LynkerSDK.ipcRenderer.invoke('ringingChange',{from:'main'});
}
LynkerSDK.ipcRenderer.on('ringingChange', (e, val) => {
  ringing.value = getShouldRinging();
});
// 点击更新
const handelUpdate = (status) => {
  console.log('handelUpdate', status)
  if (status === 'newPack') {
    LynkerSDK.ipcRenderer.invoke('check-update', { origin: 'updatePack' });
  } else if(status === 'ing') {
    LynkerSDK.ipcRenderer.invoke('pack-back-install', { isBack: false });
  }
}

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await getProfile();
    if (res.status === 200) {
      console.log('getUserInfo', res.data);
      setProfilesInfo(res.data);
      imageUrl.value = res.data?.avatar;
      userName.value = res.data?.title;
    }
  } catch (error) {
    console.error('getUserInfo', error);
  }
}
let timer = null;
LynkerSDK.ipc.handleRenderer('cart-number', (opt) => {
  clearTimeout(timer);
  timer = setTimeout(() => {
    checkCartNumber(opt);
  }, 200);
});

const checkCartNumber = async (opt) => {
  if (!opt) return;
  if (opt && typeof opt.number !== 'undefined') {
    cartNumber.value = opt.number;
  }

  if (!opt?.check) return;
  const res = await getCartStats();
  const data = res?.data || {};
  if (data?.code === 0) {
    const num = data.data?.total || 0;
    if (num !== cartNumber.value) {
      cartNumber.value = num;
    }
  } else {
    cartNumber.value = 0;
  }
};

const openCart = async () => {
  const url = LynkerSDK.getH5UrlWithParams(`/shop/index.html#cart`, { token: `${getAccesstoken()}` });
  console.log( 'openCart', url);

  await LynkerSDK.windowsTabs.openTab({
      tabsId: '购物车',
      tabsTitle: '购物车',
      options: {
        title: `购物车`,
        id: '购物车',
        // url: 'http://localhost:8080/shop/#/cart',
        icon: 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/shop/shop_tab_icon.png',
        url: url,
        // beforeCloseOptions: {
        //   title: '是否关闭',
        //   content: '111111111',
        // },
      },
  })
};

onMounted(() => {
  checkTeams();
  // 修复异常情况下，本地用户信息未更新
  // https://www.tapd.cn/tapd_fe/69781318/bug/detail/1169781318001054796
  getUserInfo();

  checkCartNumber({ check: true });
});
</script>

<style lang="less" scoped>
.win-title {
  //width: calc(100% - 20px);
  height: 48px;
  line-height: 48px;
  //background-color: #2069e3;
  // border-top-left-radius: 9px;
  // border-top-right-radius: 9px;
  background: linear-gradient(0deg, var(--bg-kyy-color-bg-software-foucs, #272B4F) 0%, var(--bg-kyy-color-bg-software-foucs, #272B4F) 100%), var(--bg-kyy-color-bg-software-lose-foucs, rgba(20, 26, 66, 0.80));
  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-app-region: drag;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.win-title-center {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}


.app-title-controls {
  -webkit-app-region: no-drag;
  user-select: none;

  flex-basis: 200px;
  flex-grow: 0;
  flex-shrink: 0;

  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  .package {
    width: 44px;
    height: 28px;
    border-radius: 4px;
    background: url('@renderer/assets/identity/org_upgrade.svg');
    margin-right: 16px;
    cursor: pointer;
    &:hover {
      background: url('@renderer/assets/identity/org_upgrade_hover.svg');
    }
  }
  .windows-icon-bg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    color: rgba(129, 129, 129, 0.6);
    cursor: pointer;

    .icon-size {
      width: 20px;
      height: 20px;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
      -webkit-user-drag: none;
    }
  }

  .windows-icon-bg:hover {
    background-color: rgba(182, 182, 182, 0.2);
  }

  .close-icon:hover {
    background-color: rgba(232, 17, 35, 0.9);
    color: #fff;
  }
}

.window-avatar {
  user-select: none;
  -webkit-app-region: no-drag;
  cursor: pointer;
}

.btn-top {
  //background-color: @kyy_brand_5;
  display: flex;
  padding: 0px 8px;
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_software_item_hover, rgba(255, 255, 255, 0.16));
  margin-right: 16px;
  height: 28px;
  justify-content: center;
  align-items: center;

  &.cart-entry {
    position: relative;
    background: var(--bg-kyy_color_bg_software_item_hover, rgba(255, 255, 255, 0.16));
    width: 48px;
    height: 28px;
    padding: 0;
    cursor: pointer;
    .cart-number {
      position: absolute;
      top: -4px;
      right: -4px;
      min-width: 16px;
      height: 16px;
      padding: 0 4px;
      border-radius: var(--kyy_radius_badge_full, 999px);
      background-color: var(--kyy_color_badge_bg, #FF4AA1);
      color: var(--text-kyy_color_text_5, #FFF);
      font-size: 12px;
      line-height: 16px;
      text-align: center;
    }
  }

}
.btn-top:hover{
  background: var(--brand-kyy_color_brand_hover, #707EFF);
}
.btn-top-icon{
  // width: 28px;
  // height:28px;
  font-size: 28px;
  color:#D5DBE4;
}
.ringing-btn{
  position: relative;
  .tips{
    display: none;
    position: absolute;
    left: -146px;
    top:1px;
    padding:4px 8px;
    color: var(--kyy_color_toopltip_text, #FFF);
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
    border-radius: var(--kyy_radius_toopltip, 6px);
    background: var(--bg-kyy_color_bg_software_item_hover, rgba(255, 255, 255, 0.16));
    &::after {
      content: "";
      position: absolute;
      top: 50%; /* Arrow pointing downwards */
      right: -9px;
      margin-top: -5px;
      border-width: 5px;
      border-style: solid;
      border-color:  transparent transparent transparent rgba(255, 255, 255, 0.16);
    }
  }
  &:hover .tips{
    display: inline-block
  }
}
.border-line{
  width: 1px;
  height: 18px;
  background: var(--bg-kyy-color-bg-software-item-hover, rgba(255, 255, 255, 0.16));
  margin-right: 16px;
}

.app-im-menu_add {
  height: 16px;
  width: 16px;
  color: @kyy_brand_6;
  margin-right: 8px;
}

.app-im-menu_item {
  display: flex;
  flex-direction: row;
  align-items: center;
  line-height: 22px;
  font-size: 14px;
}

.app-title-search {
  -webkit-app-region: no-drag;
  width: 320px;
  height: 28px;
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-software-item-hover, rgba(255, 255, 255, 0.16));
  //border: 1px solid #e3e6eb;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  user-select: none;
  gap: 4px;

  color: var(--text-kyy-color-text-5, #ACB3C0);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  &:hover {
    border-color: #c9c9c9;
  }
}
.update-box{
  -webkit-app-region: no-drag;
  margin-left: 12px;
  width: 100px;
  .update-install{
    height: 28px;
    width: 28px;
    border-radius: 4px;
    background: url('@renderer/assets/svg/common/icon_version.svg') no-repeat center center / 20px;
    background-color: var(--bg-kyy_color_bg_software_item_hover, rgba(255, 255, 255, 0.16));
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    line-height: 18px;
    padding: 4px;
    > .text {
      display: none;
    }
    &:hover {
      background-color: var(--brand-kyy_color_brand_hover, #707EFF);
      width: 88px;
      background-position-x: 4px;
      > .text {
        display: inline-block;
        padding-left: 22px;
        vertical-align: middle;
        color: #fff;
      }
    }
    // &::after{
    //   position: absolute;
    //   right: -2px;
    //   top: -2px;
    //   content: '';
    //   width: 6px;
    //   height: 6px;
    //   border-radius: var(--kyy_radius_badge_full, 999px);
    //   border: 1px solid var(--kyy_color_badge_border, #FFF);
    //   background: var(--kyy_color_badge_bg, #FF4AA1);
    // }
  }
  .update-ing{
    height: 30px;
    padding: 4px;
    background: url('@renderer/assets/svg/common/icon_computer.svg') no-repeat 6px center / 18px;
    border-radius: 4px;
    background-color: var(--bg-kyy_color_bg_software_item_hover, rgba(255, 255, 255, 0.16));
    cursor: pointer;
    width: max-content;
    &:hover {
      background-color: var(--brand-kyy_color_brand_hover, #707EFF);
    }
    &.active{
      background-color: var(--brand-kyy_color_brand_hover, #3E4CD1);
    }
    .percent{
      margin-left: 28px;
      vertical-align: middle;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      color: #fff;
    }
  }
}
.env-info {
  pointer-events: none;
  background-color: rgba(255, 0, 0, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;
  height: 28px;
  line-height: 28px;
  position: absolute;
  left: 110px;
  top: 10px;
  z-index: 3;
  &.env-info-win32 {
    left: 70px;
  }
}
</style>
