import { JSBridgeBase } from './base';
import type { JsBridgeHandler } from './hooks/useJsBridge';
import { useJsBridge as useJsBridgeHook } from './hooks/useJsBridge';
import { BusinessModule } from './modules/business';

export class <PERSON><PERSON>ridge extends JSBridgeBase {
  protected static override instance: JSBridge | null = null;
  public readonly business: BusinessModule;
  public readonly useJsBridge: (handler: JsBridgeHandler) => () => void;

  private constructor() {
    super();
    this.business = new BusinessModule();
    this.useJsBridge = useJsBridgeHook;
  }

  public static getInstance(): JSBridge {
    if (!JSBridge.instance) {
      JSBridge.instance = new JSBridge();
    }
    return JSBridge.instance;
  }

  public static resetInstance(): void {
    JSBridge.instance = null;
  }

  public getEnv() {
    return super.getEnv();
  }
}

// 导出单例实例和类
export const jsbridge = JSBridge.getInstance();
export const RKJsbridge = jsbridge;
export default JSBridge;
