import lodash from 'lodash';
import { getConfig } from '../utils';

type ApiModule =
  | 'iam-srv'
  | 'im-sync'
  | 'client-organize'
  | 'organize'
  | 'square'
  | 'know-srv'
  | 'business'
  | 'order'
  | 'square-operation-manage'
  | 'portal'
  | 'tools'
  | 'global'
  | 'h5'
  | 'org-web'
  | 'activities'
  | 'website'
  | 'families';

type ApiConfig = {
  [key in ApiModule]: string;
};

export const getApiConfig = (env: string): ApiConfig => {
  const _env = `${getConfig().env}` as 'DEV' | 'TEST' | 'PRE' | 'PROD';
  if (`${getConfig().env}`) {
    env = _env.toLocaleLowerCase();
  }

  console.log('env==', env);
  const config = {
    dev: {
      organize: 'https://dev.ringkol.com/organize/api',
      'client-organize': 'https://dev.ringkol.com/client/api',
      'im-sync': 'https://dev.ringkol.com/im/api',
      'iam-srv': 'https://dev.ringkol.com/iam/api',
      // "iam-srv": "http://192.168.31.160:8000",
      square: 'https://dev.ringkol.com/square/api',
      portal: 'https://dev.ringkol.com/portal/api',
      'square-operation-manage':
        'https://dev.ringkol.com/square-operation-manage/api',
      'know-srv': 'https://dev.ringkol.com/know/api',
      business: 'https://dev.ringkol.com/business/api',
      order: 'https://dev.ringkol.com/order/api',
      tools: 'https://dev.ringkol.com/tools/api',
      global: 'https://dev.ringkol.com/global/api',
      h5: 'https://operation-manage-square-dev.ringkol.com',
      'org-web': 'https://organize-manage-dev.ringkol.com',
      activities: 'https://dev.ringkol.com/activities/api',
      website: 'https://dev.ringkol.com',
      // 排行api
      families: 'https://dev.ringkol.com/families/api',
    },
    test: {
      organize: 'https://qa.ringkol.com/organize/api',
      'client-organize': 'https://qa.ringkol.com/client/api',
      'im-sync': 'https://qa.ringkol.com/im/api',
      'iam-srv': 'https://qa.ringkol.com/iam/api',
      // "iam-srv": "http://192.168.31.160:8000",
      square: 'https://qa.ringkol.com/square/api',
      portal: 'https://qa.ringkol.com/portal/api',
      'square-operation-manage':
        'https://qa.ringkol.com/square-operation-manage/api',
      'know-srv': 'https://qa.ringkol.com/know/api',
      business: 'https://qa.ringkol.com/business/api',
      order: 'https://qa.ringkol.com/order/api',
      tools: 'https://qa.ringkol.com/tools/api',
      global: 'https://qa.ringkol.com/global/api',
      h5: 'https://operation-manage-square-qa.ringkol.com',
      'org-web': 'https://organize-manage-qa.ringkol.com',
      activities: 'https://qa.ringkol.com/activities/api',
      website: 'https://qa.ringkol.com',
      // 排行api
      families: 'https://qa.ringkol.com/families/api',
      WS_URL: 'wss://im-qa.ringkol.com/msg_gateway',
      API_URL: 'https://im-qa.ringkol.com/api',
    },
    pre: {
      organize: 'https://pre.ringkol.com/organize/api',
      'client-organize': 'https://pre.ringkol.com/client/api',
      'im-sync': 'https://pre.ringkol.com/im/api',
      'iam-srv': 'https://pre.ringkol.com/iam/api',
      square: 'https://pre.ringkol.com/square/api',
      portal: 'https://pre.ringkol.com/portal/api',
      'square-operation-manage':
        'https://pre.ringkol.com/square-operation-manage/api',
      'know-srv': 'https://pre.ringkol.com/know/api',
      business: 'https://pre.ringkol.com/business/api',
      order: 'https://pre.ringkol.com/order/api',
      tools: 'https://pre.ringkol.com/tools/api',
      global: 'https://pre.ringkol.com/global/api',
      h5: 'https://operation-manage-square-pre.ringkol.com',
      'org-web': 'https://organize-manage-pre.ringkol.com',
      activities: 'https://pre.ringkol.com/activities/api',
      website: 'https://pre.ringkol.com',
      // 排行api
      families: 'https://pre.ringkol.com/families/api',
    },
    prod: {
      organize: 'https://ringkol.com/organize/api',
      'client-organize': 'https://ringkol.com/client/api',
      'im-sync': 'https://ringkol.com/im/api',
      'iam-srv': 'https://ringkol.com/iam/api',
      square: 'https://ringkol.com/square/api',
      portal: 'https://ringkol.com/portal/api',
      'square-operation-manage':
        'https://ringkol.com/square-operation-manage/api',
      'know-srv': 'https://ringkol.com/know/api',
      business: 'https://ringkol.com/business/api',
      order: 'https://ringkol.com/order/api',
      tools: 'https://ringkol.com/tools/api',
      global: 'https://ringkol.com/global/api',
      h5: 'https://operation-manage-square.ringkol.com',
      'org-web': 'https://organize-manage.ringkol.com',
      activities: 'https://ringkol.com/activities/api',
      website: 'https://ringkol.com',
      // 排行api
      families: 'https://ringkol.com/families/api',
    },
  };

  if (lodash.get(config, `${env}`, undefined)) {
    return lodash.get(config, `${env}`, undefined);
  }

  // if (env === 'PRE') {
  //   // 先用用正式环境的后面预发环境没问题再切过来
  //   return config.prod;
  // }
  // if (env === 'PROD') {
  //   return config.prod;
  // }

  // 默认开发环境
  return config.prod;
};
