<template>
  <!-- :select-list="selectList" -->
  <selectMemberSimple
    v-if="routerQuery.simple"
    :visible="true"
    @confirm="selectMemberConfirm"
    @close="selectMemberConfirm"
    v-bind="routerQuery"
  />
  <selectMember
    v-else
    :visible="true"
    showMyGroupMenu
    :menu="['recent', 'friend', 'orgcontacts', 'groups', 'organize']"
    :change-menus="true"
    @confirm="selectMemberConfirm"
    @close="selectMemberConfirm"
    v-bind="routerQuery"
  />
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import lodash from 'lodash';
import { useRoute } from 'vue-router';
import sdk from '@lynker-desktop/electron-sdk/renderer';
import selectMember from "@renderer/components/rk-business-component/select-member/common-add-members.vue";
import selectMemberSimple from "@renderer/components/rk-business-component/select-member/simple-add-member.vue";
const route = useRoute();
const routerQuery = computed(() => {
  try {
    const obj = lodash.omitBy(
      JSON.parse(decodeURIComponent(route?.query?.options as string)),
      (v) => v === null || v === undefined || v === ''
    );
    return {
      ...obj,
    };
  } catch (error) {
    console.error(error);
  }
  return {};
});

onMounted(() => {
  // fix routerView 有背景色
  let style = document.createElement('style');
  style.innerHTML = `
    .routerView {
      background: transparent!important;
      background-color: transparent!important;
    }
  `;
  // 将 style 标签添加到页面的 head 中
  document.head.appendChild(style);
});

const selectMemberConfirm = async (val) => {
  /**
   * 参数
   * callbackId: string;
   * json字符串
   */
  let callbackId = '';
  try {
    const obj = JSON.parse(decodeURIComponent(route?.query?.data as string));
    console.log(val, obj);
    callbackId = obj.callbackId;
  } catch (error) {
    console.error(error);
  }
  sdk.ipc.invokeRenderer(callbackId, JSON.stringify(val || []));
};
console.log("selectMember props", {...routerQuery.value});

</script>
<style lang="less" scoped>

</style>
