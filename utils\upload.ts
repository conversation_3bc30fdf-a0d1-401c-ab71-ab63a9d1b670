import OSS from 'ali-oss';
import pLimit from 'p-limit';
import { MessagePlugin, UploadFile } from 'tdesign-vue-next';
import { v5 as uuidv5 } from 'uuid';
import { getStsToken } from '../axios/api';
import { OSS_BUCKET, OSS_DEFAULT_UPLOAD_DIR, OSS_REGION } from '../constants';
import { replaceUrlDomain } from './cdn';

const defaultUploadOptions = { rename: true, rootDir: OSS_DEFAULT_UPLOAD_DIR };

/**
 * 上传文件到OSS服务
 * @param files - 单个或多个文件
 * @param options - 上传选项
 * @returns 上传结果（包含URL等信息）
 */
export async function uploadToOSS(
  files: UploadFile | UploadFile[] | File,
  options = defaultUploadOptions,
): Promise<any> {
  // 获取 STS Token
  const stsRes = await getStsToken();
  if (!stsRes || typeof stsRes !== 'object' || !('data' in stsRes)) {
    throw new Error('Invalid STS token response');
  }

  // 初始化 OSS 客户端
  const ossClient = initOSSClient(stsRes);

  // 处理多个文件
  if (Array.isArray(files)) {
    return uploadFilesBatch(ossClient, files, options);
  }

  return uploadFile(ossClient, files, options);
}

/**
 * 初始化OSS客户端
 * @param stsRes - STS响应数据
 * @returns OSS客户端实例
 */
function initOSSClient(stsRes: any): OSS {
  const accessParams = getAccessParams(stsRes);

  return new OSS({
    ...accessParams,
    region: OSS_REGION,
    bucket: OSS_BUCKET,
    secure: true,
    refreshSTSToken: async () => getAccessParams(await getStsToken()),
    refreshSTSTokenInterval: 300000,
  });
}

/**
 * 处理多个文件的上传
 * @param client - OSS客户端实例
 * @param files - 文件数组，每个文件对象需要包含有效的raw属性和name属性
 * @param options - 上传选项，包括是否重命名和根目录
 * @returns 每个文件的上传结果，包含文件名和可能的错误信息
 */
async function uploadFilesBatch(
  client: OSS,
  files: UploadFile[],
  options: { rename: boolean; rootDir?: string },
): Promise<any[]> {
  if (files.length === 0) return;
  if (files.some((file) => !file || !file.raw || !file.raw.name)) {
    throw new Error('每个文件对象必须包含有效的 raw 属性和 name 属性');
  }

  // 并发控制（最多5个并发）
  const limit = pLimit(5);
  const uploadPromises = files.map((file) =>
    limit(async () => {
      const fileName = file.raw.name;

      try {
        await uploadFile(client, file, options);
        return { fileName };
      } catch (error) {
        return {
          fileName,
          // 捕获错误并返回详细信息
          error: error instanceof Error ? error.message : String(error),
        };
      }
    }),
  );

  return await Promise.all(uploadPromises);
}

/**
 * 处理单个文件的上传
 * @param client - OSS客户端
 * @param file - 单个文件
 * @param options - 上传选项
 * @returns 单个文件的上传结果
 */
async function uploadFile(
  client: OSS,
  file: UploadFile | File,
  options?: { rename: boolean; rootDir?: string },
): Promise<any> {
  if (!file.name) {
    throw new Error("The 'file' parameter must have a valid 'name' property.");
  }

  try {
    const fileName = options?.rename
      ? normalFileName(file.name, options?.rootDir ?? OSS_DEFAULT_UPLOAD_DIR)
      : file.name;
    const result = await client.put(fileName, file);

    return {
      ...result,
      url: replaceUrlDomain(result.url),
    };
  } catch (error) {
    console.error(`Failed to upload file ${file.name}:`, error);
    return undefined;
  }
}

/**
 * 分片上传
 * @param file - 文件
 * @param options - 上传选项
 * @returns 上传结果
 */
export const multipartUpload = async (
  file: UploadFile | { blob: Blob; name: string },
  options: {
    rename: boolean;
    rootDir?: string;
    partSize?: number;
    onProgress?: (progress: number, client: any) => void;
  },
) => {
  // 获取 STS Token
  const stsRes = await getStsToken();
  if (!stsRes || typeof stsRes !== 'object' || !('data' in stsRes)) {
    throw new Error('Invalid STS token response');
  }

  // 初始化 OSS 客户端
  const ossClient = initOSSClient(stsRes);

  // 统一获取文件名和数据
  const fileName = options.rename
    ? normalFileName(file.name, options.rootDir ?? OSS_DEFAULT_UPLOAD_DIR)
    : file.name;
  let fileData: Blob | File;
  if ('blob' in file && file.blob) {
    fileData = file.blob;
  } else if ('raw' in file && file.raw) {
    fileData = file.raw;
  } else {
    throw new Error('文件参数不合法');
  }

  const result = await ossClient.multipartUpload(fileName, fileData, {
    partSize: options.partSize ?? 102400,
    progress: (p: number) => {
      options?.onProgress?.(p, ossClient);
    },
  });

  // 兼容 url 获取
  let url =
    (result?.url as string) || result?.res?.requestUrls?.[0]?.split('?')[0];
  if (url?.length) {
    return { name: fileName, url };
  }

  return Promise.reject(new Error('上传失败'));
};

/**
 * 提取STS访问参数
 * @param res - STS响应
 * @returns 访问参数
 */
function getAccessParams(res: any) {
  const data = res.data.data || res.data;
  if (!data.AccessKeyId || !data.AccessKeySecret || !data.SecurityToken) {
    throw new Error('Invalid STS token response');
  }

  return {
    accessKeyId: data.AccessKeyId,
    accessKeySecret: data.AccessKeySecret,
    stsToken: data.SecurityToken,
  };
}

// 重命名文件名
const normalFileName = (name: string, rootDir = OSS_DEFAULT_UPLOAD_DIR) => {
  const uuid = getRandomUUID(name).substring(10);
  const hashedName = uuid.substring(1, 8);
  return `${rootDir}/${hashedName}/${name}`;
};

/**
 * 生成随机UUID
 * @param key - 用于生成UUID的键
 * @returns 随机UUID字符串
 */
function getRandomUUID(key = `${Date.now()}`): string {
  let randomValue = Math.random().toString().replace('0.', '');
  let webcrypto: Crypto | undefined;

  try {
    if (typeof window !== 'undefined' && window.crypto) {
      webcrypto = window.crypto;
    }
  } catch (error) {
    console.error('getRandomUUID error: ', error);
  }

  if (webcrypto) {
    try {
      const ar = webcrypto.getRandomValues(new Uint8Array(12));
      randomValue = ar.toString() || randomValue;
    } catch (error) {
      console.error('getRandomUUID error: ', error);
    }
  }

  return uuidv5(
    `${JSON.stringify(key)}_${Date.now()}_${randomValue}`,
    uuidv5.URL,
  );
}

/**
 * 上传验证
 * @param params - 上传参数
 */
export const onUploadValidate = (params: {
  files: UploadFile[];
  type: string;
}) => {
  const { files, type } = params;
  if (type === 'FILE_OVER_SIZE_LIMIT') {
    MessagePlugin.warning(files[0].response.error);
  }
};
