<template>
  <div class="bg-[#fff]" v-lkloading="{ show: lkloadingFlag }">
    <work-area-head ref="workAreaHeadRef" :tab-list="tabList" :active-index="activeIndex" :group-list="groupList"
      :activation-group-item="activationGroupItem" :not-red="true" @open-refresh="openRefresh" @deltab-item="deltabItem"
      @upt-work-bench-tab-item="uptWorkBenchTabItem" @set-work-bench-tab-item="setWorkBenchTabItem"
      @get-group-list-api="getGroupListApi" @setactivation-group-item="setactivationGroupItem"
      @set-active-index="setActiveIndex" />
    <div ref="bodyRef" style="position: relative; height: calc(100% - 40px)">
      <router-view v-slot="{ Component }" :tab-list="tabList" :activation-group-item="activationGroupItem"
        :group-list="groupList" :tab-index="tabIndex" class="work-bench-index" :cloud-disk-type="activationGroupItem"
        @open-refresh="openRefresh" @get-group-list-api="getGroupListApi" @deltab-item="deltabItem" @close="closeTab"
        @closeViewPage="closeViewPage" @fistTab="fistTab" @add="add" @set-active-index="setActiveIndex"
        @publish="onPublish" @upt-work-bench-tab-item="uptWorkBenchTabItem"
        @set-work-bench-tab-item="setWorkBenchTabItem">
        <keep-alive :key="pageKey" :exclude="data.exc">
          <component :is="Component" v-if="data.showCompoent"
            :key="route.path.includes('activityParticipantDetail') ? route.path : Component.key" ref="routerViewRef"
            :exclude="data.exc" />
        </keep-alive>
      </router-view>
      <drawer ref="drawerRef" :attach="() => bodyRef" />
    </div>
  </div>
</template>

<script setup lang="jsx">
  import { ref, watch, nextTick, provide, onMounted } from "vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import { useI18n } from "vue-i18n";
  import { isEmpty } from "lodash";
  import { useRoute, useRouter } from "vue-router";
  import { getCommonTeamsAxios } from "@renderer/api/member/api/businessApi";
  import { teamcompanysTotal } from "@renderer/api/workBench/index.ts";
  import WorkAreaHead from "./components/WorkAreaHead.vue";
  import { useApprovalStore } from "@renderer/store/modules/approval";
  import omit from "lodash/omit";
  import to from 'await-to-js';
  import { byOpenIdGetStaffsPlatformAxios } from '@renderer/api/digital-platform/api/businessApi';
  import { useMessageStore } from "@/views/message/service/store";
  // import { useComponentEvent } from '@/hooks/useComponentEvent';
  import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
  import LynkerSDK from '@renderer/_jssdk';
  import Drawer from '@renderer/_jssdk/components/drawer/index.vue';
  import { clearAllNode } from '@renderer/_jssdk/components/iframe/iframePool';

  const { ipcRenderer, shell } = LynkerSDK;
  const msgStore = useMessageStore();

  const bodyRef = ref(null);
  const drawerRef = ref(null);

  const pageKey = ref(1);

  const { t } = useI18n();
  const lkloadingFlag = ref(false);
  const router = useRouter();
  const route = useRoute();
  const routerViewRef = ref(null);
  const tabList = ref([]);
  const groupList = ref([]);
  const activationGroupItem = ref({});
  const tabIndex = ref(0);
  const data = ref({
    showCompoent: true,
    exc: "",
  });
  const workAreaHeadRef = ref(null);
  const emits = defineEmits(["add"]);

  const add = () => {
    emits("add");
  };

  // 获取专属名称map数据
  const onGetByOpenIdGetStaffsPlatformAxios = () => {
    return new Promise(async (resolve, reject) => {
      const [err, res] = await to(byOpenIdGetStaffsPlatformAxios({}));
      if (err) {
        console.error('专属名称map报错：', err?.message)
        reject(err?.message)
        return;
      }
      const { data } = res?.data;
      console.log('获取专属名称map数据', data)
      resolve(data)
    })
  }


  const onGetExcluseNamse = async () => {
    const [errP, resP] = await to(onGetByOpenIdGetStaffsPlatformAxios());
    if (resP && resP?.length > 0) {
      // 转换为键值对数组 [key, value]
      const entries = resP.map(item => [item.team_id, item.exclusive_name]);
      msgStore.exclusiveNames = new Map(entries);
      console.log('msgStorekakxi', msgStore.exclusiveNames);
    }
  }
  var currentTeamId = ref('');
  watch(activationGroupItem, (newVal) => {
    if (currentTeamId.value !== newVal?.teamId) {
      currentTeamId.value = newVal?.teamId;
      setTimeout(() => {
        // clearAllNode();
      }, 0);
    }
  });

  const getGroupListApi = (val) => {
    console.log(val, "val走進這裡");

    // 增加一个专属名称列表请求
    onGetExcluseNamse();

    getCommonTeamsAxios().then((res) => {
      groupList.value = res.data.data;
      if (val) {
        activationGroupItem.value = res.data.data.find((e) => e.teamId === val);
      }
      if (!activationGroupItem.value?.teamId) {
        activationGroupItem.value = res.data.data[0];
      }
      window.localStorage.setItem("workBenchTeamid", activationGroupItem.value.teamId);
      getRedNums();
      window.localStorage.setItem("honorteamid", activationGroupItem.value?.teamId);
      // 初始化时-->保存该值
      window.localStorage.setItem("businessTeamId", activationGroupItem.value?.teamId);
      window.localStorage.setItem("honorteam", JSON.stringify(activationGroupItem.value));
    });
  };
  ipcRenderer.on("set-unread-post-approval", () => {
    console.log("触发流水线!");
    getGroupListApi(activationGroupItem.value?.teamId);
  });
  const fistTab = () => {
    for (let index = 0; index < tabList.value.length; index++) {
      const element = tabList.value[index];
      let nameArr = element.name.split(",");
      nameArr.forEach((e) => {
        setTimeout(() => {
          data.value.exc = e;
          data.value.showCompoent = false;
          setTimeout(() => {
            data.value.showCompoent = true;
            data.value.exc = "";
          });
        });
      });
    }
    tabList.value = [tabList.value[0]];
  }
  watch(router.currentRoute, () => {
    // 路由复用
    console.log(route.query, "route.queryroute.queryroute.query333333");
    console.log(tabList.value, "tabList.valuetabList.value13123123");

    if (route.query?.toAdmin) {
      // tabList.value = [tabList.value[0]];
      getGroupListApi(route.query.teamId);
    }

  });
  ipcRenderer.on("update-niche-reddot", () => {
    console.log("触发流水线!");
    getGroupListApi(activationGroupItem.value?.teamId);
  })


  ipcRenderer.on("IM-refresh", () => {
    if (activationGroupItem.value?.teamId) {
      console.log("触发流水线!");
      let i = 0;
      console.log('workbench lss', i++)
      getGroupListApi(activationGroupItem.value?.teamId);
    }
  });


  const activeIndex = ref(0);
  // 关闭页签 新增isClose参数 用于判断是否需要直接关闭页签
  const deltabItem = async (index, flag, notTip) => {
    console.log('触发这里来了来了了了',index, flag, notTip, tabList.value[index]);
    if (route.path === '/workBenchIndex/init_apply' || route.path === '/workBenchIndex/approve_home') {
      flag = true;
    }

    /** 党建的东西 -start */
    const isPbRelease = tabList.value.some((v) => ["release_party"].includes(v.name));
    try {
      if (isPbRelease && routerViewRef.value?.detectionClose(index, flag)) return;
    } catch (error) {
      console.log("error实例", error);
    }
    // 风采草稿判断
    if (tabList.value[index].name === "fengcai_release_party") {
      if (route.path !== tabList.value[index].path) {
        router.push(tabList.value[index].path);
        setTimeout(() => {
          routerViewRef.value?.closePage();
        }, 100);
        return;
      }
      if (routerViewRef.value?.detectionClose(index, flag)) {
        return;
      }
    }
    if (tabList.value[index].name === "bench_approvalDesgin") {
      const approvalStore = useApprovalStore();
      approvalStore.initData();
    }
    /** 党建的东西 -end */
    if (tabList.value[index].delFlag && !notTip) {
      if (route.path !== tabList.value[index].path) {
        console.log(tabList.value[index], "參數啊啊啊啊");
        router.push(tabList.value[index].path);
        setTimeout(() => {
          routerViewRef.value.closeOrSaveFn(tabList.value[index].name);
        }, 100);
        return;
      }
      routerViewRef.value.closeOrSaveFn(tabList.value[index].name);
      return;
    }

    // 关闭页面前确认
    const beforeClose = routerViewRef.value?.handleBeforeClose;
    if (beforeClose && typeof beforeClose === 'function') {
      const confirm = await beforeClose();
      if (!confirm) return;
    }
    console.log(tabList.value[index], "tabList.value[index]11111");

    // 根据审批模块需求变更为数组循环
    let nameArr = tabList.value[index].name?.split(",");
    console.log(nameArr, "nameArr11111");
    nameArr?.forEach((e) => {
      setTimeout(() => {
        data.value.exc = e;
        data.value.showCompoent = false;
        setTimeout(() => {
          data.value.showCompoent = true;
          data.value.exc = "";
        });
      });
    });

    tabList.value?.splice(index, 1);
    console.log(tabList.value, "啊塞擦声");
    let routeObj = omit(route.query, ['toAdmin']);

    if (flag) {
      const tabItemNext = tabList.value[tabList.value.length - 1];
      // const tabItemNextQuery = JSON.parse(JSON.stringify(tabItemNext.query));
      let tabItemNextQuery = omit(tabItemNext.query, ['toAdmin']);

      router.push({
        path: tabItemNext.path,
        query: isEmpty(tabItemNext.query) ? routeObj : tabItemNextQuery,
      });
    }

    // 在页签关闭完成后触发组件状态更新
    // data.value.showCompoent = false;
    // nextTick(() => {
    //   data.value.showCompoent = true;
    // });
  };

  const closeTab = (page) => {
    const index = tabList.value.findIndex((item) => item.name === page);
    if (index !== -1) {
      deltabItem(index, true, true);
    }
  }

  if (route.query.activationGroupItemTeamId) {
    getGroupListApi(route.query.activationGroupItemTeamId);
  } else {
    getGroupListApi();
  }
  onMountedOrActivated(() => {
    // if (route.query.activationGroupItemTeamId) {
    //   getGroupListApi();
    // }
    console.log(drawerRef.value, 'drawerRef.value');
    // drawerRef.value.open({
    //   id: 'aaaaaaaa',
    //   title: '1',
    //   url: 'https://www.baidu.com',
    // });
    // setTimeout(() => {
    //   drawerRef.value.open({
    //     id: 'bbbbbb',
    //     title: '2',
    //     url: 'https://taobao.com/',
    //   });
    // }, 1000)
  });

  onMounted(() => {
    // 添加ipc监听
    LynkerSDK.ipc.handleRenderer('work-bench-is-inited', (val) => {
      return true;
    })
    LynkerSDK.ipc.handleRenderer('work-bench-open-drawer-for-webview', (val) => {
      console.log('work-bench-open-drawer-for-webview', 'val');
      if (val.id && val.url) {
        drawerRef.value.open({
          id: val.id,
          title: val.title,
          url: val.url,
        });
      }
    })
    LynkerSDK.ipc.handleRenderer('work-bench-close-drawer-for-webview', (val) => {
      console.log('work-bench-close-drawer-for-webview', 'val');
      if (val.id) {
        drawerRef.value.close(val.id);
      }
    })
    LynkerSDK.ipc.handleRenderer('open-work-bench-tab-item', async (val) => {
      console.log('open-work-bench-tab-item', 'val', val, tabList);
      const index = tabList.value.findIndex((item) => item.path_uuid === val.path_uuid);
      if (val.path_uuid && index > -1) {
        tabList.value[index] = {
          ...tabList.value[index],
          ...val,
          updateKey: val.path_uuid,
          title: val.title || tabList.value?.[index]?.title,
          url: val.url || tabList.value?.[index]?.url,
          icon: val.icon || tabList.value?.[index]?.icon,
          activeIcon: val.activeIcon || tabList.value?.[index]?.activeIcon,
        };
      } else {
        tabList.value.push(val);
      }
      router.push({
        path: val.path,
        query: {
          ...(val.query || {}),
          __tabs_id__: val.path_uuid,
          __tabs_title__: val.title || tabList.value?.[index]?.title,
          __tabs_icon__: val.icon || tabList.value?.[index]?.icon,
          __tabs_active_icon__: val.activeIcon || tabList.value?.[index]?.activeIcon,
        },
      });
      return true;
    });
    LynkerSDK.ipc.handleRenderer('close-work-bench-tab-item', async (val) => {
      console.log('close-work-bench-tab-item', 'val', val, tabList);
      const index = tabList.value.findIndex((item) => item.path_uuid === val.path_uuid);
      // if (index !== -1) {
      //   tabList.value.splice(index, 1);
      // }
      await workAreaHeadRef.value.deltabItem(tabList.value[index]);
    });
    LynkerSDK.ipc.handleRenderer('update-work-bench-tab-item', async (val) => {
      console.log('update-work-bench-tab-item', 'val', val, tabList);
      const index = tabList.value.findIndex((item) => item.path_uuid === val.path_uuid);
      tabList.value[index] = {
        ...tabList.value[index],
        ...val,
      };
    });
    LynkerSDK.ipc.handleRenderer('get-work-bench-tab-list', async () => {
      return tabList.value?.map((item) => ({
        title: item.title,
        path_uuid: item.path_uuid,
        path: item.path,
      }));
    });
    LynkerSDK.ipc.handleRenderer('work-bench-reload', async () => {
      console.log('work-bench-reload', 'val');
      openRefresh();
      return true;
    })
  })

  const openRefresh = () => {
    getGroupListApi();

    // pageKey.value++;
    const currentRoute = router.currentRoute.value;
    console.log(currentRoute, "当前路由aseawdaw");
    if ('partnerOrder,partnerCustomer,partnerIndex,partnerCustomer'.includes(currentRoute.name)) {
      pageKey.value++;
    }
    currentRoute.matched.forEach((r) => {
      // fullPath替换成了path
      if (r.path === currentRoute.path) {
        // 获取到当前页面的name
        const comName = r.components.default.name || r.name;

        if (comName.name === "bench_approvalDesgin") {
          const approvalStore = useApprovalStore();
          approvalStore.initData();
        }
        console.log(comName, "拿到的name");
        if (comName !== undefined) {
          data.value.exc = comName;
          data.value.showCompoent = false;
        }
      }
    });
    setTimeout(() => {
      data.value.showCompoent = true;
      data.value.exc = "";
    });
  };

  provide('refresh-page', openRefresh);

  const setActiveIndex = (index) => {
    activeIndex.value = index;
  };
  ipcRenderer.on("del-work-bench-tab-item", (e, val) => {
    console.log(val, "删除的val");
    tabList.value = tabList.value.filter((item) => item.name !== val);
  });

  /**
   * 确保当前激活组织为目标 teamId
   * @param {object} item - 需包含 teamId 的对象
   * @returns Promise<boolean> - true 表示已切换或无需切换，false 表示用户取消
   */
  const ensureTeamSwitched = (item) => {
    const teamId = item?.query?.teamId
    const index = groupList.value.findIndex((v) => v?.teamId === teamId);
    if (index === -1) return Promise.resolve(true);
    return setactivationGroupItem(groupList.value[index]);
  };

  ipcRenderer.on("set-work-bench-tab-item", async (e, val) => {
    console.log(val, "vaset-work-bench-tab-iteml");

    const ok = await ensureTeamSwitched(val);
    if (!ok) return;

    setTimeout(() => {
      setWorkBenchTabItem(val);
    }, 200);

    if (val.gopath) {
      router.push({
        path: val.path,
        query: val.query || {},
      });
    }
  });
  const getRedNums = async () => {
    for (const element of groupList.value) {
      console.log(`正在请求 teamId: ${element.teamId}`); // 打印当前 teamId

      try {
        const res = await teamcompanysTotal(element.teamId);
        console.log(`teamId: ${element.teamId} 返回数据：`, res.data.data); // 打印返回结果

        res.data.data.forEach((ele) => {
          if (ele.count > 0) {
            element.isRedDot = true;
          }
        });
      } catch (error) {
        console.error(`请求 teamId: ${element.teamId} 失败`, error);
      }
    }
  };
  // 更新tab
  const uptWorkBenchTabItem = (item) => {
    if (item.updateKey) {
      const index = tabList.value.findIndex((v) => v?.updateKey === item.updateKey);
      tabList.value[index] = item;
    }
  };
  const setWorkBenchTabItem = (item) => {

    if (item.upd) {
      const index = tabList.value.findIndex(
        (v) => v?.query?.id === item?.query?.id,
      )


      tabList.value[index].title = item.title;


      return
    }
    if (item.addNew) {

      let cloneRouteQuery = JSON.parse(JSON.stringify(route.query));
      let itemQuery = JSON.parse(JSON.stringify(item.query));
      delete cloneRouteQuery.__tabs_id__;
      delete itemQuery.__tabs_id__;
      let routeNotUuidArr = ["analysisExpressDetail",'bench_ad_details', 'noticeDetail', 'noticeDetailRead', 'policyExpressDetail', "PolicyExpressInfo", "PolicyAnalysisInfo"]
      const index = tabList.value.findIndex(
        (v) => v?.path === item.path && routeNotUuidArr.includes(route.name) ? JSON.stringify(itemQuery)===JSON.stringify(cloneRouteQuery) : JSON.stringify(v?.query) === JSON.stringify(item.query)
      );
      console.log(index, '我拿到的id');
      console.log(tabList.value, '我拿到的tabList');
      console.log(item, '我拿到的item');
      console.log(itemQuery, '我拿到的itemQueryitem');
      console.log(cloneRouteQuery, '我拿到的cloneRouteQueryitem');
      console.log(route.query, '我拿到的query');

      if (index === -1) {
        tabList.value.push(item);
      } else {
        tabList.value[index] = item;
      }
    } else {
      let hasDuplicate = false;
      let index = 0;
      if (item.path_uuid === "new-partner") { //合伙人特殊处理一下
        index = tabList.value.findIndex((e) => e.path_uuid === item.path_uuid);
        hasDuplicate = index !== -1;
        item.updateKey = "new-partner";
      } else {
        index = tabList.value.findIndex((e) => e.path === item.path);
        hasDuplicate = index !== -1;
      }
      console.log(tabList.value, "tabList.valuetabList.valuetabList.value");
      console.log(item, "tabList.valuetabList.valuetabList.valueitemitem");
      // const hasDuplicate = index !== -1;
      if (hasDuplicate) {
        tabList.value[index] = item;
      } else {
        tabList.value.push(item);
      }
    }
  };
  provide("addTabFn", setWorkBenchTabItem);

  const setactivationGroupItem = (item) => {
    return new Promise((resolve) => {
      if (item.teamId !== activationGroupItem.value.teamId) {
        const confirmDia = DialogPlugin.confirm({
          header: t("square.switchAndRefresh"),
          body: t("square.switchAndRefreshContent"),
          confirmBtn: t("square.action.confirm"),
          cancelBtn: t("square.action.cancel"),
          zIndex: 99999999,
          placement: "center",
          onConfirm: async () => {
            for (let index = 0; index < tabList.value.length; index++) {
              const element = tabList.value[index];
              console.log(element.name, "element.routeNameelement.routeNameelement.name");
              // 根据审批模块需求变更为数组循环
              let nameArr = element.name?.split(",");
              nameArr.forEach((e) => {
                setTimeout(() => {
                  data.value.exc = e;
                  data.value.showCompoent = false;
                  setTimeout(() => {
                    data.value.showCompoent = true;
                    data.value.exc = "";
                  });
                });
              });
            }
            activationGroupItem.value = item;
            activationGroupItem.value.noJump = true;
            window.localStorage.setItem("workBenchTeamid", activationGroupItem.value.teamId);

            window.localStorage.setItem("teamId", item.teamId);
            window.localStorage.setItem("businessTeamId", item.teamId);
            window.localStorage.setItem("honorteamid", item.teamId);
            window.localStorage.setItem("honorteam", JSON.stringify(item));

            confirmDia.hide();
            router.push({
              path: "/workBenchIndex/workBenchHome",
              query: {},
            });
            localStorage.removeItem("leftTabValue");
            getRedNums();
            setTimeout(() => {
              tabList.value = [tabList.value[0]];
              console.log(tabList.value, "tabList.valuetabList.value");
              // 删除所有的webview
              // document.getElementsByTagName('webview').forEach((item) => {
              //   item.remove();
              // });
            }, 200);
            setTimeout(() => {
              clearAllNode();
            }, 0);

            resolve(true); // 切换成功
          },
          onClose: () => {
            confirmDia.hide();
            resolve(false); // 用户取消
          },
        });
      } else {
        resolve(true); // 组织相同
      }
    });
  };

  // 使用组件事件 hooks
  // useComponentEvent({
  //   handler: (data) => {
  //     if (data.target === 'all' || data.target === routerViewRef.value?.$options.name) {
  //       routerViewRef.value?.handleEvent?.(data);
  //     }
  //   },
  // });

  // 修改 onPublish 方法
  const onPublish = (flag = true) => {
    const index = tabList.value.findIndex((item) => item.name === "release_party");
    if (tabList.value[index].delFlag) {
      routerViewRef.value.closeOrSaveFn(tabList.value[index].name);
      return;
    }

    data.value.exc = tabList.value[index].name;
    data.value.showCompoent = false;
    setTimeout(() => {
      data.value.showCompoent = true;
      data.value.exc = "";
    });
    tabList.value.splice(index, 1);
    if (flag) {
      const tabItemNext = tabList.value[tabList.value.length - 1];
      router.push({
        path: tabItemNext?.path,
        query: isEmpty(tabItemNext.query) ? route.query : tabItemNext.query,
      });
    }
  };

  // 关闭页签 新增flag参数 用于判断是否需要回到前一页
  const closeViewPage = (page, flag) => {
    console.log(page, "pagepagepage");
    const index = tabList.value.findIndex((item) => item.name === page);
    if (index !== -1) {
      tabList.value.splice(index, 1);
      let routeObj = omit(route.query, ['toAdmin']);
      if (flag) {
        const tabItemNext = tabList.value[tabList.value.length - 1];
        let tabItemNextQuery = omit(tabItemNext.query, ['toAdmin']);

        router.push({
          path: tabItemNext.path,
          query: isEmpty(tabItemNext.query) ? routeObj : tabItemNextQuery,
        });
      }
    }
  }
</script>
<style>
  .work-bench-index {
    background: #fff;
    overflow: auto;
    height: calc(100%) !important;
  }

  ::-webkit-scrollbar {
    width: 4px;
    background-color: #f5f5f5;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: #e3e6eb;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #c8c8c8;
  }
</style>
<style lang="less" scoped>
  :deep(.appAuthTipDialog) {
    .t-dialog__body__icon {
      padding: 16px 0 24px;
    }

    .t-button--theme-default {
      font-weight: 600;
    }
  }
</style>
