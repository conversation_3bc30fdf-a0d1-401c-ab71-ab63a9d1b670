<!--
 * @Description:
-->

# Empty 空状态

### 介绍

空状态时的占位提示。

### 引入

```js
import rk-empty from "@renderer/components/rk-common/rk-empty/index.vue";
```

## 代码演示

### 基础用法

```html
<rk-empty />
```

### 自定义图片与大小

通过 `image-size` 属性图片的大小。

```html
<rk-empty img="path/to/image.png" width="300px" height="300px" />
```

### 显示自定义提示信息

通过 `tip` 属性自定义提示信息。

```html
<rk-empty tip="自定义的提示信息" />
```

### Slots

| 名称    | 说明           |
| ------- | -------------- |
| default | 自定义图标     |
| tip     | 自定义提示信息 |
| bottom  | 自定义底部内容 |

### 插入自定义 svg

```html
<template>
  <rk-empty>
    <svg-icon name="custom-icon" />
  </rk-empty>
</template>
<!-- 或者 -->
<template>
  <rk-empty>
    <template #default>
      <svg-icon name="custom-icon" />
    </template>
  </rk-empty>
</template>
```

### 插入提示信息

通过默认插槽可以在 Empty 组件的下方插入内容。

```html
<template>
  <rk-empty>
    <template #tip>
      <div>提示信息</div>
    </template>
  </rk-empty>
</template>
```

### 插入底部内容

通过默认插槽可以在 Empty 组件的下方插入内容。

```html
<template>
  <rk-empty>
    <template #bottom>
      <button @click="loadData">加载数据</button>
    </template>
  </rk-empty>
</template>
```

## API

### Props

| 参数   | 说明                                                     | 类型     | 默认值   |
| ------ | -------------------------------------------------------- | -------- | -------- |
| img    | 图片 URL                                                 | _string_ | ``       |
| width  | 图标的宽度，默认单位为 `px`                              | _string_ | 200px    |
| height | 图标的高度，默认单位为 `px`                              | _string_ | 200px    |
| tip    | 自定义提示信息，如果未提供则使用 name 对应的默认提示信息 | _string_ | 暂无数据 |
| name   | 缺省类型                                                 | _string_ | no-data  |

### 内置提示信息映射

RKEmpty 组件内置了一系列预定义的提示信息，可以通过设置 name 属性来引用它们：

```ts
{
  404: '页面未找到',
  502: '服务器错误',
  fail: '操作失败',
  success: '操作成功',
  offline: '网络断开',
  no_search_contact: '用户不存在',
  'chat-init': '聊天初始化',
  'fail-location': '定位失败',
  'no-address': '暂无地址',
  'no-auth': '暂无权限',
  'no-collect': '暂无收藏',
  'no-data': '暂无数据',
  'no-goods': '暂无商品',
  'no-message': '暂无消息',
  'no-order': '暂无订单',
  'no-result': '搜索无结果',
  'new-album': '新建拾光节点',
  'no-comment': '目前还没有人评论哦',
  'no-fans': '目前还没有粉丝哦',
  'no-forward': '目前还没有人转发哦',
  'no-like': '目前还没有人点赞哦',
  'no-firend-list': '马上发一个动态试试吧',
}
```

### 类型定义

组件导出以下类型定义：

```ts
export type EmptyProps = ExtractPropTypes<typeof emptyProps>;
```
