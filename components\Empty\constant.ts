/*
 * @Author: ziwen.ji ji<PERSON><EMAIL>
 * @Date: 2024-06-19 14:19:00
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-06-19 17:18:18
 * @FilePath: /lynker-desktop/src/renderer/components/rk-common/rk-empty/index.ts
 * @Description:
 */
import type { ExtractPropTypes } from 'vue';
import { buildProps } from '../../utils/vue-util';

export const tipMap = {
  404: '404',
  502: '502',
  fail: '操作失败',
  success: '操作成功',
  offline: '网络断开',
  no_search_contact: '用户不存在',
  'chat-init': '聊天初始化',
  'fail-location': '定位失败',
  'no-address': '暂无地址',
  'no-auth': '暂无权限',
  'no-collect': '暂无收藏',
  'no-data': '暂无数据',
  'no-goods': '暂无商品',
  'no-message': '暂无消息',
  'no-order': '暂无订单',
  'no-result': '搜索无结果',
  'new-album': '新建拾光节点',
  'no-comment': '目前还没有人评论哦',
  'no-fans': '目前还没有粉丝哦',
  'no-forward': '目前还没有人转发哦',
  'no-like': '目前还没有人点赞哦',
  'no-firend-list': '马上发一个动态试试吧',
  violation: '内容违反平台规范，无法查阅',
  'no-data-activity': '暂无活动',
  'no-contact': '暂无联络方式',
  'image-fail': '加载失败',
  slogan: '',
  no_detail: '详情未设置',
};

export const emptyProps = buildProps({
  img: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: 'no-data',
  },
  // 图标宽度
  width: {
    type: String,
    default: '200px',
  },
  // 图标高度
  height: {
    type: String,
    default: '200px',
  },
  // 提示消息
  tip: String,
} as const);

export type EmptyProps = ExtractPropTypes<typeof emptyProps>;
