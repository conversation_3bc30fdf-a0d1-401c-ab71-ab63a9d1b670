<script setup lang="ts">
import { computed, onMounted, reactive, ref, nextTick, onUnmounted } from 'vue';
import lodash from 'lodash';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import to from 'await-to-js';
import { useTabsStore } from '@renderer/components/page-header/store';
import { getMemberApplyLinkAxios } from '@renderer/api/member/api/businessApi';
import { getMemberApplyLinkAxios as getPoliticsApplyLinkAxios } from '@renderer/api/politics/api/businessApi';
import { getMemberApplyLinkAxios as getAssociationApplyLinkAxios } from '@renderer/api/association/api/businessApi';
import { getMemberApplyLinkAxios as geUniApplyLinkAxios } from '@renderer/api/uni/api/businessApi';

import { getMemberApplyLinkAxios as getCBDApplyLinkAxios } from '@renderer/api/cbd/api/businessApi';
import { getResponseResult } from '@renderer/utils/myUtils';
import { MessagePlugin, Icon as TIcon } from 'tdesign-vue-next';
import { inviteUrl } from '@renderer/utils/baseUrl';
import Qs from 'qs';
import LynkerSDK from '@renderer/_jssdk';
import { DigitalPlatformTypeSquare, originType } from '@renderer/views/digital-platform/utils/constant';
import { externalApp } from '@renderer/api/workBench';
// import { SquareType } from '@renderer/api/square/enums';
import EntryItem from './EntryItem.vue';
import KefuEntry from '@/views/square/homepage/components/KefuEntry.vue';
import { getAppEntries } from '@/api/square/square';
import useNavigate from '@/views/square/hooks/navigate';
import { useSquareStore } from '@/views/square/store/square';
import { Entry, EntryType, Square, SquareData, ActiveStatus, teamAnnualFeeResponse } from '@/api/square/models/square';

// 扩展 Entry 类型以包含 market 属性
interface ExtendedEntry extends Entry {
  market?: {
    platformUuid: string;
    nameSuffix: string;
  };
  info?: any; // 用于 ExternalApp 类型
}

const { shell } = LynkerSDK;
const props = defineProps<{
  squareData: SquareData;
  feeInfo: teamAnnualFeeResponse;
  hideKefu?: boolean;
}>();
const emit = defineEmits(['open-buy', 'kefu-loaded', 'loaded']);

const route = useRoute();
const router = useRouter();
const store = useSquareStore();
const tabStore = useTabsStore();
const { t } = useI18n();
const { goServicePage, goAlbumPage } = useNavigate();

const square = computed<Square>(() => props.squareData?.square || ({} as Square));
const isSelf = computed(() => square.value.squareId === store.squareId);
const teamId = computed(() => props.squareData?.organizationProfile?.teamId);
const isSelfTeam = computed(() => !!store.squareList.find((v) => v.square.originId === teamId.value));

const kefuEnable = ref(false);
const appBoxRef = ref<HTMLElement>();
const entryItemsRef = ref<HTMLElement[]>([]);

const entries = ref<ExtendedEntry[]>([]);

// 展开/收起状态
const isExpanded = ref(false);
const maxDisplayCount = ref(15);

// 布局相关常量
const ITEM_WIDTH = 110; // apps-item-wrap 的宽度
const ITEM_GAP = 8; // gap 的宽度
const ROWS_LIMIT = 3; // 限制显示的行数

// 计算显示的应用入口项
const displayEntries = computed(() => {
  const allEntries = entries.value;
  const maxCount = maxDisplayCount.value;
  if (allEntries.length < maxCount) {
    return allEntries;
  }
  return isExpanded.value ? allEntries : allEntries.slice(0, maxCount);
});

// 切换展开/收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 计算容器宽度和每行能显示的项目数量
const calculateMaxDisplayCount = () => {
  if (!appBoxRef.value) return;

  const containerWidth = appBoxRef.value.offsetWidth;
  const availableWidth = containerWidth;

  // 计算每行能显示的项目数量
  // 对于 flex 布局，需要考虑 gap 的影响
  // 每行最后一个项目不需要 gap，所以计算公式为：
  // (容器宽度 + gap) / (项目宽度 + gap)
  let itemsPerRow = Math.floor((availableWidth + ITEM_GAP) / (ITEM_WIDTH + ITEM_GAP));

  // 验证计算结果是否合理
  const totalWidth = itemsPerRow * ITEM_WIDTH + (itemsPerRow - 1) * ITEM_GAP;
  const isValid = totalWidth <= availableWidth;

  if (!isValid && itemsPerRow > 1) {
    // 如果计算结果不合理，尝试减少一个项目
    itemsPerRow -= 1;
  }

  if (itemsPerRow <= 0) {
    maxDisplayCount.value = entries.value.length;
    return;
  }

  // 计算三行能显示的总项目数量
  const maxItemsInThreeRows = itemsPerRow * ROWS_LIMIT;

  // 如果总项目数量小于等于三行能显示的数量，显示全部
  if (entries.value.length <= maxItemsInThreeRows) {
    maxDisplayCount.value = entries.value.length;
  } else {
    // 否则显示三行减1的数量（为展开按钮留出空间）
    maxDisplayCount.value = maxItemsInThreeRows - 1;
  }
};

// 监听窗口大小变化
let resizeObserver: ResizeObserver | null = null;

// 初始化 ResizeObserver
const initResizeObserver = () => {
  if (!appBoxRef.value) return;

  resizeObserver = new ResizeObserver(() => {
    nextTick(() => {
      calculateMaxDisplayCount();
    });
  });

  resizeObserver.observe(appBoxRef.value);
};

// 获取应用入口权限
const getInfo = async (emitLoaded = true) => {
  const currentSquareId = square?.value?.squareId;
  const currentTeamId = store?.teamId || undefined;
  const [err, res] = await to(getAppEntries(currentSquareId, currentTeamId));
  if (err) return;

  const list = (res?.data?.data?.entries || []) as ExtendedEntry[];

  // 排除一些不需在列表显示的项（如还未发布上线的）
  const excludes = [EntryType.Kefu];
  let tempList = [];
  tempList = list.filter((v) => v.enable && Object.values(EntryType).includes(v.entryType) && !excludes.includes(v.entryType));
  try {
    if (teamId.value) {
      const externalAppRes = await externalApp(teamId.value, {
        channel_type: 'square',
      });
      const externalAppList = externalAppRes.data.data.items.map((item) => ({
        entryType: EntryType.ExternalApp,
        info: item,
        enable: true,
        activeStatus: ActiveStatus.Activated,
      }));
      tempList.push(...externalAppList);
    }
  } catch (error) {
    console.error(error);
  }
  // 排序：已激活的项目排在前面
  tempList = tempList.sort((a, b) => {
    if (a.activeStatus === ActiveStatus.Activated && b.activeStatus !== ActiveStatus.Activated) return -1;
    if (a.activeStatus !== ActiveStatus.Activated && b.activeStatus === ActiveStatus.Activated) return 1;
    return 0;
  });
  if (!isSelf.value) {
    tempList = tempList?.filter((item) => item.activeStatus === ActiveStatus.Activated);
  }

  entries.value = tempList.filter((item) => {
    // 服务类型，跟 EntryItem 组件的 v-if 条件一致，避免显示空白项
    if (item.entryType === EntryType.Serve) return (item.serve.displayType === 1 || (item.serve.displayType === 2 && item.serve.type === 1));
    return true;
  });

  kefuEnable.value = list.filter((v) => v.entryType === EntryType.Kefu && v.enable).length > 0;

  // 数据加载完成后重新计算 maxDisplayCount
  nextTick(() => {
    calculateMaxDisplayCount();
  });
  if (emitLoaded) {
    emit('loaded', entries.value);
  }
};

// 应用红点
const iconDot = reactive({} as Record<EntryType, boolean>);

const openExternal = (url: string) => shell.openExternal(url);

// 判断权益
const checkRights = async (item: ExtendedEntry) => {
  const items = props.feeInfo.annualFeeDetail.package.items;
  // 没有权益 - 将 EntryType 转换为对应的 FeePackageItemType 进行比较
  const entryTypeToItemTypeMap = {
    [EntryType.Niche]: 'NICHE' as const,
    [EntryType.Serve]: 'SERVE' as const,
  };
  const itemType = entryTypeToItemTypeMap[item.entryType];
  if (itemType && items.filter((v) => v.itemType === itemType).length === 0) {
    emit('open-buy');
    return;
  }
  if (item.activeStatus === ActiveStatus.NoContent) {
    MessagePlugin.warning('未发布');
    return false;
  }
  return true;
};

const checkRightsItems = [EntryType.Niche, EntryType.Serve];
const onlyCheckOpenedItems = [EntryType.CircleRingkol, EntryType.DigitalPlatformApply, EntryType.Publication, EntryType.Store, ...checkRightsItems];
const onlyCheckContentItems = [EntryType.Portal, EntryType.FENGCAI, EntryType.PartyBuilding, EntryType.CircleRingkol, EntryType.About, EntryType.Notice, EntryType.Activity, EntryType.Publication, EntryType.PARTNER, ...checkRightsItems];
const onlyCheckNoDataItems = [EntryType.Store, EntryType.PolicyExpress];

const entryClick = async (item: ExtendedEntry, isAuto = false) => {
  console.log(item, 'entryClick');

  if (isSelfTeam.value && checkRightsItems.includes(item.entryType)) {
    if (!await checkRights(item)) return;
  }

  if (onlyCheckOpenedItems.includes(item.entryType) && item.activeStatus === ActiveStatus.NotOpen) {
    MessagePlugin.warning(isSelfTeam.value ? '未开通' : '暂未开通功能');
    return;
  }

  if (onlyCheckContentItems.includes(item.entryType) && item.activeStatus === ActiveStatus.NoContent) {
    MessagePlugin.warning(isSelfTeam.value ? '未发布' : '暂无数据');
    return;
  }

  if (onlyCheckNoDataItems.includes(item.entryType) && item.activeStatus === ActiveStatus.NoContent) {
    MessagePlugin.warning('暂无数据');
    return;
  }

  const squareId = square.value.squareId;
  let fullPath = '';

  switch (item.entryType) {
    case EntryType.Portal:
      openExternal(item.portalUrl);
      break;
    case EntryType.FENGCAI:
      fullPath = `/square-fengcai/list?team_id=${teamId.value}`;
      tabStore.addTab({ label: t('banch.ptfc'), fullPath, icon: 'elegance' });
      router.push(fullPath);
      break;
    case EntryType.Niche:
      if (!await checkRights(item)) return;
      router.push(`/square/niche/list?teamId=${squareId}`);
      break;
    case EntryType.PartyBuilding:
      fullPath = `/square-pb/list?team_id=${teamId.value}`;
      tabStore.addTab({ label: t('square.partyBuild'), fullPath, icon: 'iconparty' });
      router.push(fullPath);
      break;
    case EntryType.CircleRingkol:
      if (squareId === store.squareId) {
        store.activeMenuIdx = 4;
      }
      fullPath = `/square/ringkol-circle-preview?id=${squareId}`;
      tabStore.addTab({ fullPath }, route.path);
      router.push(fullPath);
      break;
    case EntryType.Video:
      if (item.activeStatus === ActiveStatus.NoContent) {
        MessagePlugin.warning('未发布视频动态');
        return false;
      }
      fullPath = `/square/video?id=${squareId}`;
      // tabStore.addTab({ fullPath }, route.path);
      router.push(fullPath);
      break;
    case EntryType.Market:
      fullPath = `${marketPathMap[item?.market?.platformUuid]}?platform=square&origin=market&team_id=${teamId.value}&team_name=${item?.market?.nameSuffix}`;
      tabStore.addTab({ label: `店铺-${item?.market?.nameSuffix}`, fullPath, icon: 'marketvip' });
      router.push(fullPath);
      break;
    case EntryType.About:
      fullPath = `/square-alone/aboutour?platform=square&team_id=${teamId.value}`;
      tabStore.addTab({ label: '关于我们', fullPath, icon: 'iconaboutus' });
      router.push(fullPath);
      break;
    case EntryType.Publication:
      fullPath = `/square-alone/publication?platform=square&team_id=${teamId.value}&square=${encodeURIComponent(JSON.stringify(square.value))}`;
      tabStore.addTab({ label: `${square.value.name}的会刊`, fullPath });
      router.push(fullPath);
      break;
    case EntryType.Notice:
      fullPath = `/square-notice/list?platform=square&teamId=${teamId.value}`;
      tabStore.addTab({ label: '公告', fullPath, icon: 'systemservice' });
      router.push(fullPath);
      break;
    case EntryType.REGIONAL_RESOURCE:
      fullPath = `/square-alone/square_politics_square?platform=square&teamId=${teamId.value}&teamFullName=${square.value.name}`;
      tabStore.addTab({ label: t('niche.qyzy'), fullPath });
      router.push(fullPath);
      break;
    case EntryType.PARTNER:
      fullPath = '/square-partner/list';
      tabStore.addTab({ label: '合伙人', fullPath, icon: 'partner' });
      router.push(fullPath);
      break;
    case EntryType.Album:
      if (isSelf.value) {
        const fullPath = '/square/phone-album/time-line';
        tabStore.addTab({
          label: t('square.route.album'),
          fullPath,
          icon: 'photoalbum',
        });
        router.push(fullPath);
        return;
      }
      goAlbumPage(squareId);
      break;
    case EntryType.Serve:
      if (!await checkRights(item)) return;
      if (item.serve.displayType === 1) {
        goServicePage(square.value.squareId, square.value.name, teamId.value);
      }
      if (item.serve.displayType === 2) {
        const link = item.serve.consistent === 2
          ? item.serve.desktopEndLink || item.serve.link
          : item.serve.link || item.serve.desktopEndLink;
        openExternal(link);
      }
      break;
    case EntryType.DigitalPlatformApply: // 数字平台邀请
      onGetLink(item).then((link: string) => {
        openExternal(link);
      });
      break;
    case EntryType.Activity:
      fullPath = `/square-activity/list?team_id=${teamId.value}&platform=square`;
      tabStore.addTab({ label: '活动', fullPath, icon: 'activity' });
      router.push(fullPath);
      break;

    case EntryType.PolicyExpress:
      if (item.activeStatus === ActiveStatus.NoContent) {
        MessagePlugin.warning('暂无数据');
        return false;
      }
      fullPath = `/square-alone/square-policy-express?platform=square&teamId=${teamId.value}`;
      tabStore.addTab({ label: t('policy.policy_express'), fullPath, icon: 'icon-policy' });
      router.push(fullPath);
      break;
    case EntryType.Store:
      // fullPath = `/square-alone/shop-home?teamId=${teamId.value}&storeId=${item.store?.storeId || 2}`;
      // tabStore.addTab({ label: '店铺', fullPath, icon: 'shop-h796i02h' });
      // router.push(fullPath);
      await LynkerSDK.square.openTabForWebview({
        path_uuid: `shop-home-${item.store?.storeId}`,
        title: '店铺',
        icon: 'shop-h796i02h',
        url: LynkerSDK.getH5UrlWithParams('/shop/index.html#/shop-home', {
          teamId: teamId.value,
          storeId: item.store?.storeId,
        }),
      });
      break;
    case EntryType.ExternalApp: {
      if (isAuto) {
        return;
      }
      // if (isAuto) {
      // @ts-ignore
      let url = '';

      switch (item?.info?.type) {
        case 'app':
          url = item?.info?.share_link;
          break;
        case 'h5':
          url = item?.info?.desktop_link || item?.info?.h5_link;
          break;
        case 'wechat_official':
          url = item?.info?.article_link;
          break;
        case 'mini_program':
          url = '';
          break;
        case 'wechat_mini':
        default:
          url = '';
      }
      if (url) {
        LynkerSDK.openExternalApp({
          url,
        });
      } else {
        MessagePlugin.error('该应用暂无分享链接');
      }
      // }
      break;
    }
    default:
      break;
  }
};

/**
 * 根据数字平台类型获取邀请链接
 */
const onGetLink = async (item: ExtendedEntry): Promise<string> => {
  if (!item.digitalPlatformApply || !item.digitalPlatformApply.digitalPlatformType) {
    throw new Error('Invalid input: digitalPlatformType is required');
  }

  try {
    const res: any = await getInviteLinkAxios(item.digitalPlatformApply?.digitalPlatformType);
    let params = {
      link: res?.link,
    };

    const path = {
      [DigitalPlatformTypeSquare.Member]: '/account/jump?to=memberInvite',
      [DigitalPlatformTypeSquare.CBD]: '/account/jump?to=cbdInvite',
      [DigitalPlatformTypeSquare.Government]: '/account/jump?to=politicsInvite',
      [DigitalPlatformTypeSquare.Association]: '/account/jump?to=associationInvite',
      [DigitalPlatformTypeSquare.Uni]: '/account/jump?to=uniInvite',
    }[item.digitalPlatformApply.digitalPlatformType];

    if (!path) {
      throw new Error('Unsupported digitalPlatformType');
    }

    return `${inviteUrl}${path}&${Qs.stringify(params)}`;
  } catch (error) {
    console.error(error);
  }
};

const marketPathMap = {
  [originType.Member]: '/square-alone/square_digital_platform_member_shop',
  [originType.CBD]: '/square-alone/square_digital_platform_cbd_shop',
  [originType.Government]: '/square-alone/square_digital_platform_politics_shop',
  [originType.Association]: '/square-alone/square_digital_platform_association_shop',
  [originType.Uni]: '/square-alone/square_digital_platform_uni_shop',
};

/**
 * 获取数字商协会邀请链接
 */
const getInviteLinkAxios = async (type) => {
  console.log(type, 'toe呃呃呃呃呃呃呃呃呃呃呃');

  const originId = square.value?.originId;
  if (!originId) {
    throw new Error('Origin ID is not available');
  }

  const apiMap = {
    [DigitalPlatformTypeSquare.Member]: getMemberApplyLinkAxios,
    [DigitalPlatformTypeSquare.CBD]: getCBDApplyLinkAxios,
    [DigitalPlatformTypeSquare.Government]: getPoliticsApplyLinkAxios,
    [DigitalPlatformTypeSquare.Association]: getAssociationApplyLinkAxios,
    [DigitalPlatformTypeSquare.Uni]: geUniApplyLinkAxios,
  };

  // const marketPathMap = {
  //   [DigitalPlatformTypeSquare.Member]: '/square-alone/digital_platform_member_shop',
  //   [DigitalPlatformTypeSquare.CBD]: '/square-alone/digital_platform_cbd_shop',
  //   [DigitalPlatformTypeSquare.Government]: '/square-alone/digital_platform_politics_shop',
  //   [DigitalPlatformTypeSquare.Association]: '/square-alone/digital_platform_association_shop',
  // };
  // const marketPath = marketPathMap[type];

  const api = apiMap[type];
  if (!api) {
    throw new Error('Unsupported type');
  }

  try {
    const result = await api({}, originId);
    return getResponseResult(result).data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg !== 'Network Error') {
      MessagePlugin.error(errMsg);
    }
    throw error;
  }
};

const debounceGetInfo = lodash.debounce(() => getInfo(false), 800);

onMounted(() => {
  getInfo();
  // 初始化 ResizeObserver
  nextTick(() => {
    initResizeObserver();
  });
  // getReadCensus();
  // getSocietyArticleChannelInfo();
  window.addEventListener('focus', debounceGetInfo);
});

onUnmounted(() => {
  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  window.removeEventListener('focus', debounceGetInfo);
});

// 手动导航到指定入口
// 500ms 防抖 避免重复请求 下次优化
const navigateToEntry = lodash.debounce(async (entryType: string, externalAppId?: string) => {
  // if (!entries.value || entries.value.length === 0) {
  //   console.warn('AppEntry entries 数据未加载完成，无法导航到:', entryType);
  //   return false;
  // }
  await getInfo(false);

  const targetEntry = entries.value?.find((entry) => {
    if (entry.entryType === entryType && entry?.info?.uuid && externalAppId) {
      return entry?.info?.uuid === `external_app_app_${externalAppId}`;
    }
    return entry.entryType === entryType;
  });

  if (targetEntry) {
    console.log('导航到入口:', entryType, targetEntry);
    entryClick(targetEntry, true);
    return true;
  }

  console.warn('未找到对应的 appEntry:', entryType);
  return false;
}, 500);

// 暴露方法供父组件调用
defineExpose({
  entryClick,
  entries,
  navigateToEntry,
});
</script>

<template>
  <div class="apps-box" v-bind="$attrs">
    <div ref="appBoxRef" class="apps-list">
      <div
        v-for="(item, index) in displayEntries"
        ref="entryItemsRef"
        :key="index"
        class="flex-shrink-0 apps-item-wrap"
        :class="{ disabled: item.activeStatus !== ActiveStatus.Activated }"
        @click="entryClick(item)"
      >
        <entry-item
          class="apps-item"
          :item="item"
          :icon-dot="iconDot"
          :square="square"
        />
      </div>
      <div v-if="entries.length > maxDisplayCount" class="flex-shrink-0 apps-item-wrap" @click="toggleExpand">
        <div class="apps-item justify-center gap-[4px]">
          <span class="text-[#1A2139] text-[14px] font-bold">{{ isExpanded ? '收起' : `更多` }}</span>
          <t-icon :name="!isExpanded ? 'caret-down-small' : 'caret-up-small'" />
        </div>
      </div>
    </div>
  </div>
  <KefuEntry v-if="teamId && !props.hideKefu && kefuEnable" :team-id="teamId" @loaded="emit('kefu-loaded', $event)" />
</template>

<style scoped lang="less">
:deep(.t-badge--dot) {
  width: 10px;
  height: 10px;
}

.item-wrap {
  flex-shrink: 0;
  &:empty {
    display: none;
  }
}

.apps-box {
  width: 100%;
  margin-bottom: 24px;
  padding: 0 16px;
  .row {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    overflow-x: auto;
    .scrollbar2(8px, 2px);
    &::-webkit-scrollbar {
      height: 0px;
    }
    &:hover::-webkit-scrollbar {
      height: 8px;
    }
  }

  .disabled {
    :deep(.name) {
      color: var(--icon-kyy_color_icon_disabled, #D5DBE4) !important;
    }
  }

  &--list {
    max-height: 192px;
    overflow-y: auto;
  }
  &--more {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    background: var(--bg-kyy_color_bg_light, #FFF);
    flex-shrink: 0;
    &:hover {
      box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.12);
      cursor: pointer;
    }
    :global(.apps-box--more__item) {
      display: flex;
      align-items: center;
      color: #1A2139;
      font-size: 14px;
      padding: 8px;
    }
    :global(.apps-box--more__item > .name) {
      margin-left: 12px;
    }
    :global(.apps-box--more__item .icon),
    :global(.apps-box--more__item .icon img) {
      width: 20px;
      height: 20px;
    }
    :global(.apps-box--more__item iconpark-icon) {
      font-size: 20px;
    }
    :global(.apps-box--more__item:hover) {
      color:#4D5EFF;
      border-radius: 4px;
      background: #E1EAFF;
      cursor: pointer;
    }
  }
  :deep(.apps-item) {
    display: flex;
    padding: 8px 11px;
    align-items: center;
    height: 36px;
    border-radius: 4px;
    background: #fff;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .icon-bg-niche {
      background: var(--brand-kyy-color-brand-default, #4d5eff);
      img {
        width: 100%;
        height: 100%;
      }
    }
    .name {
      color: var(--lingke-black-90, #1a2139);
      font-feature-settings: "clig" off, "liga" off;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      margin-left: 10px;
    }
    .red-dot {
      position: relative;
      &:before {
        content: " ";
        position: absolute;
        left: 13px;
        top: -7px;
        width: 12px;
        height: 12px;
        border: 1px solid var(--kyy_color_badge_border, #FFF);
        background: var(--kyy_color_badge_bg, #FF4AA1);;
        border-radius: 50%;
      }
    }
  }
}
.apps-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  .apps-item-wrap {
    width: 110px;
    .apps-item {
      width: 100%;
    }
  }
  .apps-more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 36px;
    border-radius: 4px;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    background: var(--bg-kyy_color_bg_light, #FFF);
    cursor: pointer;
    color: var(--lingke-black-90, #1a2139);
    font-size: 14px;
    font-weight: 600;
    margin-top: 8px;
    transition: all 0.2s ease;
    &:hover {
      background: #f8f9fa;
      border-color: var(--brand-kyy-color-brand-default, #4d5eff);
      color: var(--brand-kyy-color-brand-default, #4d5eff);
    }
    .t-icon {
      margin-right: 6px;
      font-size: 16px;
      transition: transform 0.2s ease;
    }
    &:hover .t-icon {
      transform: scale(1.1);
    }
  }
}
</style>
