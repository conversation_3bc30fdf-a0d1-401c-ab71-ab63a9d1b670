<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from 'vue';
import '@amap/amap-jsapi-types';
import { Button as TButton, Link as TLink } from 'tdesign-vue-next';
import RkBaiduMapSelector from './BaiduMapSelector.vue';
import { BMap, BMarker } from 'vue3-baidu-map-gl';
import { markerIcon, BAIDU_AK, BAIDU_API_URL, openExternalMap } from './utils';
import type { Location, MapClickEvent, MapConfirmData, MapPoint } from './types';
import type { PropType } from 'vue';

// HACK: md5 is not defined
// https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
let moduleObject: any;
onBeforeMount(() => {
  moduleObject = (window as any).module;
  // eslint-disable-next-line no-global-assign
  (window as any).module = undefined;
});
const onBMapInitdHandler = () => {
  // eslint-disable-next-line no-global-assign
  (window as any).module = moduleObject;
};

const props = defineProps({
  /** 指定位置 */
  loc: {
    type: Object as PropType<Location>,
    default: () => ({}),
  },
  /** 是否可修改 */
  canUpdate: {
    type: Boolean,
    default: true,
  },
  /** 是否显示为小尺寸 */
  mini: {
    type: Boolean,
    default: false,
  },
  /** 是否显示为大尺寸 */
  large: {
    type: Boolean,
    default: false,
  },
  /** 地图高度 */
  height: {
    type: [String, Number],
    default: 200,
  },
  /** 卡片是否可删除 */
  removable: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  confirm: [data: MapConfirmData];
  remove: [];
  'open-external-map': [url: string];
}>();

// 兼容默认值
const location = ref<Location>(props.loc ?? {});
const mapHeight = computed(() => {
  const { height, mini } = props;
  if (height) return height;
  if (mini) return 96;
  return '100%';
});
const mapVisible = ref(false);

// 地图中心点
const center = ref<MapPoint>();
// 地图缩放级别
const zoom = ref(10);

// 标记点坐标
const markerPoint = ref<MapPoint | undefined>(center.value);

// 地图初始化
const mapInit = () => {
  onBMapInitdHandler();
};

// 重新选择地点
const mapConfirm = (data: MapConfirmData) => {
  const { lat, lng } = data.location;
  location.value.latLng = {
    longitude: lng,
    latitude: lat,
  };
  location.value.name = data.name;
  location.value.address = data.address || data.name;

  mapClick({ latlng: data.location });
  emit('confirm', {
    ...location.value,
    ...data,
  });
};

// 点击地图设置新的标记点，并获取其详情地址
const mapClick = ({ latlng }: MapClickEvent, isUserClick = false) => {
  markerPoint.value = latlng;
  center.value = latlng;
  zoom.value = 18;

  if (!props.canUpdate && isUserClick) {
    openExternalMap(
      { lng: latlng.lng, lat: latlng.lat, name: location.value.name || '定位地址', title: location.value.name || '定位地址' },
      (url: string) => emit('open-external-map', url)
    );
  }
};

// 定位到指定位置
watch(() => props.loc, (val) => {
  if (!val || !val.latLng) {
    delete location.value.latLng;
    return;
  }

  location.value = val;
  const { longitude, latitude } = val.latLng;
  if (longitude && latitude) {
    mapClick({ latlng: { lat: latitude, lng: longitude } });
  }
}, { immediate: true, deep: true });
</script>

<template>
  <div :class="large ? 'block' : 'inline-block'">
    <div
      v-if="location.latLng?.latitude"
      class="map-card"
      :class="{ mini, large }"
    >
      <div class="header">
        <div class="address-wrap">
          <div class="name">
            <img
              src="./assets/icon_orientation.svg"
              alt=""
              class="icon"
            >
            {{ location.name }}
          </div>
          <div class="address">
            {{ location.address || '--' }}
          </div>
        </div>

        <template v-if="canUpdate">
          <t-link
            theme="primary"
            hover="color"
            @click="mapVisible = true"
          >
            修改
          </t-link>
          <img
            v-if="removable"
            src="./assets/icon_close_circle.svg"
            alt=""
            class="icon remove"
            @click="emit('remove')"
          >
        </template>
      </div>

      <div class="map-container">
        <BMap
          :ak="BAIDU_AK"
          :api-url="BAIDU_API_URL"
          :center="center"
          :zoom="zoom"
          :height="mapHeight"
          enable-scroll-wheel-zoom
          @initd="mapInit"
          @click="(e) => mapClick(e, true)"
        >
          <BMarker
            v-if="markerPoint"
            :enable-clicking="false"
            :position="markerPoint"
            :offset="{ x: -14, y: -48 }"
            :icon="markerIcon"
          />
        </BMap>
      </div>
    </div>

    <t-button
      v-else-if="canUpdate"
      variant="outline"
      theme="primary"
      class="plain"
      @click="mapVisible = true"
    >
      <img
        src="./assets/icon_orientation.svg"
        alt=""
        class="icon"
      >选择地址
    </t-button>

    <RkBaiduMapSelector
      v-if="mapVisible"
      v-model:visible="mapVisible"
      :loc="loc"
      @confirm="mapConfirm"
    />
  </div>
</template>

<style scoped lang="less">
@import './styles.less';

.map-card {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);
  width: 440px;

  &.mini {
    width: 240px;
    .map-container {
      height: 96px;
    }
  }

  &.large {
    width: 100%;
  }
}

.header {
  display: flex;
  height: 64px;
  padding: 8px 12px;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  .address-wrap {
    flex: 1;
    width: 0;
  }
  .name {
    display: flex;
    color: var(--text-kyy-color-text-2, #516082);
    .ellipsis();
  }
  .address {
    color: var(--text-kyy-color-text-3, #828DA5);
    .ellipsis();
  }
  .icon {
    width: 20px;
    height: 20px;
  }
  &:hover .remove {
    opacity: 1;
  }
  .remove {
    position: absolute;
    right: 0;
    top: -10px;
    opacity: 0;
    cursor: pointer;
  }
}

.map-container {
  width: 100%;
  height: 164px;
}
</style>
