import type { Ref } from 'vue';
import { computed, ref, watch } from 'vue';

export interface ImageItem {
  url: string;
  uploading?: boolean;
  progress?: number;
  tempId?: string;
  failed?: boolean;
}

export interface UseImageUploadStateOptions {
  maxCount: number;
  onStateChange?: (urls: string[]) => void;
}

export function useImageUploadState(
  modelValue: Ref<string | string[]>,
  options: UseImageUploadStateOptions,
) {
  const { maxCount, onStateChange } = options;

  const images = ref<ImageItem[]>([]);
  const originalImages = ref<{ url: string }[]>([]);

  // 生成唯一ID
  const generateUniqueId = () => Math.random().toString(36).substr(2, 9);

  // 计算是否可以上传
  const canUpload = computed(() => {
    const currentCount = images.value?.length || 0;
    return currentCount < maxCount;
  });

  // 添加上传占位符
  const addUploadingItem = () => {
    const uploadingItem: ImageItem = {
      url: '',
      uploading: true,
      progress: 0,
      tempId: generateUniqueId(),
    };

    images.value = [...(images.value || []), uploadingItem];
    return {
      item: uploadingItem,
      index: images.value.length - 1,
    };
  };

  // 更新上传项状态
  const updateUploadingItem = (index: number, tempId: string, url: string) => {
    if (
      images.value &&
      images.value[index] &&
      images.value[index].tempId === tempId
    ) {
      const newImages = [...images.value];
      newImages[index] = {
        url,
        uploading: false,
        progress: 100,
        tempId,
      };
      images.value = newImages;

      // 触发状态更新
      updateState();
      return true;
    }
    return false;
  };

  // 移除上传项
  const removeUploadingItem = (index: number) => {
    if (images.value && images.value[index]) {
      images.value.splice(index, 1);
    }
  };

  // 移除图片
  const removeImage = (index: number) => {
    if (!images.value || !Array.isArray(images.value)) {
      images.value = [];
      return;
    }

    images.value.splice(index, 1);

    if (originalImages.value && originalImages.value[index]) {
      originalImages.value.splice(index, 1);
    }

    updateState();
  };

  // 更新进度
  const updateProgress = (index: number, progress: number) => {
    if (images.value && images.value[index] && images.value[index].uploading) {
      images.value[index].progress = parseInt(String(progress), 10);
    }
  };

  // 更新状态到父组件
  const updateState = () => {
    const urls = images.value.map((v) => v?.url).filter(Boolean);
    modelValue.value = urls;
    originalImages.value = images.value.map((item) => ({ url: item.url }));
    onStateChange?.(urls);
  };

  // 监听外部 modelValue 变化
  watch(
    () => modelValue.value,
    (newVal) => {
      // 如果有文件正在上传，跳过此次更新以避免状态冲突
      if (images.value && images.value.some((item) => item.uploading)) {
        return;
      }

      if (!newVal || (Array.isArray(newVal) && newVal.length === 0)) {
        images.value = [];
        originalImages.value = [];
        return;
      }

      // 统一处理为数组格式
      let newImages: ImageItem[] = [];
      if (typeof newVal === 'string') {
        newImages = [{ url: newVal, uploading: false }];
      } else if (Array.isArray(newVal)) {
        newImages = newVal.map((url) => ({
          url: url as string,
          uploading: false,
        }));
      }

      images.value = newImages;
      originalImages.value = newImages.map((item) => ({ url: item.url }));
    },
    { immediate: true },
  );

  return {
    images,
    originalImages,
    canUpload,
    addUploadingItem,
    updateUploadingItem,
    removeUploadingItem,
    removeImage,
    updateProgress,
    updateState,
  };
}
