<script setup lang="ts">
import { computed, PropType, ref } from 'vue';
import omit from 'lodash/omit';
import Cropper from 'cropperjs';
import { MessagePlugin, Upload as TUpload, Dialog as TDialog, Button as TButton, Loading as VLoading } from 'tdesign-vue-next';
import { UploadFile } from 'tdesign-vue-next/es/upload/type';
import isFunction from 'lodash/isFunction';
import RkImageCropper from './ImageCropper.vue';
import { uploadToOSS } from '../../utils/upload';
import { urlToBase64, checkImageLoaded, getImageInfo } from '../../utils/image';
import { isHttpUrl } from '../../utils/validate';
import { blobToImage, readFileAsDataURL } from '../../utils/file';
import enlargeIcon from './icon/enlarge.svg';
import reduceIcon from './icon/reduce.svg';

const IMAGE_MIN_WIDTH = 20;
const IMAGE_MIN_HEIGHT = 20;

type ImgType = string | Blob | null | File;
type Options = Cropper.Options;

const props = defineProps({
  /** cropperjs 默认选项 */
  options: {
    type: Object as PropType<Options>,
    default: () => ({
      // 限制裁剪框不超过画布的大小
      viewMode: 1,
      // 定义裁剪框的固定纵横比。默认情况下，裁剪框具有自由比例
      aspectRatio: 1,
      // 显示裁剪框上方的虚线
      guides: false,
      // 它应该是一个0到1之间的数字。定义自动裁剪区域的大小（百分比）
      autoCropArea: 1,
    }),
  },
  /** 标题 */
  title: {
    type: String,
    default: '编辑图片',
  },
  /** 输出宽度 */
  outputWidth: {
    type: Number,
    default: 0,
  },
  /** 输出高度 */
  outputHeight: {
    type: Number,
    default: 0,
  },
  /** 文件类型 */
  accept: {
    type: String,
    default: 'image/jpg,image/jpeg,image/png,image/gif',
  },
  /** 文件大小限制 */
  sizeLimit: {
    type: [Number, Object],
    default: () => 0,
  },
  /** 自定义 beforeUpload 函数 */
  beforeUpload: {
    type: Function,
    default: null,
  },
});

const emit = defineEmits(['confirm']);

const visible = ref(false);
const inSrc = ref('');

// cropper 选项
const cropperOptions = ref<Options>(props.options);
// 内部用选项
const otherOptions = ref(omit(props, 'options'));

const loading = ref(true);
const refCropper = ref();
const refreshKey = ref(0);

// 裁剪尺寸信息
const cropInfo = ref();
const onCropper = (data: { info: Cropper.Data }) => {
  cropInfo.value = data.info;
};

// 输出尺寸信息
const outSizeInfo = computed(() => {
  const { outputWidth, outputHeight } = otherOptions.value;
  const aspectRatio = cropperOptions.value.aspectRatio || 1;

  if (outputWidth && outputHeight) return { width: outputWidth, height: outputHeight };
  if (outputWidth) return { width: outputWidth, height: outputWidth / aspectRatio };
  if (outputHeight) return { width: outputHeight * aspectRatio, height: outputHeight };
  return cropInfo.value;
});
const sizeInfo = computed(() => `${outSizeInfo.value.width.toFixed(0)} x ${outSizeInfo.value.height.toFixed(0)}`);

// 裁剪框初始化完成
const onCropperReady = () => {
  loading.value = false;
  refCropper.value.handleCropper('setCropBoxData', { top: 0 });
};

// 裁剪图片，将裁剪后的图片上传到 OSS，并返回上传后的图片地址
const confirm = async () => {
  // 构建裁剪参数
  const { outputWidth, outputHeight } = otherOptions.value;
  const cropData: Cropper.GetCroppedCanvasOptions = {
    imageSmoothingQuality: 'high',
    ...(outputWidth && { width: outputWidth }),
    ...(outputHeight && { height: outputHeight }),
  };

  loading.value = true;

  try {
    // 获取裁剪后的图片数据
    const canvas = refCropper.value.cropper.getCroppedCanvas(cropData);
    const blob = await new Promise<Blob>((resolve, reject) => {
      canvas.toBlob((blob: Blob | null) => {
        if (blob) resolve(blob);
        else reject(new Error('Failed to generate cropped image blob'));
      });
    });

    // 转换为图片并上传
    const image = blobToImage(blob, 'cropper-img');
    const res = await uploadToOSS(image);

    // 触发事件并关闭对话框
    emit('confirm', res.url, res);

    visible.value = false;
    loading.value = false;
  } catch (error) {
    console.error('Error during confirmation process:', error);
    loading.value = false;
  }
};

// 放大缩小
const zoom = (value: number) => refCropper.value.handleCropper('zoom', value);

// 图片上传
const defaultBeforeUpload = async (file: UploadFile) => {
  // 检查自定义 beforeUpload 函数
  const beforeUpload = otherOptions.value.beforeUpload;
  if (isFunction(beforeUpload)) {
    const valid = await safeCall(beforeUpload, file);
    if (!valid) return false;
  }

  try {
    // 获取图片信息并验证尺寸
    const info = await getImageInfo(file.raw || new File([], ''));
    if (info.width <= IMAGE_MIN_WIDTH || info.height <= IMAGE_MIN_HEIGHT) {
      await MessagePlugin.warning(`请上传大于${IMAGE_MIN_WIDTH}*${IMAGE_MIN_HEIGHT}px的图片`);
      return false;
    }

    // 验证文件大小限制
    const { sizeLimit } = otherOptions.value;
    if (sizeLimit) {
      const isNumber = typeof sizeLimit === 'number';
      const size = isNumber ? sizeLimit : sizeLimit.size;
      const msg = isNumber || !sizeLimit.message ? '图片尺寸不符合要求' : sizeLimit.message.replace(/\{sizeLimit\}/g, String(size));

      const overSize = file.size > 1024 * 1024 * size;
      if (overSize) {
        await MessagePlugin.warning(msg);
        return false;
      }
    }

    // 处理文件读取
    inSrc.value = await readFileAsDataURL(file.raw || new File([], ''));

    refreshKey.value++;
    visible.value = true;

    return true;
  } catch (error) {
    console.error('文件上传前检查过程中发生错误:', error);
    return false;
  }
};

const safeCall = async (fn: Function, ...args: any[]) => {
  try {
    return await fn(...args);
  } catch (error) {
    console.error('调用函数时发生错误:', error);
    return false;
  }
};

const uploaderRef = ref(null);

/**
 * 打开图片裁剪弹框
 */
const open = async (img: ImgType, options: typeof props) => {
  // 支持命令式传参
  cropperOptions.value = {
    // 限制裁剪框不超过画布的大小
    viewMode: 1,
    // 定义裁剪框的固定纵横比。默认情况下，裁剪框具有自由比例
    aspectRatio: 1,
    // 显示裁剪框上方的虚线
    guides: false,
    // 它应该是一个0到1之间的数字。定义自动裁剪区域的大小（百分比）
    autoCropArea: 1,
    ...cropperOptions.value,
    ...options?.options || {},
  };
  otherOptions.value = {
    ...otherOptions.value,
    ...omit(options || {}, 'options') || {},
  };

  // 未传 img 则先自动打开文件选择器选择图片
  if (!img) {
    uploaderRef.value.triggerUpload();
    return;
  }

  loading.value = true;

  try {
    if (typeof img === 'string' && isHttpUrl(img)) {
      // 检查图片是否加载成功
      const loaded = await checkImageLoaded(img);
      if (!loaded) {
        uploaderRef.value.triggerUpload();
        return;
      }

      // 将远程图片转为 Base64，避免跨域问题
      img = await urlToBase64(img);
    }

    // UploadFile 类型
    if (typeof img === 'object') {
      defaultBeforeUpload(img);
      return;
    }

    // 已上传的图片地址
    inSrc.value = img as string;
    refreshKey.value++;
    visible.value = true;
  } catch (error) {
    console.error('Error occurred while opening the image cropper:', error);
  } finally {
    loading.value = false;
  }
};

const close = () => {
  visible.value = false;
};

defineExpose({
  open,
  close,
});
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    width="600"
    prevent-scroll-through
    attach="body"
    :header="otherOptions?.title"
    :footer="false"
    :close-on-overlay-click="false"
    :z-index="2700"
    class="rk-cropper-dialog"
  >
    <div
      v-loading="loading"
      class="content"
    >
      <RkImageCropper
        ref="refCropper"
        :key="refreshKey"
        :src="inSrc"
        :options="cropperOptions"
        height="296px"
        @cropper="onCropper"
        @readied="onCropperReady"
      />

      <div
        v-if="outSizeInfo"
        class="crop-info"
      >
        {{ sizeInfo }}
      </div>
    </div>

    <div class="footer">
      <slot name="re-upload">
        <t-upload
          ref="uploaderRef"
          theme="custom"
          allow-upload-duplicate-file
          :accept="accept"
          :max="10"
          :show-file-list="false"
          :before-upload="defaultBeforeUpload"
        >
          <t-button variant="outline">
            重新上传
          </t-button>
        </t-upload>
      </slot>

      <div class="zoom-wrap">
        <div
          class="item"
          @click="zoom(0.1)"
        >
          <img
            :src="enlargeIcon"
            class="icon"
          >
        </div>
        <div
          class="item"
          @click="zoom(-0.1)"
        >
          <img
            :src="reduceIcon"
            class="icon"
          >
        </div>
      </div>

      <t-button
        class="ml-auto"
        :loading="loading"
        :disabled="loading"
        @click="confirm"
      >
        确定
      </t-button>
    </div>
  </t-dialog>
</template>

<style lang="less">
.rk-cropper-dialog {
  .t-dialog__body {
    padding-bottom: 0;
  }

  .content {
    position: relative;
    width: 100%;
    height: 296px;
    background: #1c222b;
    .crop-info {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-flex;
      padding: 4px 8px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: var(--bg-kyy-color-bg-mask, rgba(0, 0, 0, 0.50));
      color: #fff;
      font-size: 12px;
      line-height: 20px;
    }
  }

  .footer {
    margin-top: 22px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .zoom-wrap {
    width: 96px;
    padding: 8px 16px;
    border-radius: 100px;
    background: var(--bg-kyy-color-bg-deep, #F5F8FE);
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    .item {
      position: relative;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
    }
    .icon {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
