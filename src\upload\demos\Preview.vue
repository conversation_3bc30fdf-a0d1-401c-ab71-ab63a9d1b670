/**
 * title: 预览
 * description: 自定义图片预览。此处用 `ImageViewer` 演示，如需调用 Electron SDK 预览，请自行使用 `@click` 事件返回的数据处理。
 */

<template>
  <div class="demo-wrap">
    <ImageViewer
      v-model:visible="visible"
      v-model:index="previewIndex"
      :images="imgUrls"
    >
      <template #trigger>
        <RkUploadImage
          v-model="imgUrls"
          root-dir="换成上传OSS根目录"
          @click="previewImage"
        />
      </template>
    </ImageViewer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkUploadImage } from '@rk/unitPark';
import { ImageViewer } from 'tdesign-vue-next';

const imgUrls = ref([
  'https://tdesign.gtimg.com/demo/demo-image-1.png',
  'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/shop_apply_bg.png',
  'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/shop/upload_logo_example.png',
]);

const previewImgs = ref([]);
const previewIndex = ref(0);
const visible = ref(false);

const previewImage = ({ images, index, url }: { images: string[]; index: number; url: string }) => {
  previewImgs.value = images;
  previewIndex.value = index;
  visible.value = true;
};
</script>

<style scoped lang="less">
.demo-wrap {
  display: flex;
  align-items: flex-start;
}
</style>
