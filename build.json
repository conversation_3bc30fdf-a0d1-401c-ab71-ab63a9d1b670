{"asar": false, "extraFiles": [], "publish": [{"provider": "generic", "url": "http://127.0.0.1"}], "productName": "electron-vue-template", "appId": "org.sky.electron-vue-template", "directories": {"output": "build"}, "files": ["dist/electron/**/*"], "dmg": {"contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"icon": "build/icons/icon.icns"}, "win": {"icon": "build/icons/icon.ico"}, "linux": {"icon": "build/icons"}}