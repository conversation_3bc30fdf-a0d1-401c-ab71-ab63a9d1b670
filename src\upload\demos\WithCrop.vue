/**
 * title: 图片裁剪
 * description: 支持图片裁剪功能
 */
<template>
  <RkUploadImage
    v-model="imageUrls"
    root-dir="换成上传OSS根目录"
    enable-crop
    :max-count="3"
    :cropper-props="{
      title: '自定义裁剪标题',
      outputWidth: 300,
      outputHeight: 300,
      options: {
        aspectRatio: 1,
        viewMode: 1
      }
    }"
    @crop-confirm="onCropConfirm"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RkUploadImage } from '@rk/unitPark';

const imageUrls = ref([]);

const onCropConfirm = (croppedUrl: string, originalUrl: string) => {
  console.log('裁剪完成:', croppedUrl, originalUrl);
};
</script>