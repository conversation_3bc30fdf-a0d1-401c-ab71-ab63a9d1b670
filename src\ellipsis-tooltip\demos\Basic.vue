<script setup lang="ts">
import RkEllipsisTooltip from '../index.vue';

const text1 = '这是一段很长的文本内容，可能会超出容器的宽度，需要使用省略号显示';

const text2 = '这是一段更长的文本内容，用来测试多行文本省略效果。当文本内容超过指定的行数时，会自动进行截断处理，并在鼠标悬停时显示完整的文本内容。';

const tooltipContent = '这里是 tooltip 中显示的详细信息，可以包含更多的描述文字，帮助用户了解完整的内容信息。';
</script>

<template>
  <div class="demo-container">
    <h3>基础用法</h3>
    <p>当文本超出容器宽度时，自动显示省略号，鼠标悬停显示完整内容。</p>
    <div class="demo-section">
      <div class="text-container">
        <RkEllipsisTooltip :text="text1" />
      </div>

      <div class="text-container mt-16">
        <RkEllipsisTooltip text="短文本" />
      </div>
    </div>

    <h3>自定义 tooltip 内容</h3>
    <p>可以为 tooltip 设置不同于显示文本的内容。</p>
    <div class="demo-section">
      <div class="text-container">
        <RkEllipsisTooltip
          :text="text1"
          :content="tooltipContent"
        />
      </div>
    </div>

    <h3>多行文本省略</h3>
    <p>支持多行文本的省略显示。</p>
    <div class="demo-section">
      <div class="text-container">
        <RkEllipsisTooltip
          :line-number="2"
          :text="text2"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@import '../../demo-common.less';

.text-container {
  width: 200px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  background: #f5f5f5;
  position: relative;
}

.mt-16 {
  margin-top: 16px;
}
</style>
