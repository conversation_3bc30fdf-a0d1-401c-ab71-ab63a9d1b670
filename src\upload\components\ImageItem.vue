<script setup lang="ts">
import { Image as TImage, Loading as TLoading } from 'tdesign-vue-next';
import type { ImageItem } from '../type';
import closeIcon from '../icon/close.svg';

const props = defineProps<{
  item: ImageItem;
  index: number;
  readonly?: boolean;
}>();

const emit = defineEmits<{
  (e: 'click', index: number, url: string): void;
  (e: 'remove', index: number): void;
}>();

const handleClick = () => {
  if (props.item?.url) {
    emit('click', props.index, props.item.url);
  }
};

const handleRemove = () => {
  emit('remove', props.index);
};
</script>

<template>
  <div class="img-wrap">
    <!-- 上传中的占位符 -->
    <div
      v-if="item.uploading"
      class="upload-placeholder"
    >
      <div class="upload-progress">
        <t-loading size="22px" />
        <p class="progress-text">上传中{{ item.progress || 0 }}%</p>
      </div>
    </div>

    <!-- 已上传的图片 -->
    <t-image
      v-else-if="item?.url"
      :src="item?.url"
      fit="cover"
      class="img"
      @click="handleClick"
    >
      <template #error>
        <div class="image-error">
          <span>加载失败</span>
        </div>
      </template>
      <template #loading>
        <div class="loading-wrap">
          <t-loading size="small" />
        </div>
      </template>
    </t-image>

    <img
      v-if="!readonly && !item.uploading"
      :src="closeIcon"
      class="btn-close"
      @click="handleRemove"
    >
  </div>
</template>

<style lang="less" scoped>
.img-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 78px;
  height: 78px;
  border: 1px solid transparent;
  border-radius: 8px;
  position: relative;

  &:hover .btn-close {
    opacity: 1;
  }

  &.draggable {
    cursor: move;
  }

  &.dragging {
    opacity: 0.5;
  }

  .img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    user-select: none;
    cursor: zoom-in;
  }

  .btn-close {
    opacity: 0;
    position: absolute;
    top: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    z-index: 1;
    box-shadow: 0 0 1.6px rgba(#fff, 0.16);
    border-radius: 50%;
  }

  .upload-progress {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    display: flex;
    gap: 4px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--kyy_color_upload_text_default, #516082);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
  }

  .progress-text {
    margin: 0;
    color: var(--kyy_color_upload_text_disabled, #ACB3C0);
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
  }
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
  background: var(--kyy_color_upload_bg, #FFF);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-wrap {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}

.image-error {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--kyy_color_upload_bg, #FFF);
  border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
  color: var(--text-kyy-color-text-3, #828da5);
  font-size: 12px;
}
</style>
