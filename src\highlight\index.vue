<template>
  <span class="rk-highlight">
    <template
      v-for="(part, idx) in parts"
      :key="idx"
    >
      <span
        v-if="part.highlight"
        :class="['rk-highlight__text', highlightClass]"
        :style="highlightStyle"
      >{{ part.text }}</span>
      <span
        v-else
        class="rk-highlight__normal"
      >{{ part.text }}</span>
    </template>
  </span>
</template>

<script setup lang="ts">
import { computed, CSSProperties, PropType } from 'vue';

export interface RkHighlightTextPart {
  /** 文本 */
  text: string;
  /** 是否高亮 */
  highlight: boolean;
}

const props = defineProps({
  /** 内容文本 */
  content: {
    type: String,
    required: true,
  },
  /** 高亮关键词 */
  keyword: {
    type: String,
    default: '',
  },
  /** 自定义高亮类名 */
  highlightClass: {
    type: String,
    default: '',
  },
  /** 自定义高亮样式 */
  highlightStyle: {
    type: Object as PropType<CSSProperties>,
    default: () => ({}),
  },
  /** 是否忽略大小写 */
  ignoreCase: {
    type: Boolean,
    default: true,
  },
  /** 是否启用全词匹配 */
  wholeWord: {
    type: Boolean,
    default: false,
  },
});

/**
 * 转义正则表达式特殊字符
 */
const escapeRegExp = (text: string): string => text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

/**
 * 构建正则表达式
 */
const buildRegex = (keyword: string): RegExp => {
  const escapedKeyword = escapeRegExp(keyword);
  const pattern = props.wholeWord ? `\\b${escapedKeyword}\\b` : escapedKeyword;
  const flags = props.ignoreCase ? 'gi' : 'g';
  return new RegExp(pattern, flags);
};

/**
 * 分割文本并标记高亮部分
 */
const parts = computed<RkHighlightTextPart[]>(() => {
  const { content, keyword } = props;

  // 没有关键词或内容为空时直接返回
  if (!keyword || !content) {
    return [{ text: content, highlight: false }];
  }

  try {
    const regex = buildRegex(keyword);
    const result: RkHighlightTextPart[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    // 匹配所有关键词
    while ((match = regex.exec(content)) !== null) {
      // 添加匹配前的普通文本
      if (match.index > lastIndex) {
        result.push({
          text: content.slice(lastIndex, match.index),
          highlight: false,
        });
      }

      // 添加高亮文本
      result.push({
        text: match[0],
        highlight: true,
      });

      lastIndex = match.index + match[0].length;

      // 防止无限循环
      if (match[0].length === 0) {
        break;
      }
    }

    // 添加剩余的普通文本
    if (lastIndex < content.length) {
      result.push({
        text: content.slice(lastIndex),
        highlight: false,
      });
    }

    return result;
  } catch (error) {
    // 正则表达式错误时返回原始内容
    console.warn('Highlight component regex error:', error);
    return [{ text: content, highlight: false }];
  }
});
</script>

<style lang="less" scoped>
.rk-highlight {
  display: inline;
  word-break: break-all;
  &__text {
    color: var(--brand-kyy_color_brand_default, #4d5eff);
  }
}
</style>
