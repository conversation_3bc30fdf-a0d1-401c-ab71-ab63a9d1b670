import {
  app,
  BrowserWindow,
  Menu,
  dialog,
  Tray,
  nativeImage,
  screen,
  ipcMain,
  shell,
} from 'electron';
import { getSDK, getRandomUUID } from '@lynker-desktop/electron-sdk/main';
import { getInvokeQuery, handleSchemeWakeup } from '@main/services/protocol';
import { handleError } from '@main/services/window/utils';
import { showDevTool } from '@main/env';
import { dbInit } from '@main/message/dbManager';
import { ipcMainHandle } from '@main/utils';
import { apmReport } from '../../renderer/utils/apm';
import { IsUseSysTitle, UseStartupChart } from '../config/const';
import menuconfig from '../config/menu';
import { winURL, getRendererWindowEntry, loadingURL } from '../config/StaticPath';
import {
  mainWindowConfig,
  loginWindowConfig,
  settingWindowConfig,
  previewWindowConfig,
} from '../config/windowsConfig';
import { windowManager, isValidWindow } from './windowManager';
import { switchRegister, initSearch, setSecondInstanceParams } from './shortcut';
// import { logHandler } from "../../renderer/log";
import { setPopBvOther } from './windowBv';
import { cleanupAllProcesses } from '../index'
const Store = require('electron-store');

const reLoadUrl = [
  {
    key: 'popBv',
    url: getRendererWindowEntry('windows/popBv/index.html'),
    num: 3,
  },
];
// 获取未处理的 reject 并将其扔给我们已有的另一个回退处理程序
process.on('unhandledRejection', (err: Error) => {
  apmReport({
    name: `node:unhandledRejection:${err.name}: ${err.message}`,
    message: `${err.stack}`,
  });
});

process.on('uncaughtException', (err: Error) => {
  apmReport({
    name: `node:uncaughtException:${err.name}: ${err.message}`,
    message: `${err.stack}`,
  });
});
process.on('error', (err: Error) => {
  apmReport({
    name: `node:sync:${err.name}: ${err.message}`,
    message: `${err.stack}`,
  });
});

/**
 * 渲染进程崩溃次数
 */
const maxRetryCount = 10;

// 当确定渲染进程卡死时，分类型进行告警操作
app.on('render-process-gone', (event, webContents, details) => {
  try {
    const url = webContents.getURL?.() || '';
    const win = BrowserWindow.fromWebContents(webContents);
    const sdkWin = getSDK().windowManager.get(win?.id);

    // 崩溃计数容错
    const wcAny = webContents as any;
    if (!('__crashCount__' in wcAny)) {
      wcAny.__crashCount__ = 0;
      wcAny.on('did-start-loading', () => {
        wcAny.__crashCount__ = 0;
      });
      wcAny.on('did-finish-load', () => {
        wcAny.__crashCount__ = 0;
      });
    }
    wcAny.__crashCount__ += 1;

    // 日志上报，明确 details.reason 类型
    apmReport({
      name: `render-process-gone: ${details.reason}`,
      message: `crashCount: ${wcAny.__crashCount__}; url: ${url}; type: ${webContents.getType?.()}; details: ${JSON.stringify(details)}`,
    });
    console.log('====>render-process-gone', JSON.stringify({
      id: sdkWin?.id,
      name: sdkWin?._name,
      details,
    }, null, 2));
    // 开发环境下弹窗提示 reason 类型
    // if (showDevTool && win) {
    //   dialog.showMessageBox(win, {
    //     type: 'info',
    //     title: '渲染进程异常类型',
    //     message: `details.reason: ${details.reason}`,
    //     detail: JSON.stringify(details, null, 2),
    //     noLink: true,
    //   });
    // }

    // 自动恢复阈值
    const retryThreshold = maxRetryCount;

    // 优先自动恢复
    if (wcAny.__crashCount__ <= retryThreshold) {
      if (webContents.getType?.() === 'browserView' || (sdkWin?._name !== 'mainWindow' && sdkWin?._name !== 'loginWindow')) {
        webContents.stop();
        webContents.reload();
        return;
      }
    }

    // 超过阈值，友好提示
    if (win && !app.isPackaged) {
      dialog.showMessageBox(win, {
        type: 'warning',
        title: '进程异常',
        buttons: ['重试', '退出'],
        message: `检测到窗口多次异常（类型：${details.reason}），是否尝试重启？`,
        noLink: true,
      }).then((res) => {
        if (res.response === 0) {
          wcAny.__crashCount__ = 0;
          webContents.reload();
        } else {
          cleanupAllProcesses();
          app.relaunch();
          setTimeout(() => {
            app.exit(0);
          }, 0);
        }
      }).catch((err) => {
        apmReport({
          name: 'dialog-showMessageBox-error',
          message: err?.stack || String(err),
        });
        app.relaunch();
        setTimeout(() => {
          app.exit(0);
        }, 0);
      });
    }
  } catch (error) {
    apmReport({
      name: 'render-process-gone-handler-error',
      message: error?.stack || String(error),
    });
  }
});
/**
 * 新的gpu崩溃检测，详细参数详见：http://www.electronjs.org/docs/api/app
 * @returns {void}
 * <AUTHOR> (umbrella22)
 * @date 2020-11-27
 */
app.on('child-process-gone', (event, details) => {
  // log.error(`APP-ERROR:child-process-gone;event: ${JSON.stringify(event)}; details:${JSON.stringify(details)}; killed:${JSON.stringify(details.reason === 'killed')}`);

  const message = {
    title: '',
    buttons: [],
    message: '',
  };
  switch (details.type) {
    case 'GPU':
      switch (details.reason) {
        case 'crashed':
          message.title = '警告';
          message.buttons = ['确定', '退出'];
          message.message = '硬件加速进程已崩溃，是否关闭硬件加速并重启？';
          break;
        case 'killed':
          message.title = '警告';
          message.buttons = ['确定', '退出'];
          message.message = '硬件加速进程被意外终止，是否关闭硬件加速并重启？';
          break;
        default:
          break;
      }
      break;

    default:
      break;
  }

  // 添加日志
  // logHandler({
  //   name: `main process: ${details.reason}`,
  //   info: `APP-ERROR:render-process-gone; event: ${JSON.stringify(event)}; details:${JSON.stringify(details)}`,
  //   desc: message.message
  // });
  // apmReport({
  //   name: `child process: ${details.reason}`,
  //   message: `APP-ERROR:render-process-gone; event: ${JSON.stringify(event)}; details:${JSON.stringify(details)}`
  // });
  const window = windowManager.mainWindow || windowManager.loginWindow;
  try {
    window.webContents.send('child-process-gone');
  } catch (error) {
    console.error('child-process-gone error: ', error);
  }
  dialog
    .showMessageBox(window, {
      type: 'warning',
      title: message.title,
      buttons: message.buttons,
      message: message.message,
      noLink: true,
    })
    .then((res) => {
      // 当显卡出现崩溃现象时使用该设置禁用显卡加速模式。
      if (res.response === 0) {
        if (details.type === 'GPU') {
          app.disableHardwareAcceleration();
        } else {
          window.reload();
        }
      } else {
        cleanupAllProcesses();
        app.relaunch();
        app.exit(0);
      }
    });
});

export const initWindow = (login: Boolean = false) => {
  // createMainWindow();
  if (UseStartupChart) {
    // return loadingWindow(loadingURL, login);
  }
  return login ? createMainWindow() : createLoginWindow();
};

export const onSecondInstance = (event, argv) => {
  const result = handleSchemeWakeup(argv) ?? { module: 'chat' };
  const moduleMap = {
    chat: '/main/message', // 消息
    square: '/square/friend-circle', // 广场号
    contacts: '/main/contactsIndex', // 联系人
    disk: '/clouddiskIndex/clouddiskhome', // 云盘
    approve: '/approvalIndex/approve_home', // 审批
    project: '/workBenchIndex/engineer_home', // 工程
    member: '/memberIndex/member_number', // 会员
    politics: '/politicsIndex/politics_number', // 城市
    niche: '/businessIndex/businessReleaseList', // 商机
    serve: '/serviceIndex/service_home', // 服务
    activities: '/activity/activityList', // 活动
    'chain-device': '/deviceIndex/device-list', // 设备
    'chain-customer': '/customerIndex/customer-list', // 客户
    'chain-supplier': '/supplierIndex/supplier-list', // 供应商
    'chain-partners': '/partnerIndex/partner-list', // 合作伙伴
  };
  const reloadUrl = moduleMap[result.module] || '/main/message';
  const params = { ...result, path: reloadUrl, query: global.invokeParams };

  const win = windowManager.mainWindow || windowManager.loginWindow;
  if (!isValidWindow(win)) {
    setSecondInstanceParams(params);
    initWindow();
  } else {
    win.show();
    if (win.isMinimized()) {
      win.restore();
    }
    win.focus();

    setTimeout(() => {
      try {
        win.webContents.send('on-second-instance-path', params);
      } catch (error) {
        console.error('onSecondInstance error: ', error);
      }
    }, 500);
  }
};

/*
const loadingWindow = (loadingURL: string, login: Boolean = false) => {
  windowManager.loadWindow = await getSDK().windowManager.create({
    name: 'loadingWindow',
    url: ``,
    browserWindow: {
      width: 400,
      height: 600,
      frame: false,
      skipTaskbar: true,
      transparent: true,
      resizable: false,
      webPreferences: { experimentalFeatures: true },
    }
  });

  windowManager.loadWindow.loadURL(loadingURL);
  windowManager.loadWindow.show();
  windowManager.loadWindow.setAlwaysOnTop(true);
  windowManager.loadWindow.on('closed', windowManager.closeLoadWindow)
  // 延迟两秒可以根据情况后续调快，= =，就相当于个，sleep吧，就那种。 = =。。。
  setTimeout(() => {
    login ? createMainWindow() : createLoginWindow();
  }, 1500);
}
*/

const createLoginWindow = async () => {
  if (windowManager.loginWindow && windowManager.loginWindow?.isDestroyed() === false) {
    return;
  }
  windowManager.destroyTray();
  const loginUrl = `${winURL}#account?${getInvokeQuery(global.invokeParams)}`;
  windowManager.loginWindow = await getSDK().windowManager.create({
    name: 'loginWindow',
    url: loginUrl,
    loadingView: {
      url: loadingURL,
    },
    browserWindow: {
      webPreferences: {
        webSecurity: false, // 解决跨越问题
        nodeIntegration: true, // 允许渲染进程使用 node 模块
        // enableRemoteModule: true, // 允许渲染进程使用 remote 模块
      },
      titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
      ...Object.assign(loginWindowConfig(), {}),
    },
  });
  windowManager.closeMainWindow();
  // 赋予模板
  const menu = Menu.buildFromTemplate(menuconfig as any);
  // 加载模板
  Menu.setApplicationMenu(menu);

  // 加载登陆窗口

  // 开发模式下自动开启devtools
  if (showDevTool) {
    windowManager.loginWindow.webContents.openDevTools({
      mode: 'undocked',
      activate: true,
    });
  }
  // 未初始化 预加载窗口未加载完成可先调用ready-to-show
  windowManager.loginWindow.once('ready-to-show', () => {
    windowManager.loginWindow.show();
    if (UseStartupChart && windowManager.loadWindow) {
      windowManager.closeLoadWindow();
    }
  });

  // 添加登录窗口全屏和恢复默认大小的事件监听
  windowManager.loginWindow.on('enter-full-screen', () => {
    console.log('登录窗口进入全屏模式');
    try {
      windowManager.loginWindow.webContents.send('window-fullscreen-changed', { isFullscreen: true });
    } catch (error) {
      console.error('发送登录窗口全屏事件失败:', error);
    }
  });

  windowManager.loginWindow.on('leave-full-screen', () => {
    console.log('登录窗口退出全屏模式');
    try {
      windowManager.loginWindow.webContents.send('window-fullscreen-changed', { isFullscreen: false });
    } catch (error) {
      console.error('发送登录窗口退出全屏事件失败:', error);
    }
  });

  windowManager.loginWindow.on('resize', () => {
    const bounds = windowManager.loginWindow.getBounds();
    const isMaximized = windowManager.loginWindow.isMaximized();
    const isFullScreen = windowManager.loginWindow.isFullScreen();

    try {
      windowManager.loginWindow.webContents.send('window-resize', {
        bounds,
        isMaximized,
        isFullScreen,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('发送登录窗口大小变化事件失败:', error);
    }
  });

  windowManager.loginWindow.on('maximize', () => {
    console.log('登录窗口最大化');
    try {
      windowManager.loginWindow.webContents.send('window-state-changed', {
        state: 'maximized',
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('发送登录窗口最大化事件失败:', error);
    }
  });

  windowManager.loginWindow.on('unmaximize', () => {
    console.log('登录窗口恢复默认大小');
    try {
      windowManager.loginWindow.webContents.send('window-state-changed', {
        state: 'normal',
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('发送登录窗口恢复默认大小事件失败:', error);
    }
  });

  // windowManager.loginWindow.on("closed", windowManager.closeLoginWindow);
  windowManager.preventClose(windowManager.loginWindow);
  handleError(windowManager.loginWindow, 'loginWindow');
  // 提前创建 popview
  windowManager.createPopView();
};

const windowFocusFunc = () => {
  try {
    console.log('====>browser-window-focus');
    switchRegister('search', true);
    // if (windowManager.messageView) {
    //   windowManager.messageView.webContents.send("im.real.invoke", { action: 'imLogin', data: { from: 'browser-window-focus' } });
    // }
    if (windowManager.mainWindow) {
      windowManager.mainWindow.webContents.send('mainWindow-focused');
    }

  } catch (error) {
    console.log('====>error', error);
  }
};
const windowBlurFunc = () => {
  switchRegister('search', false);
  if (windowManager.mainWindow) {
    windowManager.mainWindow.webContents.send('mainWindow-blurred');
  }
};
const createMainWindow = async () => {
  // 提前创建 popview
  await windowManager.createPopView();
  if (windowManager.mainWindow && windowManager.mainWindow?.isDestroyed() === false) {
    return;
  }
  windowManager.destroyTray();
  windowManager.mainWindow = await getSDK().windowManager.create({
    name: 'mainWindow',
    url: `${winURL}#/main/message`,
    loadingView: {
      url: loadingURL,
    },
    browserWindow: {
      ...mainWindowConfig(),
      webPreferences: {
        ...mainWindowConfig().webPreferences,
        preload: `${__dirname}/preload.js`,
        scrollBounce: false,
      },
      fullscreenable: true,
      titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
      transparent: true,
      frame: false,
      hasShadow: true,
      backgroundColor: '#ffffff',
    },
  });
  windowManager.createMessageView(() => {
    windowManager.mainWindow.loadURL(`${winURL}#/main/message?${getInvokeQuery(global.invokeParams)}`);

    if (showDevTool) {
      windowManager.mainWindow.webContents.openDevTools({
        mode: 'undocked',
        activate: true,
      });
    }
  });
  // 赋予模板
  const menu = Menu.buildFromTemplate(menuconfig as any);
  // 加载模板
  Menu.setApplicationMenu(menu);
  // ready-to-show之后显示界面
  // windowManager.mainWindow.once('ready-to-show', () => {
  setTimeout(() => {
    windowManager.mainWindow.show();
    // initSearch();
    destoryLoadWindow();
    windowManager.closeLoginWindow(); // 等加载完再销毁登录窗口不然当前没有存在的窗口会走win.close和app.quit
  }, 1000 * 3);
  // });

  // 添加窗口全屏和恢复默认大小的事件监听
  windowManager.mainWindow.on('enter-full-screen', () => {
    console.log('主窗口进入全屏模式');
    try {
      windowManager.mainWindow.webContents.send('window-fullscreen-changed', { isFullscreen: true });
    } catch (error) {
      console.error('发送全屏事件失败:', error);
    }
  });

  windowManager.mainWindow.on('leave-full-screen', () => {
    console.log('主窗口退出全屏模式');
    try {
      windowManager.mainWindow.webContents.send('window-fullscreen-changed', { isFullscreen: false });
    } catch (error) {
      console.error('发送退出全屏事件失败:', error);
    }
  });

  windowManager.mainWindow.on('resize', () => {
    const bounds = windowManager.mainWindow.getBounds();
    const isMaximized = windowManager.mainWindow.isMaximized();
    const isFullScreen = windowManager.mainWindow.isFullScreen();

    try {
      windowManager.mainWindow.webContents.send('window-resize', {
        bounds,
        isMaximized,
        isFullScreen,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('发送窗口大小变化事件失败:', error);
    }
  });

  windowManager.mainWindow.on('maximize', () => {
    console.log('主窗口最大化');
    try {
      windowManager.mainWindow.webContents.send('window-state-changed', {
        state: 'maximized',
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('发送最大化事件失败:', error);
    }
  });

  windowManager.mainWindow.on('unmaximize', () => {
    console.log('主窗口恢复默认大小');
    try {
      windowManager.mainWindow.webContents.send('window-state-changed', {
        state: 'normal',
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('发送恢复默认大小事件失败:', error);
    }
  });

  // windowManager.mainWindow.on("closed", windowManager.closeMainWindow);
  // lss暂时注释这一坨，不然我这边没法开发，阻断了进程

  app.removeListener('browser-window-focus', windowFocusFunc);
  app.removeListener('browser-window-blur', windowBlurFunc);
  app.on('browser-window-focus', windowFocusFunc);
  app.on('browser-window-blur', windowBlurFunc);
  windowManager.preventClose(windowManager.mainWindow);

  handleError(windowManager.mainWindow, 'mainWindow');
};

export const refreshMainWindow = (options) => {
  try {
   options.origin === 'changeAccount' ? null : windowManager.mainWindow.webContents.send('refresh-profiles-info');
    dbInit(options.openid);
    windowManager.mainWindow.loadURL(
      `${winURL}#/main/message?${getInvokeQuery(global.invokeParams)}`,
    );
    windowManager.createMessageView(async () => {
      windowManager.hideBvs();
      await windowManager.mainWindow.loadURL(
        `${winURL}#/main/message?${getInvokeQuery(global.invokeParams)}`,
      );
      // windowManager.mainWindow.webContents.reload();
      setPopBvOther({ show: false });
      windowManager.mainWindow.webContents.reloadIgnoringCache();
      setTimeout(() => {
        try {
          windowManager.mainWindow.webContents.send('refresh-account-tip');
        } catch (error) {
          console.error('refreshMainWindow error: ', error);
        }
      }, 3000);

      if (showDevTool) {
        // windowManager.mainWindow.webContents.openDevTools({
        //   mode: "undocked",
        //   activate: true
        // });
      }
    });
  } catch (error) {
    console.error('refreshMainWindow error: ', error);
  }
};

export const createIframeWindow = async (uuid: string) => {
  if (windowManager.iframeWindow) {
    windowManager.iframeWindow.loadURL(`${winURL}#/iframe?uuid=${uuid}`);
    windowManager.iframeWindow.show();
    return;
  }
  windowManager.iframeWindow = await getSDK().windowManager.create({
    name: `iframeWindow-${getRandomUUID()}`,
    url: `${winURL}#/iframe?uuid=${uuid}`,
    browserWindow: {
      ...mainWindowConfig(),
      webPreferences: {
        ...mainWindowConfig().webPreferences,
        // preload: `${__dirname}/preload.js`,
        scrollBounce: false,
      },
      titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
    },
  });
  if (showDevTool) {
    // windowManager.iframeWindow.webContents.openDevTools({
    //   mode: "undocked",
    //   activate: true
    // });
  }
  // ready-to-show之后显示界面
  // windowManager.iframeWindow.once('ready-to-show', () => {
    windowManager.iframeWindow.show();
  // });
  windowManager.iframeWindow.on('closed', () => {
    windowManager.iframeWindow = null;
  });
  handleError(windowManager.iframeWindow, 'iframeWindow');
};

export const createPreviewWindow = async (url: string) => {
  console.log(url);
  windowManager.closePreviewWindow();

  if (windowManager.previewWindow) {
    windowManager.previewWindow.loadURL(url);
    windowManager.previewWindow.show();
    return;
  }
  windowManager.previewWindow = await getSDK().windowManager.create({
    name: `previewWindow-${getRandomUUID()}`,
    url: '',
    browserWindow: {
      ...previewWindowConfig(),
    },
  });
  windowManager.previewWindow.loadURL(`${winURL}#/h5?url=${url}`);
  console.log(`${winURL}#/h5?url=${url}`);
  windowManager.previewWindow.menuBarVisible = false;
  // 开发模式下自动开启devtools
  // if (showDevTool) {
  //   windowManager.previewWindow.webContents.openDevTools({
  //     mode: "undocked",
  //     activate: true
  //   });
  // }

  // ready-to-show之后显示界面
  // windowManager.previewWindow.once('ready-to-show', () => {
    windowManager.previewWindow.show();
  // });
  windowManager.previewWindow.on('closed', () => {
    windowManager.previewWindow = null;
  });
  handleError(windowManager.previewWindow, 'previewWindow');
};

// 创建偏好设置窗口
export const createSettingWindow = async (type?: string) => {
  // 检查窗口是否存在且未被销毁
  if (windowManager.settingWindow && !windowManager.settingWindow.isDestroyed() && type !== 'init') {
    windowManager.settingWindow.show();
    return;
  }

  // 如果窗口已被销毁，清除引用
  if (windowManager.settingWindow && windowManager.settingWindow.isDestroyed()) {
    windowManager.settingWindow = null;
  }

  // 创建主窗口
  windowManager.settingWindow = await getSDK().windowManager.create({
    name: '设置',
    url: `${winURL}#/setting`,  // 使用 attachViewWithRoute 加载内容，这里不设置 URL
    browserWindow: {
      ...settingWindowConfig(),
      webPreferences: {
        ...settingWindowConfig().webPreferences,
      },
      titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
      show: true,
    },
  });
  // const viewOptions = {
  //   bounds: {
  //     x: 0,
  //     y: 0,
  //     width: settingWindowConfig().width,
  //     height: settingWindowConfig().height,
  //   },
  //   ...settingWindowConfig()
  // }
  // 使用 attachViewWithRoute 加载预加载的 BrowserView
  // await attachViewWithRoute(windowManager.settingWindow, {
  //   type: 'default',
  //   url: '/setting',
  //   viewOptions: viewOptions
  // });

  // windowManager.settingWindow.once('ready-to-show', () => {
    windowManager.closeLoadWindow();
    type !== 'init' && windowManager.settingWindow.show();
  // });

  windowManager.settingWindow.on('close', () => {
    const win = windowManager.settingWindow;
    if (win) {
      // // 销毁 BrowserView
      // let view = win.getBrowserView();
      // if (view) {
      //   getSDK().windowManager.close(view?._name);
      //   if (!view.webContents?.isDestroyed()) {
      //     win.setBrowserView(null);  // 先移除
      //     view.webContents?.destroy();       // 释放资源            // 再销毁
      //     view = null;
      //   }
      // }

      // // 销毁窗口
      // getSDK().windowManager.close(win?._name);
      // win.destroy();
      windowManager.settingWindow = null;
    }
  });

  handleError(windowManager.settingWindow, 'settingWindow');
};

// 创建独立窗口
export const createAloneWindow = async (url: string, opts = {}, type) => {
  console.log('createAloneWindow', url , opts, type);
  windowManager.closeAloneWindow();
  const _type = type || 'default';
  const options = {
    ...previewWindowConfig(),
    ...opts,
    bounds: {
      x: 0,
      y: 0,
      width: opts?.width || '100%',
      height: opts?.height || '100%'
    },
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      scrollBounce: false
    }
  }
  // if (windowManager.aloneWindow) {
  //   // 使用 attachViewWithRoute 重新加载内容
  //   await attachViewWithRoute(windowManager.aloneWindow, {
  //     type: _type,
  //     url,
  //     viewOptions: options
  //   });
  //   windowManager.aloneWindow.show();
  //   return;
  // }

  windowManager.aloneWindow = await getSDK().windowManager.create({
    name: `aloneWindow-${getRandomUUID()}`,
    url: '',  // 使用 attachViewWithRoute 加载内容，这里不设置 URL
    browserWindow: options,
  });

  windowManager.aloneWindow.show();

  // 使用 attachViewWithRoute 加载预加载的 BrowserView
  await attachViewWithRoute(windowManager.aloneWindow, {
    type: _type,
    url,
    viewOptions: options
  });

  windowManager.aloneWindow.menuBarVisible = false;

  windowManager.aloneWindow.on('close', () => {
    closeAlone();
  });
  // windowManager.aloneWindow.on('closed', () => {
  //   closeAlone();
  // });

  handleError(windowManager.aloneWindow, 'aloneWindow');
};

const closeAlone = () => {
  const win = windowManager.aloneWindow;
  if (win) {
    // 销毁所有 BrowserView
    let views = win.getBrowserViews();
    if (views && views.length > 0) {
      views.forEach(view => {
        if (view && view._name) {
          getSDK().windowManager.close(view._name);
          if (!view.webContents?.isDestroyed()) {
            win.setBrowserView(null);  // 先移除
            view.webContents?.destroy();       // 释放资源
            view = null;
          }
        }
      });
    }

    // 销毁窗口
    getSDK().windowManager.close(win?._name);
    win.destroy();
    windowManager.aloneWindow = null;
  }
}

export const closeAloneWindow = () => {
  windowManager.closeAloneWindow();
};

export const createDialogWindow = async (url: string, opts = {}) => {
  windowManager.closeDialog();

  if (windowManager.dialog) {
    windowManager.dialog.loadURL(url);
    windowManager.dialog.show();
    return;
  }
  windowManager.dialog = await getSDK().windowManager.create({
    name: `dialog-${getRandomUUID()}`,
    url: '',
    browserWindow: {
      ...previewWindowConfig(),
      ...opts,
    },
  });
  windowManager.dialog.loadURL(`${winURL}#/${url}`);
  windowManager.dialog.menuBarVisible = false;
  // 开发模式下自动开启devtools
  if (showDevTool) {
    // windowManager.dialog.webContents.openDevTools({
    //   mode: "undocked",
    //   activate: true
    // });
  }

  // ready-to-show之后显示界面
  // windowManager.dialog.once('ready-to-show', () => {
    windowManager.dialog.show();
  // });
  windowManager.dialog.on('closed', () => {
    windowManager.dialog = null;
  });
  handleError(windowManager.dialog, 'dialog');
};

export const closeDialogWindow = () => {
  windowManager.closeDialog();
};



let dataValue = [];

/**
 * 在接收到窗口已经渲染完成以后执行
 * 严谨一些判断窗口实例是否存在
 * 存在:发送send进程给到数据
 * 遍历数组完以后清空数据。
 * 这儿主要是第一次渲染时执行， 如果渲染完成以后将不会执行，比如窗口已经打开的状态
 * 然后又有提醒消息他会执行下面创建窗口的命令
 */
ipcMainHandle('create-remind-dialog-data', (event, arg) => {
  // console.log('create-remind-dialog-data--------');
  if (!windowManager.remindDialog) return;
  for (const key in dataValue) {
    try {
      windowManager.remindDialog.webContents.send('create-remind-dialog', dataValue[key]);
      if (key == (dataValue.length - 1).toString()) {
        dataValue = [];
      }
    } catch (error) {
      console.error('create-remind-dialog-data error: ', error);
    }
  }
});

/**
 * 删除
 */
ipcMainHandle('create-remind-dialog-data-delete', (event, arg) => {
  const index = dataValue.findIndex((v) => v.remindDetail.openid == arg.remindDetail.openid);
  if (index === -1) return;
  const list = dataValue.filter((v) => v.remindDetail.openid != arg.remindDetail.openid);
  // dataValue.splice(index,1);
  dataValue = list || [];
  // console.log('create-remind-dialog-data-delete--------删除');
});

export const removeRemindDialogWindow = (url: string, data = {}, opts = {}) => {
  if (!windowManager.remindDialog) return;
  try {
    windowManager.remindDialog.webContents.send('remove-remind-dialog', data);
  } catch (error) {
    console.error('removeRemindDialogWindow error: ', error);
  }
};

export const createRemindDialogWindow = async (url: string, data = {}, opts = {}) => {
  /**
   * 如果同时触发多个提醒的情况下
   * 去查看数据是否为空,并且新增数据是否已在列表中,该操作是避免窗口创建时执行错误导致重复数据
   */
  if (data?.remindType == 20025 || data?.remindType == 0) {
    // 提醒数据
    if (
      (!dataValue.length || !dataValue.some((v) => v.remindDetail.openid === data?.remindDetail?.openid))
      && dataValue.length <= 4
    ) {
      dataValue.push(data);
    }
  } else if (data?.remindType == 20021) {
    // 活动数据
    if (!dataValue.length || dataValue.length <= 4) {
      dataValue.push(data);
      console.log('===============22222222=opts.width', JSON.stringify(dataValue));
    }
  }

  if (windowManager.remindDialog && !data?.done) {
    try {
      console.log('qwer', JSON.stringify(opts));
      windowManager.remindDialog.webContents.send('create-remind-dialog', data);
      windowManager.remindDialog.show();
    } catch (error) {
      console.error('createRemindDialogWindow error: ', error);
    }
    return;
  }

  console.log('================opts.width', `${winURL}#/${url}`, JSON.stringify(data));
  new Store().set('firstRemindData', data);
  await new Promise(resolve => setTimeout(resolve, 100));
  windowManager.remindDialog = await getSDK().windowManager.create({
    name: `remindDialog-${getRandomUUID()}`,
    url: `${winURL}#/${url}`,
    browserWindow: {
      show: false,
      center: false,
      skipTaskbar: true, // 显示到任务栏
      movable: false, // 移动
      resizable: false, // 窗口大小
      fullscreenable: true, // 窗口是否可以进入全屏模式
      alwaysOnTop: true, // 顶部
      transparent: true, // 透明
      frame: false, // 窗口边框
      hasShadow: true, // 窗口是否阴影
      // backgroundColor: '#00000000', // 背景色
      backgroundColor: 'rgba(255, 255, 255, 1)',
      autoHideMenuBar: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        // devTools: false,
      },
      ...opts,
    },
  });
  windowManager.remindDialog.on('closed', () => {
    windowManager.remindDialog = null;
  });
  handleError(windowManager.remindDialog, 'remindDialog');
  setTimeout(() => {
    windowManager.remindDialog.menuBarVisible = false;

    // 定位到桌面右上角
    // 获取当前显示器
    const currentDisplay = screen.getPrimaryDisplay();
    const { width, height } = currentDisplay.workArea;
    const [cwidth, cheight] = windowManager.remindDialog.getContentSize();
    const left = parseInt(String(width - (cwidth || 0) - 5), 10);
    const top = process.platform === 'darwin' ? 5 : parseInt(String(height - (cheight || 0) - 5), 10);
    console.error(width, height, cwidth, cheight, left, top);
    windowManager.remindDialog.setPosition(left, top);
    // 开发模式下自动开启devtools
    if (showDevTool) {
      // windowManager.remindDialog.webContents.openDevTools({
      //   mode: "undocked",
      //   activate: true
      // });
    }

    // ready-to-show之后显示界面
    // todo
    // windowManager.remindDialog.once('ready-to-show', () => {
      // todo 可能异步加载原因首次加载send数据页面没法接收，先存一下
      // setTimeout(() => {
      new Store().set('firstRemindData', data);
      setTimeout(() => {
        windowManager.remindDialog.show();
      }, 300)
      // }, 100)
    // });

  }, 300)
};

export const closeRemindDialogWindow = () => {
  windowManager.closeRemindDialog();
};

const destoryLoadWindow = () => {
  if (UseStartupChart && windowManager.loadWindow) {
    windowManager.closeLoadWindow();
  }
};

export const createMergedMessageWindow = async (fileUrl: string, sourceWindow?: Electron.BrowserWindow) => {
  const win = sourceWindow || windowManager.mainWindow;
  if (!win) return;

  const { y, width } = win.getBounds();

  if (windowManager.mergedMessageWindow && !windowManager.mergedMessageWindow.isDestroyed()) {
  // const view = windowManager.mergedMessageWindow.getBrowserView();
  //   if (view) {
  //     // 获取当前视图的 URL
  //     const currentUrl = new URL(view.webContents.getURL());
  //     const currentFileUrl = decodeURI(currentUrl.searchParams.get('fileUrl') || '');

  //     if (currentFileUrl !== fileUrl) {
  //       // URL 不一致，执行路由跳转
  //       await attachViewWithRoute(windowManager.mergedMessageWindow, {
  //         type: 'default',
  //         url: `/merged?fileUrl=${encodeURI(fileUrl)}`,
  //         viewOptions: {
  //           bounds: { x: 0, y: 0, width: 400, height: 600 },
  //           webPreferences: {
  //             nodeIntegration: true,
  //             contextIsolation: false,
  //             enableRemoteModule: true,
  //             scrollBounce: false
  //           }
  //         }
  //       });
  //     }

  //     windowManager.mergedMessageWindow.show();
  //     windowManager.mergedMessageWindow.focus(); // 新增聚焦
  //     windowManager.mergedMessageWindow.setPosition(width - 700, y);
  //     try {
  //       view.webContents.send('showMergedMessage', {
  //         type: '',
  //         fileUrl
  //       });
  //     } catch (error) {
  //       console.error('发送消息到合并消息窗口失败:', error);
  //     }
  //     return;
  //   }
  }

  // 创建新窗口
  windowManager.mergedMessageWindow = await getSDK().windowManager.create({
    name: 'mergedMessageWindow',
    url: `${winURL}#/merged?fileUrl=${encodeURI(fileUrl)}`,  // 使用 attachViewWithRoute 加载内容，这里不设置 URL
    browserWindow: {
      y: y - 40,
      x: width - 700,
      width: 460,
      height: 600,
      frame: false,
      titleBarStyle: 'hidden',
      trafficLightPosition: { x: 20, y: 20 },
      maximizable: false,
      resizable: false,
      fullscreenable: false,
      backgroundColor: '#fff'
    }
  });

  // 使用 attachViewWithRoute 加载预加载的 BrowserView
  // await attachViewWithRoute(windowManager.mergedMessageWindow, {
  //   type: 'default',
  //   url: `/merged?fileUrl=${encodeURI(fileUrl)}`,
  //   viewOptions: {
  //     bounds: { x: 0, y: 0, width: 400, height: 600 },
  //     webPreferences: {
  //       nodeIntegration: true,
  //       contextIsolation: false,
  //       enableRemoteModule: true,
  //       scrollBounce: false
  //     }
  //   }
  // });

  windowManager.mergedMessageWindow.on('close', (e) => {
    // e.preventDefault();
    // try {
    //   const win = windowManager.mergedMessageWindow;
    //   if (win && !win.isDestroyed()) {
    //     let view = win.getBrowserView();
    //     if (view) {
    //       getSDK().windowManager.close(view?._name);
    //       if (!view.webContents?.isDestroyed()) {
    //         win.setBrowserView(null);  // 先移除
    //         view.webContents?.destroy();       // 释放资源
    //         // view?.destroy();                   // 再销毁
    //         view = null;
    //       }
    //     }
    //     getSDK().windowManager.close(win?._name);
    //     win.destroy();
    //   }
      windowManager.mergedMessageWindow = null;
    // }  catch (error) {
    //   console.error('mergedMessageWindow close error:', error);
    // }
  });

  windowManager.mergedMessageWindow.on('closed', () => {
    console.log('mergedMessageWindow closed');
    windowManager.mergedMessageWindow = null;
  });

  handleError(windowManager.mergedMessageWindow, 'mergedMessageWindow');
};

export const createMonitorWindow = async () => {
  if (windowManager.monitorWindow && !windowManager.monitorWindow.isDestroyed()) {
    windowManager.monitorWindow.show();
    return;
  }

  // 如果窗口已被销毁，清除引用
  if (windowManager.monitorWindow && windowManager.monitorWindow.isDestroyed()) {
    windowManager.monitorWindow = null;
  }
  const options = {
    width: 1200,
    height: 900,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      scrollBounce: false
    },
    titleBarStyle: IsUseSysTitle ? 'default' : 'hidden',
    show: true,
    resizable: true
  }

  // 创建监控窗口
  windowManager.monitorWindow = await getSDK().windowManager.create({
    name: '系统监控',
    url: `${winURL}#/monitor`,
    browserWindow: options
  });

  // 使用 attachViewWithRoute 加载预加载的 BrowserView
  // await attachViewWithRoute(windowManager.monitorWindow, {
  //   type: 'default',
  //   url: '/monitor',
  //   viewOptions: {
  //     bounds: { x: 0, y: 0, width: options.width, height: options.height },
  //     webPreferences: options.webPreferences
  //   }
  // });

  windowManager.monitorWindow.on('close', () => {
    const win = windowManager.monitorWindow;
    if (win) {
      // const view = win.getBrowserView();
      // if (view) {
      //   windowManager.monitorWindow.removeBrowserView(view);
      //   view.webContents?.destroy();
      // }
      // win.destroy();
      windowManager.monitorWindow = null;
    }
  });

  handleError(windowManager.monitorWindow, 'monitorWindow');
};
