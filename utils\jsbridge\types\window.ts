/** 窗口配置接口 */
export interface WindowOptions {
  url: string;
  name?: string;
  browserWindow?: {
    width?: number;
    height?: number;
    titleBarStyle?: string;
    resizable?: boolean;
    movable?: boolean;
    useContentSize?: boolean;
    autoHideMenuBar?: boolean;
    frame?: boolean;
    show?: boolean;
    webPreferences?: {
      nodeIntegration?: boolean;
      contextIsolation?: boolean;
      webSecurity?: boolean;
    };
    [key: string]: any;
  };
}

/** 窗口信息接口 */
export interface WindowInfo {
  isMaximized: boolean;
  isMinimized: boolean;
  isFullScreen: boolean;
  width: number;
  height: number;
}
