// 下拉菜单
.t-popup .t-popup__content {
  padding: 8px;
}

.t-tooltip[data-popper-placement=top] .t-popup__content {
  margin-bottom: 4px;
  &:has(.t-popup__arrow) {
    margin-bottom: 10px !important;
  }
}

.t-tooltip[data-popper-placement=bottom] .t-popup__content {
  margin-top: 4px;
  &:has(.t-popup__arrow) {
    margin-top: 10px !important;
  }
}

.list-item-later .t-popup__content,
.later .t-popup__content {
  padding: 4px;
}

.t-tooltip .t-popup__content {
  padding: 4px 8px;
}

.t-popup-operate-clouddiskhome {
  .operate-item,
  .operate-item-fliter {
    height: 32px;
    padding-right: 16px;
    min-width: 136px;
    margin-bottom: 2px;
    padding-left: 16px;
    border-radius: 4px;
    display: flex;
    cursor: pointer;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }

    .operate-item-span {
      height: 22px;
      font-size: 14px;

      font-weight: 400;
      line-height: 22px;
      color: var(--kyy-color-dropdown-text-default, #1a2139);
      font-family: PingFang SC;
    }
  }

  .operate-item-fliter:hover {
    border-radius: 4px;
    background: var(--lingke-select, #f3f6fa);
  }
}
.popup-file-kr{

  .popup-file-kr-item {
    width: 128px !important;
  }
}
.--td-size-4{
  margin-top: 4px;
}
.operate-item-upd {
  width: 136px !important;
  flex-wrap: wrap;
  padding: 8px;
  height: auto !important;
  cursor: pointer;
  display: flex;
  // justify-content: space-between;
  align-items: center;
  .iconflie {
    width: 20px;
    height: 20px;
    font-size: 20px;
    margin-right: 12px;
    color: #828DA5;
  }
  color: var(--kyy_color_dropdown_text_default, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.operate-admin {
  .operate-item {
    width: 264px !important;
    flex-wrap: wrap;
    padding: 5px 8px;
    height: auto !important;
    justify-content: space-between;
  }

  .admin-lable {
    height: 22px;
    width: 195px;
    font-size: 14px;

    font-weight: 400;
    color: #13161b;
    line-height: 22px;
  }

  .admin-value {
    height: 20px;
    width: 100%;

    font-size: 12px;

    font-weight: 400;
    color: #717376;
    line-height: 20px;
  }

  .operate-item:last-child::after {
    content: "";
    width: 100%;
    height: 1px;
    background: #e3e6eb;
    position: absolute;
    top: -4px;
    left: 0;
  }
}

// 会导致子级选择框无法被选中
//弹出层
//.t-popup__content {
//  padding: 8px;
//}

.operate-item-span-sall {
  height: 22px;
  font-size: 14px;

  font-weight: 400;
  line-height: 22px;
  color: var(--kyy-color-dropdown-text-default, #1a2139);
  font-family: PingFang SC;
}

.org-text {
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 4px;
  font-family: PingFang SC !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
}

.operate-item {
  padding: 0 16px;

  span {
    color: var(--kyy-color-dropdown-text-default, #1a2139);
  }
}

.z-index-popup {
  z-index: 9999 !important;
  transition: 0 !important;
}

.operate-item:hover {
  background: var(--kyy-color-dropdown-bg-hover, #f3f6fa);
}
.operate-item-upd:hover {
  background: var(--kyy-color-dropdown-bg-hover, #f3f6fa);
}

.operate-item:last-child {
  position: relative;
  margin-top: 8px !important;
}

.operate-item:last-child::after {
  content: "";
  width: 100%;
  height: 1px;
  background: #e3e6eb;
  position: absolute;
  top: -4px;
  left: 0;
}

.operate-kr {
  svg {
    width: 20px;
    height: 20px;
    margin-right: 12px;

    // margin-top: 4px;
  }

  .operate-item {
    padding-left: 16px !important;
    width: 148px !important;
  }

  .operate-item:last-child::after {
    content: "";
    width: 100%;

    height: 1px;
    background: #e3e6eb;
    position: absolute;
    top: -4px;
    left: 0;
  }
}

// lss
.popupBrushBody {
  padding: 0 !important;
}

.delmode {
  .t-dialog__footer {
    .t-button {
      min-width: 80px;
    }
  }

  .t-dialog--default {
    padding: 32px !important;
  }

  .t-dialog__body {
    padding: 0 !important;
  }
}

.dialog {
  .t-dialog--default {
    padding: 24px !important;
  }

  .t-dialog__body {
    padding: 0 !important;
  }

  .t-dialog__footer {
    padding-top: 0 !important;
  }
}

.warnDel {
  .t-dialog__modal-default {
    padding: 32px !important;
  }
}
.t-popup-operate-fengcai {
  max-height: 270px;
  overflow-y: auto;
}
.fcfengcai {
  .t-popup__content {
    padding: 4px !important;
    margin-top: -12px !important;
  }
  .operate-item-fengcai {
    display: flex;
    height: 32px;
    min-width: 136px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 8px;
    align-items: center;
    gap: 12px;
    color: var(--kyy_color_dropdown_text_default, #1a2139);
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;

    line-height: 32px; /* 157.143% */
  }
  .operate-item-fengcai:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
  }
}



.popupTag {
  .t-popup__content {
    border-radius: 8px !important;
    background: var(--bg-kyy_color_bg_light, #FFF);

    /* kyy_shadow_m */
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12) !important;
    padding: 0px !important;
  }
}