```jsx
import pkgs from '../package.json';
console.log(pkgs);
export default () => {
  return (
    <div>
      <h1>富文本组件</h1>
      <h2>当前稳定版本: 0.3.2</h2>
      <p>当前文档版本: {pkgs.devDependencies['@rk/editor']}</p>
    </div>
  );
};
```

### 属性

示例:

```jsx
// 1.引入组件
import { LkEditor } from '@rk/editor';
// 2.引入样式
import '@rk/editor/index.css';

export default () => (
  <div class="lk-editor-wrapper">
    <LkEditor
      editorType="A"
      height="300"
      ref="quill"
      toolbar
      isPreviewImage
      options={{
        placeholder: '请输入内容',
        showTool: true,
        readOnly: false,
        showBubble: true,
        height: 300,
      }}
    />
  </div>
);
```

#### Props

| 名称             | 类型     | 默认值 | 说明                                                          |
| ---------------- | -------- | ------ | ------------------------------------------------------------- |
| editor-type      | string   | A      | 编辑器类型 A ｜ B ｜ C ｜ D                                   |
| maxlength        | number   | -      | 编辑器字数限制                                                |
| toolbar          | boolean  | true   | 是否显示工具栏（即将弃用==>请使用 showTool）                  |
| is-preview-image | boolean  | true   | 是否可预览图片（已经弃用-->根据 previewImage 去自定义接入预览 |
| options          | object   | -      | 配置项                                                        |
| onCropper        | function | -      | 编辑图中间处理方法, 裁剪等后加工图返回图的 url                |

#### options 配置项

| 名称        | 类型         | 默认值         | 说明                                 |
| ----------- | ------------ | -------------- | ------------------------------------ |
| isMobile    | boolean      | false          | 是否小屏幕                           |
| placeholder | string       | '请输入内容'   | 编辑器提示语                         |
| readOnly    | boolean      | false          | 是否只读                             |
| showTool    | boolean      | true           | 是否显示工具栏                       |
| toolbar     | string:array | toolbar 工具栏 | 不展示工具栏(尽量不要取消太多)       |
| showBubble  | boolean      | true           | 是否显示气泡                         |
| height      | number       | 0              | 编辑器高度（如果要可拖动必须穿非 0） |
| componentId | string       | lk-editor      | 复制监听事件                         |

#### toolbar 工具栏

| 名称       | 类型   | 说明     | 名称        | 类型   | 说明     |
| ---------- | ------ | -------- | ----------- | ------ | -------- |
| header     | string | 标题     | ordered     | string | 有序列表 |
| size       | string | 字体大小 | bullet      | string | 无序列表 |
| bold       | string | 加粗     | blockquote  | string | 引用     |
| italic     | string | 斜体     | line        | string | 分割线   |
| strike     | string | 删除线   | image       | string | 图片     |
| underline  | string | 下划线   | annex       | string | 附件     |
| color      | string | 字体颜色 | align       | string | 对齐方式 |
| background | string | 背景颜色 | link        | string | 链接     |
| indentLeft | string | 左缩进   | indentRight | string | 右缩进   |
| clean      | string | 清空     |

### API

示例:

```typescript
<lk-editor ref="quill"
   @image="onLkQuillImage"
   @update="handleContentChange"
   @annex="onAnnex"
   @previewImage="onPreviewImage"
   @goLink="onGoLink"
   @annexEvent="onAnnexEvent"
   :options="{
      isMobile:true
   }"
   @copyFile="onCopyFile"></lk-editor>
```

| 事件名          | 默认值                                              | 描述                 |
| --------------- | --------------------------------------------------- | -------------------- |
| image           | 无                                                  | 编辑器触发上传图片   |
| ready           | 无                                                  | 编辑器初始化完成回调 |
| update          | ` delta {ops:[]}`                                   | 编辑器内容更新时触发 |
| annex           | 无                                                  | 编辑器上传附件       |
| copyFile        | `(event,type:{doc,xls,pdf,ppt,image},base64,files)` | 编辑器复制文件       |
| previewImage    | `(data:{images,index,url})`                         | 编辑器预览图片       |
| goLink          | `(data:{images,index,url})`                         | 编辑器点击链接地址   |
| annexEvent      | `(events,type:{preview,download},data:any)`         | 编辑器触发上传附件   |
| imageEditChange | `(isEdit: boolean)`                                 | 编辑器编辑图片       |

#### goLink

- 描述: 编辑器点击链接地址
- 返回参数: 链接地址

electron 示例:

```typescript
require('electron').shell.openExternal(link);
```

### 方法

示例:

```typescript
// 获取dom
const quill = ref<any>(null);
quill.value.getQuill();
// 插入图片
quill.value.insertImage('https://img.zcool.cn/communith.jpg');
```

| 方法               | 默认值        | 描述                       |
| ------------------ | ------------- | -------------------------- |
| renderContent()    | `{ ops: [] }` | 渲染编辑器内容             |
| getQuill()         | N             | 获取编辑器对象             |
| disableEditor()    | N             | 是否禁止编辑器（即将弃用） |
| insertImage()      | string        | 插入图片                   |
| cursorPosition()   | N             | 获取光标位置               |
| getContents()      | N             | 获取编辑内容               |
| getHtml()          | N             | 获取编辑器 html            |
| setHtml()          | string        | 设置编辑器 html            |
| getContentLength() | N             | 获取内容长度               |

### 富文本回显 `@rk/editor >= 0.3.1`

```typescript
import { createApp } from 'vue';
// 1.引入组件
import LkEditor from '@rk/editor';
// 2.引入样式
import '@rk/editor/index.css';

// 3.创建vue实例
const app = createApp(App);

// 4.注册组件
app.use(LkEditor);

const onFileDownload = (file) => {
  console.log(file);
}

const onFilePreview = (file) => {
  console.log(file);
}

const onPreviewImage = (image) => {
  console.log(image);
}

// 5.富文本回显
<LkEditorPlayground
   :content="content"
   @onFileDownload="onFileDownload"
   @onFilePreview="onFilePreview"
   @onPreviewImage="onPreviewImage"
   @onLink="onLink"
>
</LkEditorPlayground>

```

#### Props

| 方法           | 类型                 | 默认值 | 描述                                 |
| -------------- | -------------------- | ------ | ------------------------------------ |
| content        | string / { ops: [] } | N      | 富文本内容(html 字符串或 Delta 数据) |
| onFileDownload | function             | N      | 下载文件回调                         |
| onFilePreview  | function             | N      | 预览文件回调 参数`File`              |
| onPreviewImage | function             | N      | 预览图片回调 参数`Image`             |
| onLink         | function             | N      | 点击链接回调 参数`link`              |

#### 回调参数

| 名称  | 类型                                                               | 说明     |
| ----- | ------------------------------------------------------------------ | -------- |
| File  | `{name:string,type:string,size:number,url:string, file_id:string}` | 文件对象 |
| Image | `{images:string[],index:number,url:string}`                        | 图片对象 |
| link  | `{url:string}`                                                     | 链接对象 |

### 本地开发

安装依赖

```typescript
yarn;
```

运行

```typescript
yarn dev
```

打包

```typescript
yarn build
```

本地调试(将包安装到本地全局)

```typescript
yarn link
```

本地项目调试

```typescript
yarn link @rk/editor
```
