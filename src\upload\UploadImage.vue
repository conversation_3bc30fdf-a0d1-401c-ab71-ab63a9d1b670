<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { onUploadValidate } from '../../utils/upload';
import { SizeLimitObj, Upload as TUpload } from 'tdesign-vue-next';
import { useUploadImageLogic } from './hooks/useUploadImageLogic';
import ImageItem from './components/ImageItem.vue';
import { defineAsyncComponent, type PropType } from 'vue';
import imgIcon from './icon/img.svg';
import { CropperConfig } from './type';

const RkCropperDialog = defineAsyncComponent(() => import('../cropper/CropperDialog.vue'));

const props = defineProps({
  /** 绑定的图片URL */
  modelValue: {
    type: [String, Array] as PropType<string | string[]>,
    default: (): string[] => []
  },
  /** 上传根目录 */
  rootDir: {
    type: String,
    required: true
  },
  /** 最大上传数量 */
  maxCount: {
    type: Number,
    default: 1
  },
  /** 接受的文件类型 */
  accept: {
    type: String,
    default: 'image/png,image/jpg,image/jpeg'
  },
  /** 图片文件大小限制，默认单位 KB。可选单位有：`'B' | 'KB' | 'MB' | 'GB'`。示例一：`1000`。示例二：`{ size: 2, unit: 'MB', message: '图片大小不超过 {sizeLimit} MB' }` */
  sizeLimit: {
    type: [Number, Object] as PropType<number | SizeLimitObj>,
    default: (): SizeLimitObj => ({
      size: 5,
      unit: 'MB',
      message: '文件大小不能超过{sizeLimit}MB',
    })
  },
  /** 是否只读 */
  readonly: {
    type: Boolean,
    default: false
  },
  /** 是否可拖拽排序 */
  sortable: {
    type: Boolean,
    default: false
  },
  /** 是否自动排序 */
  autoSort: {
    type: Boolean,
    default: false
  },
  /** 图片列表容器类名 */
  containerClass: {
    type: String,
    default: 'rk-upload-image-container'
  },
  /** 是否启用图片裁剪功能 */
  enableCrop: {
    type: Boolean,
    default: false
  },
  /** 裁剪器配置参数 */
  cropperProps: {
    type: Object as PropType<CropperConfig>,
    default: (): CropperConfig => ({
      title: '编辑图片',
      outputWidth: 0,
      outputHeight: 0,
      options: {
        aspectRatio: 1,
        viewMode: 1,
        guides: false,
        autoCropArea: 1,
      },
    })
  },
  /** 是否显示下方错误提示 */
  showErrorMessage: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits({
  /** 更新绑定值 */
  'update:modelValue': (value: string[]) => true,
  /** 图片列表变化 */
  'change': (urls: string[]) => true,
  /** 预览图片 */
  'preview': (payload: { images: string[]; index: number; url: string }) => true,
  /** 点击图片 */
  'click': (payload: { images: string[]; index: number; url: string }) => true,
  /** 拖拽排序 */
  'sort': (oldIndex: number, newIndex: number) => true,
  /** 裁剪确认 */
  'crop-confirm': (url: string, originalUrl: string) => true
});

const modelVal = useVModel(props, 'modelValue', emit, {
  passive: true,
  defaultValue: [],
});

// 上传逻辑
const {
  images,
  canActuallyUpload,
  actualContainerClass,
  cropperDialogRef,
  handleCropConfirm,
  handleCropCancel,
  customUploadImage,
  removeImage,
  handleImgClick,
} = useUploadImageLogic(modelVal, props, emit as any);
</script>

<template>
  <div :class="['uploader', { 'hide-upload-error-tip': !showErrorMessage }]">
    <div
      v-if="images?.length"
      :class="[actualContainerClass, 'image-list-container', { 'sortable': sortable }]"
    >
      <ImageItem
        v-for="(item, index) in images"
        :key="item?.url || index"
        :item="item"
        :index="index"
        :readonly="readonly"
        @click="handleImgClick"
        @remove="removeImage"
      />
    </div>

    <t-upload
      v-if="canActuallyUpload"
      ref="uploadRef"
      theme="custom"
      :accept="props.accept"
      :multiple="props.maxCount > 1 && !enableCrop"
      :abridge-name="[6, 6]"
      :max="props.maxCount"
      :request-method="customUploadImage"
      :size-limit="props.sizeLimit"
      v-bind="$attrs"
      allow-upload-duplicate-file
      @validate="onUploadValidate"
    >
      <slot>
        <div class="img-wrap btn-upload">
          <img
            :src="imgIcon"
            class="icon"
          >
          <p class="desc">
            点击上传
          </p>
        </div>
      </slot>
    </t-upload>

    <!-- 图片裁剪 - 动态组件，仅在开启裁剪时加载 -->
    <component
      :is="RkCropperDialog"
      v-if="enableCrop"
      ref="cropperDialogRef"
      @confirm="handleCropConfirm"
      @cancel="handleCropCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.uploader {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .image-list-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .icon {
    width: 24px;
    height: 24px;
  }

  .btn-upload {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 78px;
    height: 78px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
    background: var(--kyy_color_upload_bg, #fff);
    font-size: 12px;
  }

  .desc {
    color: var(--text-kyy-color-text-3, #828da5);
    font-size: 12px;
    margin: 0;
  }
}

.hide-upload-error-tip :deep(.t-upload__tips-error) {
  display: none !important;
}
</style>
