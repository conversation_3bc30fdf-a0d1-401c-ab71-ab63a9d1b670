import { useMessageStore } from "./store"
import { getGroupCardSession, getGroupDetailByCard, getGroupInfo, getGroupSessionInfo, getGroupSessionMembers, getPrivateSessionInfo, getSessionMemberByCard, parseGroupRelation, updateConversation, getGroupItem} from './request'
import { insertConversations, insertOrUpdateGroup, onDbConversationUpdate, queryConversationByIds, onDbConversationSetting } from "./dbUtils";
import { getSessionByGroupRelation, getSessionLocalIdByCards,getSessionByPrivateRelation2,  getMsgToStore,  msgToUseChat } from "./utils";
import { useGroupInviteStore } from "./groupInvite";
import { getImCardIds, getOpenid } from "@renderer/utils/auth";
import { getAssistantInfoApi } from '@renderer/api/im/api';
import { getConversationIDBySessionType, getOneConversationDetail } from './ipcIMBridge';
import { safeParseJson } from "@renderer/utils/assist";

import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;


/**
 * 更新成员
 * @param conversation
 */
export const updateConversationRelation = (conversation: ConversationToSave, origin?) => {
  console.log('=====>updateConversationRelation',conversation);
  if (conversation.conversationType === 1) {
    updatePrivateRelation(conversation.myCardId, conversation.targetCardId, {}, origin);

  } else {
    updateGroupConversationRelation(conversation);
  }
};

export const updateAssistantsInfo = async (assistantId) => {
    const openId = getOpenid();
    const data = (await getAssistantInfoApi(openId, assistantId))?.data?.data;
    data && useMessageStore().updateAssistantConversation(data)
}
/**
 * 更新单聊关系，包含置顶、免打扰、身份卡转移，并保存到数据库
 * @param main 我的身份卡
 * @param peer 对方身份卡
 * @returns
 */
export const updatePrivateRelation = async (main, peer, relation?, origin?) => {
  const info = await getPrivateSessionInfo(main, peer);
  // 请求失败
  if (!info) {
    return;
  }
  !info.main && (info.main = main);
  !info.peer && (info.peer = peer);
  console.log('=====>1',main, peer,info, origin);
  const msgStore = useMessageStore();

  // 关系是否存在？
  const inSession = info?.origin && !info?.hasDel;
  // 对方的信息
  const peerInfo = info?.card
  // 获取id
  const id = getSessionLocalIdByCards(main, peer);
  if (id) {
    const conversations = await queryConversationByIds({ ids: id });
    // 查询数据库
    console.log('=====>3conversations',conversations);
    let hasSession = false
    msgStore.sessionList.forEach(item => {
      if (item.localSessionId === id) {
        hasSession = true
        item.inSession = inSession;
        item.unregistered = info?.unregistered;
        // 身份卡转移，修改openId
        if (peerInfo) {
          item.targetId = peerInfo.openid;
        }
        item.isMute = info.noDisturb;
        item.isTop = Boolean(info.pin);
        item.group_type = info.groupType === "GT_FREQUENTLY" ? 1 : 0;
        item.relation = info.origin
        item.productId = info.productId;
        if (conversations?.[0]?.localSessionId === id) {
          onDbConversationUpdate(item);
        }
      }
    });
    // store没有但数据库有
    if (!hasSession && conversations[0]) {
      const session = conversations[0];
      session.relation = info.origin
      session.removeSession = false;
      session.latestMessage = null;
      // 临时关系新增会话，如果没有消息，界面不展示会话
      if(origin === 'APP_PAIRS_ADD' && session.relation === 'IDLE_TEMPORARY') {
        session.inSession = false;
      }
      msgStore.onAddToSessionList(session, true, '_updatePrivateRelation2');
      onDbConversationUpdate(session);
    }
    // 数据库没有需要【新增会话】
    if (!conversations?.[0]) {
      let relationItem = info;
      if (relation) {
        relation.mainOpenImID = info?.mainOpenImID;
        relation.peerOpenImID = info?.peerOpenImID;
        relationItem = relation;
      }
        const session = getSessionByPrivateRelation2(relationItem);
        console.log('=====>session5', session);
        if (!session.conversationID) {
          const conversationInfo = { conversationType: session.conversationType, targetId: session.targetOpenImId, myOpenImId: session.myOpenImId || '' };
          const { data } = await getConversationIDBySessionType(conversationInfo);
          session.conversationID = data;
        }
        if (origin === 'APP_PAIRS_ADD' && session.relation === 'IDLE_TEMPORARY') {
          session.inSession = false;
        }
        msgStore.onAddToSessionList(session, false, '_updatePrivateRelation2');
        insertConversations([session]);
    }
    origin !== 'setCurrentSession' && msgStore.sortSessions('updatePrivateRelation', true, false);
  }

  // 更新成员信息
  console.log('=====>updatePrivateRelation',info);
  if(info.myCard){
    const myCard = getSessionMemberByCard(id, info.myCard)
    if(info.myBindCard) {
      myCard.myBindCard = info.myBindCard
    }
    const peerCard = getSessionMemberByCard(id, info.card, {comment:info.comment,describe:info.describe})
    if(info.bindCard) {
      peerCard.bindCard = info.bindCard
    }
    msgStore.allMembers.set(id, new Map([[info.main,myCard],[info.peer,peerCard]]));
    await ipcRenderer.invoke("im.session.member.update", { list: [myCard,peerCard] })
  }

  console.log("[更新单聊]  ", id, info)
  return;
}

// 更新群聊天会话信息
const updateGroupConversationRelation = async (conversation: ConversationToSave) => {
  const info = await getGroupDetailByCard(conversation.targetId, conversation.myCardId);
  const cardInfo = info?.members?.[0];
  console.log('=====>cardInfo',info,cardInfo);
  if (!info || !cardInfo) { return }

  const groupInfo = getGroupItem(info);
  // 更新群组信息
  await insertOrUpdateGroup(groupInfo);
  const msgStore = useMessageStore();
  const index = useMessageStore().allGroups.findIndex(item => item.group === conversation.targetId);
  if (index !== -1) {
    msgStore.allGroups.splice(index, 1, groupInfo);
  }

  // 更新会话信息
  conversation.isTop = Boolean(cardInfo.attachments?.stayOn);
  conversation.isMute = Boolean(cardInfo.attachments?.noDisturb);
  conversation.inSession = Boolean(!info.removed && !cardInfo.removed);
  conversation.group_type = cardInfo.group_type;
  conversation.joined = cardInfo.joined;
  conversation.viewHistory = groupInfo.attachment.hasLookForHistory;
  onDbConversationUpdate(conversation);

  // 群聊是否解散
  const isGroupDelete = info.removed;
  // 当前是否在群聊中
  const isCardDelete = cardInfo.removed
  // 非自己退群的情况，提示用户被移除群聊
  if ((isGroupDelete || isCardDelete) && !cardInfo.self_delete) {
    // 移除类型：被踢出kickOut、解散dissolution
    let kickOutType = isGroupDelete ? 'dissolution' : 'kickOut';

    msgStore.onGroupKickedAlert(conversation.targetId, conversation.myCardId, kickOutType);
  }

  // 更新群成员列表
  if (['0', '1', '2', '3', '10', '15', '20', '22', '23'].includes(conversation.relation) && conversation.inSession) {
    onGroupMembersUpdate(conversation.targetId);
  }

  // 获取邀请信息
  if (!isGroupDelete && !isCardDelete && cardInfo.roles?.some(r => ["ADMIN", "OWNER"].includes(r))) {
    // 更新群聊邀请信息
    useGroupInviteStore().getGroupInvites({id: conversation.targetId, cards: [conversation.myCardId]})
  }

  //获取顶置群公告
  if (!isGroupDelete && !isCardDelete) {
    // 更新顶置群公告
    useGroupInviteStore().getGroupNotice({ group_id: conversation.targetId, pin: true, card_id: conversation.myCardId }, conversation.myCardId)
  }
}

// 入群申请
export const onGroupMemberApply = (id: string) => {
  const { chatingSession } = useMessageStore();
  if (chatingSession && chatingSession.targetId === id) {
    useGroupInviteStore().getGroupInvites({id: chatingSession.targetId, cards: [chatingSession.myCardId]})
  }
}

// 自己退群，当前群聊则删除，非当前群聊
export const onQuitGroupSync = (data: { group: string, openid: string, rid: string, history: boolean, from_server: boolean }) => {
  const msgStore = useMessageStore();
  if (data.history) { // 保留历史记录
    msgStore.sessionList.forEach(item => {
      if (item.targetId === data.group && item.myCardId === data.openid) {
        item.inSession = false;
        onDbConversationSetting(item);
      }
    });
  } else {
    const params = data?.rid === '#' ? { group: data.group, deleHistory: true } : { main: data.openid, peer: data.group, deleHistory: true };
    onDeleteConversation(params);
  }
};

// 群聊解散
export const onGroupDisband = (groupId: string, isMe: boolean) => {
  console.log(groupId, isMe);

  if (isMe) {
    onDeleteConversation({ group: groupId });
  } else {
    onGroupKickedNotify(groupId);
  }
}

// 构造群聊会话session
const getGroupSession = async (groupConversation, member) => {
  const groupSession = getGroupCardSession(groupConversation, member);
  groupSession.inSession = true;
  if (!groupSession.conversationID) {
    const conversationInfo = { conversationType: groupSession.conversationType, targetId: groupSession.targetOpenImId, myOpenImId: groupSession.myOpenImId || '' };
    const { data } = await getConversationIDBySessionType(conversationInfo);
    groupSession.conversationID = data;
  }
  if (!groupSession.latestMessage) {
    // 获取摘要
      const info = { conversationType: groupSession.conversationType, targetId: groupSession.targetOpenImId, myOpenImId: groupSession.myOpenImId };
      const result = await getOneConversationDetail(info);
      console.log('====>getOneConversationDetail', result);
      const latestMsg = result?.data?.latestMsg
      if (latestMsg) {
        const latestMessage = safeParseJson(latestMsg);
        const msgItem = msgToUseChat(latestMessage);
        if (msgItem) {
          // 转换消息格式
          delete msgItem?.textElem;
          delete msgItem?.attachedInfoElem;
          delete msgItem?.receipts;
          const lastMsg = getMsgToStore(msgItem);
          if(lastMsg){
            groupSession.msgId = lastMsg.messageUId;
            groupSession.latestMessage = lastMsg;
            groupSession.updateTime = lastMsg.sentTime;
          }
          groupSession.unreadCount = result.data.unreadCount;
        }
      }
  }
  return groupSession;
};
/**
 * 更新群聊信息
 * @param group 群ID
 * @param card 当前群的身份卡
 */
export const updateGroupRelation = async (group: string, card?: string) => {

  // 面对面建群推送的是1更新群，判断群不存在先本地创建群
  const msgStore = useMessageStore();
  const uiConversations = msgStore.sessionList.find(item => item.targetId === group);
  const dbConversations = await queryConversationByIds({ targets: group });
  if (!uiConversations && !dbConversations?.length) {
    onGroupEnter(group);
    return;
  }

  const info = await getGroupInfo(group, card);
  if (!info) return;
  const { members, ...groupInfo } = info;

  const groupConversation = uiConversations?.[0] || dbConversations?.[0] || getSessionByGroupRelation(info);
  members.forEach(async item => {
    const groupSession = await getGroupSession(groupConversation, item);
    const dbExist = dbConversations?.some(item => item.localSessionId === groupConversation.localSessionId);
    if (!dbExist) {
      insertConversations([groupSession]);
    } else {
      onDbConversationUpdate([groupSession]);
    }
  });

  msgStore.sortSessions('updateGroupRelation', true, false);
  return;
};


export const onGroupMembersUpdate = async (id: string) => {
  const members = await getGroupSessionMembers(id);
  if (members?.length) {
    useMessageStore().onGroupMembersUpdate({groupId: id, members});
  }
  return members;
}

// 群创建
export const onGroupEnter = async (id: string) => {
  // 请求群信息
  const res = await getGroupSessionInfo(id);
  console.log('=====>getGroupSessionInfo',res);
  if (!res?.myCards?.length) { return }
  const session = getSessionByGroupRelation(res);
  let cards = res.myCards
  if (Array.isArray(cards)) {
    cards?.forEach(async item => {
      const conversation = await getGroupSession(session, item);
      // 可能群还没有创建完成，消息和conversationChange已经来了。
      useMessageStore().onAddToSessionList(conversation, false, '_onGroupEnter');
      await insertConversations([conversation]);
    });
  }
  useMessageStore().sortSessions('onGroupEnter', true, true);
};

// 群成员添加通知处理
export const onAddGroupMembers = async (data: {id: string, card_ids: string[], senderUserId: string}) => {
  const members = useMessageStore().allMembers.get(data.id);
  // 我进群的身份
  const myCards = getImCardIds();
  const myGroupNewCards = data.card_ids.filter(c => myCards.includes(c));
  console.log('====>members我进群的身份', members, myGroupNewCards);
  // 更新成员数据,群头像名称等
  const groupData = await getGroupSessionInfo(data.id);
  if (myGroupNewCards.length) {
    const notINGroup = myGroupNewCards.filter(item =>!members?.get(item));
    console.log('====>notINGroup', notINGroup);
    // 我的身份已经在群里不做处理，多身份需要做处理
    if (!notINGroup.length) return;
    // if (myCards.includes(data.senderUserId)) return; // 自己发送的不处理
  } else {
    return;
  }

  // 邀请的我，或者多身份入群，请求群信息
  const { session, myGroupCards } = parseGroupRelation(groupData);
  console.log('=====>parseGroupRelation', session, myGroupCards);
  const dbConversations = await queryConversationByIds({targets: data.id});
  const msgStore = useMessageStore();

  // 新加入的群聊，群聊创建在 store 中接收消息处处理
  if (groupData.attachment.hasLookForHistory && myGroupCards.length === 1) {
    if (!session.conversationID) {
      const conversationInfo = { conversationType: session.conversationType, targetId: session.targetOpenImId, myOpenImId: myGroupCards[0].open_im_id || '' };
      const { data } = await getConversationIDBySessionType(conversationInfo);
      session.conversationID = data;
    }
    // setTimeout(() => {
      // useMessageStore().onGroupEnterMsgSync({conversationType: session.conversationType, conversationID: data, targetId: session.targetId });
    // }, 300);
     return;
  }

  // 更多身份入群
  myGroupNewCards.forEach(async cId => {
    const card = groupData.members.find(item => item.openid === cId);
    const conversation = await getGroupSession(session, card);
    console.log('=====>myGroupNewCardsInfo', session, card, conversation);
    msgStore.onAddToSessionList(conversation, true, '更多身份入群');

    // useMessageStore().onGroupEnterMsgSync( {conversationType:session.conversationType,conversationID:session.conversationID,targetId:session.targetId });

    if (dbConversations.find(s => s.localSessionId === conversation.localSessionId)) {
      await onDbConversationUpdate(conversation);

    } else {
      await insertConversations([conversation]);

    }
  });
  console.log('=====>onAddGroupMembers',{group: data.id, cards: myGroupNewCards});
}

// 群成员移除通知处理
export const onRemoveGroupMembers = async (data: {id: string, card_ids: string[]}) => {
  const members = await onGroupMembersUpdate(data.id);
  if (members === null) { return }

  if (members?.length) {
    const myCards = getImCardIds();
    data.card_ids.forEach(card => {
      if (myCards.includes(card)) {
        onGroupKickedNotify(data.id, true, card);
      }
    })

  } else {
    onGroupKickedNotify(data.id);
  }
}

// 更新顶置群公告通知
export const updateGroupNotice = (groupId) => {
  const msgStore = useMessageStore();
  if (msgStore.chatingSession && msgStore.chatingSession.targetId === groupId) {
    useGroupInviteStore().getGroupNotice({ group_id: groupId, pin: true, card_id: msgStore.chatingSession.myCardId }, msgStore.chatingSession.myCardId);
  }
};

/**
 * 修改会话置顶、免打扰设置
 * @param conversation
 */
export const updateConversationTopAndMute = (conversation: ConversationToSave) => {
  updateConversation(conversation);
}

export const onDeleteConversation = (info: { main?: string, peer?: string, localSessionId?: string, group?: string, deleHistory?:boolean }, isSelfDelete = true) => {
  const store = useMessageStore();
  console.log('=====>onDeleteConversation',info);
  if (info.group) {
    store.onDeleteConversation({ targetId: info.group, deleHistory: info.deleHistory });
    return;
  }

  if (info.main && info.peer) {
    const localSessionId = getSessionLocalIdByCards(info.main, info.peer);
    const res = store.onDeleteConversation({ localSessionId, deleHistory: info.deleHistory }, isSelfDelete);
    if (res === 'notTemporary') {
      updatePrivateRelation(info.main, info.peer)
    }
    return;
  }

  if (info.localSessionId) {
    store.onDeleteConversation({ localSessionId: info.localSessionId, deleHistory: info.deleHistory });
  }
};
export const onGroupKickedNotify = async (group: string, alert = true, card?: string) => {
  const msgStore = useMessageStore();
  // 当前会话已经退出群聊了，群解散不做处理。
  if (msgStore.chatingSession.targetId === group && !msgStore.chatingSession.inSession){
    return;
  }
  // 更新数据
  const uiConversations = msgStore.sessionList.filter(item => item.targetId === group);
  uiConversations.forEach(item => {
    if (group === item.targetId && (!card || (card && card === item.myCardId))) {
      item.inSession = false;
      onDbConversationSetting(item);
    }
  });

  // 如果被从当前的群聊中踢出，则弹窗提示
  if (alert && msgStore.chatingSession && msgStore.chatingSession.targetId === group) {
    let kickOutType = '';
    const info = await getGroupDetailByCard(group, card ?? msgStore.chatingSession.myCardId);
    const cardInfo = info?.members?.[0];
    // 群聊是否解散
    const isGroupDelete = info.removed;
    // 当前是否在群聊中
    const isCardDelete = cardInfo.removed
    // 非自己退群的情况，提示用户被移除群聊
    if ((isGroupDelete || isCardDelete) && !cardInfo.self_delete) {
      // 移除类型：被踢出kickOut、解散dissolution
      kickOutType = isGroupDelete ? 'dissolution' : 'kickOut';
    }

    msgStore.onGroupKickedAlert(group, card, kickOutType);
  }
}
