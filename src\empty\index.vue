<template>
  <div
    class="wrap"
    :style="wrapStyles"
  >
    <slot>
      <img
        :src="iconMap[name]"
        alt=""
        :style="iconStyles"
      >
    </slot>

    <div class="tip">
      <slot name="tip">
        {{ tip }}
      </slot>
    </div>

    <slot name="bottom" />
  </div>
</template>

<script setup lang="ts">
import { computed, CSSProperties, PropType } from 'vue';

import noData from './assets/no-data.svg';
import fail from './assets/fail.svg';
import success from './assets/success.svg';
import offline from './assets/offline.svg';
import violation from './assets/violation.svg';
import slogan from './assets/slogan.svg';
import no_detail from './assets/no_detail.svg';
import no_search_contact from './assets/no-search-contact.svg';
import no_result from './assets/no-result.svg';
import no_order from './assets/no-order.svg';
import no_message from './assets/no-message.svg';
import no_like from './assets/no-like.svg';
import no_goods from './assets/no-goods.svg';
import no_friend_list from './assets/no-friend-list.svg';
import no_forward from './assets/no-forward.svg';
import no_fans from './assets/no-fans.svg';
import no_data_new from './assets/no-data-new.svg';
import no_data_activity from './assets/no-data-activity.svg';
import no_contact from './assets/no-contact.svg';
import no_comment from './assets/no-comment.svg';
import no_collect from './assets/no-collect.svg';
import no_auth from './assets/no-auth.svg';
import no_address from './assets/no-address.svg';
import new_album from './assets/new-album.svg';
import image_fail from './assets/image-fail.svg';
import fail_location from './assets/fail-location.svg';
import chat_init from './assets/chat-init.svg';
import _502 from './assets/502.svg';
import _404 from './assets/404.svg';

const iconMap = {
  '404': _404,
  '502': _502,
  fail,
  success,
  offline,
  'chat-init': chat_init,
  'fail-location': fail_location,
  'no-address': no_address,
  'no-auth': no_auth,
  'no-collect': no_collect,
  'no-data': noData,
  'no-data-new': no_data_new,
  'no-goods': no_goods,
  'no-message': no_message,
  'no-order': no_order,
  'no-result': no_result,
  'new-album': new_album,
  'no-comment': no_comment,
  'no-fans': no_fans,
  'no-forward': no_forward,
  'no-like': no_like,
  'no-search-contact': no_search_contact,
  'no-friend-list': no_friend_list,
  violation,
  'no-data-activity': no_data_activity,
  'no-contact': no_contact,
  'image-fail': image_fail,
  slogan,
  no_detail,
};

const tipMap = {
  // eslint-disable-next-line quote-props
  '404': '404',
  // eslint-disable-next-line quote-props
  '502': '502',
  fail: '操作失败',
  success: '操作成功',
  offline: '网络断开',
  'chat-init': '聊天初始化',
  'fail-location': '定位失败',
  'no-address': '暂无地址',
  'no-auth': '暂无权限',
  'no-collect': '暂无收藏',
  'no-data': '暂无数据',
  'no-data-new': '暂无数据',
  'no-goods': '暂无商品',
  'no-message': '暂无消息',
  'no-order': '暂无订单',
  'no-result': '搜索无结果',
  'new-album': '新建拾光节点',
  'no-comment': '目前还没有人评论哦',
  'no-fans': '目前还没有粉丝哦',
  'no-forward': '目前还没有人转发哦',
  'no-like': '目前还没有人点赞哦',
  'no-search-contact': '用户不存在',
  'no-friend-list': '马上发一个动态试试吧',
  violation: '内容违反平台规范，无法查阅',
  'no-data-activity': '暂无活动',
  'no-contact': '暂无联络方式',
  'image-fail': '加载失败',
  slogan: '',
  no_detail: '详情未设置',
};
type IconName = keyof typeof tipMap;

const props = defineProps({
  /** 图标名称 */
  name: {
    type: String as PropType<IconName>,
    default: 'no-data',
  },
  /** 图标宽度 */
  width: {
    type: String,
    default: '',
  },
  /** 图标高度 */
  height: {
    type: String,
    default: '',
  },
  /** 提示文案 */
  tip: {
    type: String,
    default: '',
  },
  /** 是否在容器内居中（容器需设置 position: relative） */
  center: {
    type: Boolean,
    default: false,
  },
});

// 是否居中
const wrapStyles = computed<CSSProperties>(() => {
  if (!props.center) return {};
  return {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  };
});

// 图标样式
const iconStyles = computed(() => {
  const { name, width, height } = props;
  const defaultWidth = name === 'slogan' ? '338.414px' : '200px';
  const defaultHeight = name === 'slogan' ? '76.046px' : '200px';

  return {
    width: width || defaultWidth,
    height: height || defaultHeight,
  } as CSSProperties;
});

// 提示文案
const tip = computed(() => {
  if (props.name === 'slogan') return '';
  return props.tip || tipMap[props.name] || tipMap['no-data'];
});
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
}

.tip {
  margin-top: 12px;
  text-align: center;
  color: var(--lingke-contain-fonts, #516082);
}
</style>
