<template>
  <div class="add-contact-modal">
    <t-drawer
      v-if="visible"
      v-model:visible="visible"
      :header="autoHeaderText"
      :close-btn="true"
      size="472px"
      class="drawerSetForm drawerSetBodyNoPadding"
      :footer="(!props.currentRow?.is_connect && tabValue === 2) ? false: true "
    >
      <template #closeBtn>
        <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
          <use href="#close" />
        </svg> -->
        <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
      </template>
      <template #header>
        <div class="header">
          <div class="header-title">{{ autoHeaderText }}</div>

          <div class="header-close" @click.stop="onClose">
            <iconpark-icon name="iconerror" class="iconerror"></iconpark-icon>
          </div>
        </div>
      </template>

      <div class="tab">
        <div class="tab-1 cursor" :class="{'tab-actived':tabValue == 1}" @click="initTabForm();tabValue = 1;">{{ $t('member.manager.phoneAdd') }}</div>
        <div class="tab-2 cursor" :class="{'tab-actived':tabValue == 2}" @click="initTabForm();tabValue = 2;">{{ $t('member.manager.organizeAdd') }}</div>
      </div>

      <div v-if="tabValue == 1" class="toBody11">
        <!-- <t-alert theme="info">
            <template #message>
              <span>批量调整后原来的角色都会被替换为新的角色 </span>
            </template>
          </t-alert> -->
        <div class="tips">输入手机号码添加组织联系人。添加后，对方将收到短信通知</div>

        <div class="form">
          <t-form
            ref="form"
            :data="formData"
            label-align="top"
            layout="vertical"
            label-width="120px"
            :rules="rules"
          >
            <t-form-item :label="$t('member.manager.name')" name="name">
              <t-input
                v-model="formData.name"
                :maxlength="50"
                :placeholder="$t('member.impm.input_1')"
                :disabled="type"
              />
            </t-form-item>
            <t-form-item :label="$t('member.manager.phoneNum')" name="telephone" :disabled="type">
              <t-input-adornment style="width: 100%">
                <template #prepend>
                  <div>
                    <area-code v-model="formData.telCode" :disabled="type" />
                  </div>
                </template>
                <t-input
                  v-model="formData.telephone"
                  :placeholder="$t('member.manager.inputPhone')"
                  :disabled="type"
                />
              </t-input-adornment>
            </t-form-item>
            <t-form-item :label="$t('member.manager.email')" name="email">
              <t-auto-complete
                v-model="formData.email"
                :disabled="type"
                :placeholder="type ? '--' : $t('member.manager.inputEmail')"
                :options="emailOptions"
                class="w-full"
                filterable
              />
            </t-form-item>
            <t-form-item :label="$t('member.manager.position')" name="job">
              <t-input
                v-model="formData.job"
                :maxlength="50"
                :placeholder="$t('member.impm.input_1')"
              />
            </t-form-item>
            <CImageUploadComp
              :attrs="attachImage"
              :multiple="false"
            />
          </t-form>
        </div>
      </div>

      <div v-if="tabValue == 2" class="toBody11">
        <div class="form" v-if="props.currentRow?.is_connect">
          <t-form
            ref="form"
            :data="formData1"
            label-width="120px"
            :rules="rules1"
            label-align="top"
          >
            <t-form-item :label="$t('member.manager.name')" name="name">
              <t-input
                v-model="formData1.name"
                :maxlength="50"
                :placeholder="$t('member.impm.input_12')"
                :disabled="type"
                readonly
                @click="onSelectMember"
              />
            </t-form-item>
            <t-form-item :label="$t('member.manager.position')" name="job">
              <t-input
                v-model="formData1.job"
                :maxlength="50"
                :placeholder="$t('member.impm.input_1')"
              />
            </t-form-item>
            <CImageUploadComp
              :attrs="attachImage"
              :multiple="false"
            />
          </t-form>
        </div>
        <div v-else class="form" style="padding-top: 20%">
          <Empty name="fail" :tip="'组织未连接，请先连接平台'"/>
        </div>
      </div>


      <template v-if="tabValue == 1" #footer>
        <div class="footer">
          <t-button
            theme="default"
            variant="outline"
            class="footer-b"
            @click="onClose"
          >{{ $t('member.impm.input_8') }}</t-button>
          <t-button
            theme="primary"
            class="footer-b"
            :loading="loading"
            @click="onSave"
          >{{ $t('member.manager.send') }}</t-button>
        </div>
      </template>

      <template v-if="tabValue == 2" #footer>
        <div class="footer">
          <t-button
            theme="default"
            variant="outline"
            class="footer-b"
            @click="onClose"
          >{{ $t('member.impm.input_8') }}</t-button>
          <t-button
            class="footer-b"
            theme="primary"
            :loading="loading"
            @click="onSave2"
          >{{ $t('member.impm.input_9') }}</t-button>
        </div>
      </template>
    </t-drawer>

  </div>
  <!-- <t-dialog
    v-model:visible="visible"
    :header="autoHeaderText"
    class="createUpdate"
    :z-index="2500"
    attach="body"
    width="528px"
  >
    <template #body>
      <div class="toBody11">

        <div class="form">
          <t-form
            ref="form"
            :data="formData"
            label-align="right"
            layout="vertical"
            label-width="120px"
            :rules="rules"
          >
            <t-form-item :label="'姓名'" name="name">
              <t-input
                v-model="formData.name"
                :maxlength="50"
                :placeholder="'请输入'"
                :disabled="type"
              />
            </t-form-item>
            <t-form-item :label="'手机号码'" name="telephone">
              <t-input-adornment style="width: 100%" :disabled="type">
                <template #prepend>
                  <div>
                    <area-code v-model="formData.telCode" :disabled="type" />
                  </div>
                </template>
                <t-input
                  v-model="formData.telephone"
                  placeholder="请输入手机号"
                  :disabled="type"
                />
              </t-input-adornment>
            </t-form-item>
            <t-form-item label="邮箱" name="email">
              <t-auto-complete
                v-model="formData.email"
                :disabled="type"
                :placeholder="'请输入邮箱'"
                :options="emailOptions"
                class="w-full"
                filterable
              />
            </t-form-item>
            <t-form-item :label="'所在单位岗位'" name="job">
              <t-input
                v-model="formData.job"
                :maxlength="50"
                :placeholder="'请输入'"
              />
            </t-form-item>
          </t-form>
        </div>
      </div>
    </template>
    <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-dialog> -->
  <tip
    v-model:visible="tipVisible"
    :tip="checkPhoneTip"
    :btn-confirm="'确定变更'"
    :btn-cancel="$t('account.cancel')"
    @onconfirm="changeRegion"
  />

  <approval-organize-select-modal
    ref="organizeSelectModalRef"
    :header="'添加组织成员'"
    :radio-flag="false"
    :single="true"
    :team-id="currentTeamId"
    :relate-team-id="props.relateTeamId"
    :close-on-overlay-click="false"
    :show-modal-list-index-array="[1]"
    :is-filter="true"
    :is-department="false"
    :only-project="true"
    :selected="selected"
    @on-select-item="onSelectItem"
  />
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import { debounce } from "lodash";

import { ref, reactive, Ref, computed, toRaw, nextTick } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import {
  createMemberContactAxios,
  createMemberOrderAxios, createOrganizeContactAxios,
  getRegularListAxios,
  patchContactorAxios,
  patchMemberOrderAxios
} from "@renderer/api/association/api/businessApi";
import { checkPhoneNumber, getResponseResult, priceRecovery } from "@renderer/utils/myUtils";
import areaCode from "@renderer/components/account/AreaCode.vue";
import tip from "@renderer/views/setting/dialog/tip.vue";
import CImageUploadComp from "@/components/free-from/runtime/components/AvatarImageUpload.vue";
import Empty from "@renderer/components/common/Empty.vue";

import dayjs from "dayjs";
import { checkPhoneAndMatch } from "@renderer/components/account/util";
// import approvalOrganizeSelectModal from "@renderer/views/approve/approve_home/components/approvalOrganizeSelectModal.vue";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";
import approvalOrganizeSelectModal from "@renderer/views/member/member_home/panel/regular-member-panel/components/peopleSelectModal.vue";

import { platform as platformCon } from "@renderer/views/digital-platform/utils/constant";
import { useRoute } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const emailSuffix = ["@qq.com", "@163.com", "@gmail.com", "@126.com"];
const emailOptions = computed(() => {
  const emailPrefix = formData.value.email.split("@")[0];
  if (!emailPrefix) return [];

  return emailSuffix.map((suffix) => emailPrefix + suffix);
});
const attachImage = reactive({
  required: false,
  name: "照片",
  editable: true,
  max: 1,
  aspectRatio: 0.7142,

  value: []
});
const props = defineProps({
  memberId: {
    type: Number,
    default: 0
  },
  isMember: {
    type: Number,
    default: 1
  },
  platform: {
    type: String,
    default: '',
  },
  relateTeamId: {
    type: String,
    default: '',
  },
  currentRow: {
    type: Object,
    default: ()=> null
  }
});
const type = ref(0); // 0创建 1编辑

const form = ref(null);
const form1 = ref(null);
const rules = {
  name: [
    {
      required: true,
      message: "请输入",
      type: "error",
      trigger: "blur"
    }
  ],
  job: [
    {
      required: false,
      message: "请输入",
      type: "error",
      trigger: "blur"
    }
  ],
  email: [
    // {
    //   message: '请输入正确的邮箱',
    //   // validator: validatorEmail,
    //   required: false,
    //   trigger: 'blur',
    // },
    { email: { ignore_max_length: true }, message: t("identity.inputTipEmail") },
  ],
  telephone: [
    {
      required: true,
      message: "请输入",
      type: "error",
      trigger: "blur"
    },
    {
      validator: (val) => {
        const errLen = !/^[0-9-]*$/.test(val);
        console.log(!/^[0-9-]*$/.test(val), errLen, '内容');
        if (errLen) return { result: false, message: '请输入正确的手机号码', type: 'error' };
        if (!checkPhoneNumber(formData.value.telCode, formData.value.telephone)) {
          return {
            message: `请输入正确的手机号码`,
            required: true,
            trigger: "blur"
          };
        }
        return { result: true, type: 'success' };
      },
      trigger: 'blur'
    },
    { max: 11, message: '输入字数应在6到11之间', type: 'error', trigger: 'blur' },
    { min: 6, message: '输入字数应在6到11之间', type: 'error', trigger: 'blur' },

  ]

  // remark: [
  //   {
  //     required: false,
  //     message: "请输入",
  //     type: "error",
  //     trigger: "blur"
  //   }
  // ]
};
const rules1 = {
  name: [
    {
      required: true,
      message: "请输入",
      type: "error",
      // trigger: "blur"
    }
  ],
  job: [
    {
      required: false,
      message: "请输入",
      type: "error",
      trigger: "blur"
    }
  ]
};
const visible = ref(false);

let formData = ref({
  name: "", // 缴费金额，单位：元
  telephone: "",
  telCode: "86",
  job: "",
  email: "",
  photo: "",
  id: 0
});

let formData1 = ref({
  name: "", // 缴费金额，单位：元
  id_staff: 0, // 内部成员id
  job: "",
  photo: "",
});

const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => props.platform || route.query?.platform);



const currentTeamId = computed(() => {
  if (platformCpt.value === platformCon.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } if (platformCpt.value === platformCon.digitalWorkbench) {
    return route.query?.teamId || 0;
  }
    return getAssociationTeamID();

});




const tabValue = ref(1);

const initForm = () => {
  attachImage.value = [];
  formData.value = Object.assign(formData.value, {
    name: "",
    telephone: "",
    telCode: "86",
    job: "",
    email: "",
    photo: ""
  });
  formData1.value = Object.assign(formData1.value, {
    name: "", // 缴费金额，单位：元
    id_staff: 0, // 内部成员id
    job: "",
    photo: "",
  })
};

const initTabForm = () => {
  selected.value = [];
  if (tabValue.value == 1) {
    formData.value = {
      name: "", // 缴费金额，单位：元
      telephone: "",
      telCode: "86",
      job: "",
      email: "",
      photo: "",
      id: 0
    };
    attachImage.value = [];
  }
  if (tabValue.value == 2) {
    formData1.value = {
      name: "", // 缴费金额，单位：元
      id_staff: 0, // 内部成员id
      photo: "",
      job: ""
    };
    attachImage.value = [];
  }
};

const autoHeaderText = computed(() =>
  (type.value ? "编辑联系人" : "添加联系人"));

// const props = defineProps({
//   levelOptions: {
//     // 会员职务列表
//     type: Array,
//     default: () => []
//   } // 0创建 1编辑
// });

const emits = defineEmits(["reload"]);
const loading= ref(false);
const onSave = debounce(() => {
  form.value
    .validate({ showErrorMessage: true })
    .then(async (validateResult) => {
      if (validateResult && Object.keys(validateResult).length) {
        console.log(formData.value);
      } else {
        if (!checkPhone()) return;
        let params: any = {};

        // params.money = priceRecovery(params.money);
        let res = null;
        try {
          loading.value = true;
          if (!type.value) {
            params = {
              ...toRaw(formData.value),
              association_id: props.memberId,
              is_member: props.isMember
            };

            if(attachImage.value.length > 0) {
              params.photo = attachImage.value[0].file_name;
            }
            delete params.id;
            res = await createMemberContactAxios(params, currentTeamId.value);
          } else {
            params = {
              job: formData.value.job
            };

            if(attachImage.value.length > 0) {
              params.photo = attachImage.value[0].file_name;
            }
            res = await patchContactorAxios(formData.value.id, params, currentTeamId.value);
          }
          res = getResponseResult(res);
          loading.value = false;

          if (!res) return;
          if (type.value) {
            MessagePlugin.success("操作成功");
          } else {
            MessagePlugin.success(
              "邀请发送成功，联系人同意后加入"
            );
          }
          setTimeout(() => {
            onClose();
            emits("reload");
          }, 500);
        } catch (error) {
          console.log(error);
          const errMsg = error instanceof Error ? error.message : error;
          MessagePlugin.error(errMsg);
        }
        loading.value = false;

      }
    });
}, 500);

const onSave2 = debounce(() => {
  form.value
    .validate({ showErrorMessage: true })
    .then(async (validateResult) => {
      if (validateResult && Object.keys(validateResult).length) {
        console.log(formData1.value);
      } else {
        // if (!checkPhone()) return;
        let params: any = {};

        // params.money = priceRecovery(params.money);
        let res = null;
        loading.value = true;
        try {
          if (!type.value) {
            params = {
              ...toRaw(formData1.value),
              association_id: props.memberId,
              is_member: props.isMember
            };
            if(attachImage.value.length > 0) {
              params.photo = attachImage.value[0].file_name;
            }
            // delete params.id;
            res = await createOrganizeContactAxios(params, currentTeamId.value);
          } else {
            // params = {
            //   job: formData.job
            // };
            // res = await patchContactorAxios(formData.id, params);
          }
          res = getResponseResult(res);
          loading.value = false;

          if (!res) return;
          MessagePlugin.success("操作成功");
          setTimeout(() => {
            onClose();
            emits("reload");
          }, 500);
        } catch (error) {
          MessagePlugin.error(error.message);
        }
        loading.value = false;

      }
    });
}, 500);

const organizeSelectModalRef = ref(null);
const onSelectMember = async () => {
  // shareVisible.value = true;
  await organizeSelectModalRef.value.onOpen('association', selected.value);
  // await organizeSelectModalRef.value.onOpen();
  // if (formData.id_staff) {
  //   // const mb = { member: [formData.id_staff] };
  //   // console.log(mb);
  //   // organizeSelectModalRef.value.setSelectItem([formData.id_staff]);
  //   setTimeout(() => {
  //     organizeSelectModalRef.value.setCheckedMembersAndDeparts([
  //       formData.id_staff
  //     ]);
  //   }, 500);
  // } else {
  // }
};
const selected = ref([]);
const onSelectItem = (item) => {
  // selectedPerson.value = item;
  console.log(item);
  // if (organizeSelectType.value === 1) {
  //   formData.leader = item.idStaff;
  //   formData.leader_name = item.name;
  //   form.value.clearValidate(["leader_name"]);
  // } else if (organizeSelectType.value === 2) {
  //   formData.contact = item.idStaff;
  //   formData.contact_name = item.name;
  //   // form.value.clearValidate(["contact_name"]);
  // }
  selected.value = item;
  // selected.value.push(item.idStaff);
  if (tabValue.value === 1) {
    formData.value.name = item[0].staffName;
    // formData.value.id_staff = item[0].idStaff;
  } else {
    formData1.value.name = item[0].staffName;
    formData1.value.id_staff = item[0].staffId;
    formData1.value.job = item[0].job;
  }
  // form.value.clearValidate(["name"]);
};

const tipVisible = ref(false);
const checkPhoneTip = ref("");
let checkRegion = 0;
const changeRegion = () => {
  tipVisible.value = false;
  formData.value.telCode = checkRegion.toString();
  // formData.value.code ? joinTeam() : getCode();
};

// 校验手机号的合法性
const checkPhone = () => {
  checkRegion = checkPhoneAndMatch(+formData.value.telCode, formData.value.telephone);
  if (!checkRegion) {
    MessagePlugin.error({
      content: "请填写正确的手机号",
      duration: 3000
    });
    return false;
  }
  if (checkRegion !== +formData.value.telCode) {
    (checkPhoneTip.value = `检测到你输入的手机号对应的国际区号为“+${checkRegion}”，是否为你自动变更区号`),
      (tipVisible.value = true);
    return false;
  }
  return true;
};

/**
 *
 * @param data 值不为空说明为编辑状态
 */
// const edit_data = ref(null);
const onOpen = (data?: any) => {
  if (data) {
    // edit_data.value = data;
    type.value = 1;

    initForm();
    formData.value.name = data.name;
    formData.value.email = data.email;
    formData.value.job = data.job;
    formData.value.telCode = data.telcode;
    formData.value.telephone = data.telephone;
    // formData.money = Number(data.money / 100);
    formData.value.id = data.id;
  } else {
    type.value = 0;
    // form.value.reset();
    initForm();
  }
  visible.value = true;
};

const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
//@import "@renderer/views/engineer/less/common.less";
:deep(.uploadBox) {
  .add {
    width: 76px;
    height: 108px;
  }
}
:deep(.img-list) {
  img {
    width: 76px !important;
  }
}


.add-contact-modal {

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    &-title {
      color: var(--kyy_color_modal_title, #1a2139);

      /* kyy_fontSize_3/bold */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }
    &-close {
      .iconerror {
        font-size: 24px;
        color: #516082;
      }
    }
  }

  :deep(.t-form__item) {
    margin-right: 0;
    margin-bottom: 20px !important;
  }

  :deep(.t-form__label) {
    white-space: wrap !important;
  }
  :deep(.t-input-adornment) {
    width: 100%;
  }
  :deep(.t-input--auto-width) {
    width: 90px;
  }

  .t-alert--info {
    padding: 8px 16px;
  }
  .form {
    margin-top: 14px;
  }

  .tab {
    display: flex;
    height: 56px;
    padding: 0 24px;
    align-items: center;
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    .tab-1,.tab-2 {
      color: var(--text-kyy_color_text_1, #1A2139);
      text-align: center;

      /* kyy_fontSize_3/regular */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }

    .tab-1,.tab-2 {
      position: relative;
      margin-right: 44px;
    }

    .tab-actived {
      color: var(--brand-kyy_color_brand_default, #4D5EFF);
      /* kyy_fontSize_3/bold */
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }

    .tab-actived:after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 16px;
      height: 3px;
      border-radius: 1.5px;
      background: var(--brand-kyy_color_brand_default, #4D5EFF);

    }
  }

  .toBody11 {
    padding: 0 24px;
    .tips {
      color: var(--text-kyy_color_text_3, #828DA5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-top: 16px;
      margin-bottom: 10px;
    }
  }
}

.toFooter {
  display:flex;
  justify-content: space-between;
  align-items: center;
  .check {
    color: var(--checkbox-kyy-color-checkbox-text-default, #1A2139);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;

    display:flex;

    align-items: center;
    .tap {
      // line-height: 25px; /* 157.143% */
      display:flex;

      align-items: center;
      gap: 4px;
      .iconhelp {
        font-size: 20px;
        color: #828DA5;

      }
    }
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  &-b {
    min-width: 80px;
  }
}


</style>
<style lang="less" scoped>
//@import "@renderer/views/engineer/less/common.less";

.createUpdate {
  .toBody {
    height: 60vh;
    overflow: auto;
  }
  .t-dialog__header {
    padding: 0 24px;
  }
  .t-dialog__footer {
    padding: 0 24px;
  }

  .t-dialog--default {
    padding-left: 0;
    padding-right: 0;
  }
}

.setWidth {
}

.t-input-adornment__prepend {
  background: #fff!important;
}
</style>

