import { clientOrgRequest, globalRequest } from './index';
import { BaiduMapSuggestionParams } from './models/api';

// 获取使用技巧
export const getSkill = (params: any) =>
  clientOrgRequest({ url: `/skill`, method: 'get', params });

// 获取上传的token
export const getStsToken = () =>
  clientOrgRequest({ url: `/oss/sts-token`, method: 'get' });

/**
 * 地点输入提示
 * https://lbsyun.baidu.com/faq/api?title=webapi/place-suggestion-api
 * @param params 参数
 */
export const baiduMapSuggestion = (params: BaiduMapSuggestionParams) =>
  globalRequest({
    url: '/baidumap/place/v2/suggestion',
    method: 'get',
    params: { ...params, output: 'json' },
  });

/**
 * 普通IP定位
 * @param params 参数
 */
export const baiduMapIPLocation = (params?: { ip?: string }) =>
  globalRequest({
    url: '/baidumap/location/ip',
    method: 'get',
    params,
  });
