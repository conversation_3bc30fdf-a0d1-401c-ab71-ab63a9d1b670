.t-table:not(.t-table--striped) .t-table__footer>tr {
  background: #fff !important;

  .t-table__row-full-element {
    padding-left: 15px !important;
  }
}

.t-table th,
.t-table td {
  padding: 12px !important;
}

.t-table {
  color: var(--text-kyy-color-text-1, #1A2139);
  font-size: 14px;
  font-weight: 400;
}

.t-table__header {
  tr {

    th {
      border-bottom: none !important;
    }
  }
}

.t-table__empty-row {
  td {
    border-bottom: none !important;

  }
}

.clouddisk-home-table {
  z-index: 2499!important;
}

//table
.t-table__header th {
  background: var(--kyy-color-table-hrading-bg, #E2E6F5) !important;
  border-right: 1px solid var(--border-kyy-color-border-white, #FFF);
  color: var(--kyy-color-table-hrading-text, #516082) !important;
  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600 !important;
  line-height: 22px;
  /* 157.143% */
}

.t-table__header th:last-child {
  border-right: none !important;

}

.t-table .t-table__th-row-select,.t-table .t-table__cell-check {
  padding: 0!important;
}

.t-table--bordered {
  .t-table__content {
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);

  }

  td,
  th {
    border-left: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5) !important;
  }
}
// .container {
//   .t-table__content {
//     overflow: overlay  !important;
//   }
// }

.t-table--hoverable tbody tr:hover {
  background: var(--kyy_color_table_bg_hover, #F3F6FA) !important;
}

.t-table__body {
  td {
    color: var(--kyy_color_table_text, #1A2139);
  }
}


.tableSet {
  color: var(--text-kyy-color-text-1, #1A2139) !important;
}


.systemTable {
  .t-table {
    td {
      border-bottom: #ECEFF5;
    }
  }
}
