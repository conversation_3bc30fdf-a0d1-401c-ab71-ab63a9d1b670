import type Sortable from 'sortablejs';
import type { SortableOptions } from 'sortablejs';

/**
 * 拖拽排序
 * @param sortableContainer 拖拽排序的容器
 * @param options 拖拽排序的配置
 * @returns 拖拽排序的实例
 */
function useSortable<T extends HTMLElement>(
  sortableContainer: T,
  options: SortableOptions = {},
) {
  const initializeSortable = async () => {
    const Sortable = await import(
      // @ts-expect-error - This is a dynamic import
      'sortablejs/modular/sortable.complete.esm.js'
    );
    const sortable = Sortable?.default?.create?.(sortableContainer, {
      animation: 300,
      delay: 400,
      delayOnTouchOnly: true,
      ...options,
    });

    return sortable as Sortable;
  };

  return {
    initializeSortable,
  };
}

export { useSortable };

export type { Sortable };
