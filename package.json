{"name": "electron-vue-template", "version": "0.0.0", "author": "sky <https://github.com/umbrella22>", "description": "An electron-vue project", "license": "MIT", "main": "./dist/electron/main/main.js", "scripts": {"dev": "tsx .electron-vue/dev-runner.ts", "dev:sit": "tsx .electron-vue/dev-runner.ts -m sit", "build": "tsx .electron-vue/build.ts && electron-builder -c build.json", "build:win32": "tsx .electron-vue/build.ts && electron-builder -c build.json --win  --ia32", "build:win64": "tsx .electron-vue/build.ts && electron-builder -c build.json --win  --x64", "build:mac": "tsx .electron-vue/build.ts && electron-builder -c build.json --mac", "build:dir": "tsx .electron-vue/build.ts && electron-builder -c build.json --dir", "build:clean": "tsx .electron-vue/build.ts --clean", "build:web": "tsx .electron-vue/build.ts --target web", "pack:resources": "tsx .electron-vue/hot-updater.ts", "dep:upgrade": "yarn upgrade-interactive --latest", "postinstall": "electron-builder install-app-deps"}, "engines": {"node": ">=20.17.0", "npm": ">=10.9.0"}, "dependencies": {"electron-updater": "^6.6.5", "webpack": "^5.101.3", "yarn": "^1.22.22"}, "devDependencies": {"@ikaros-cli/prettier-config": "^0.1.0", "@ikaros-cli/stylelint-config": "^0.2.0", "@rspack/core": "^1.3.15", "@rspack/dev-server": "^1.1.3", "@types/adm-zip": "^0.5.7", "@types/fs-extra": "^11.0.4", "@types/node": "^22.13.1", "@types/semver": "^7.7.0", "adm-zip": "^0.5.16", "axios": "^1.9.0", "cfonts": "^3.3.0", "chalk": "^5.4.1", "del": "^7.1.0", "detect-port": "^2.1.0", "dotenv": "^16.5.0", "electron": "^34.5.8", "electron-builder": "^26.0.12", "electron-devtools-vendor": "^3.0.0", "fs-extra": "^11.3.0", "listr2": "^8.3.3", "minimist": "^1.2.8", "pinia": "3.0.1", "sass-embedded": "^1.89.0", "sass-loader": "^16.0.5", "tsx": "^4.19.4", "typescript": "^5.8.3", "vue": "^3.5.16", "vue-loader": "^17.4.2", "vue-router": "^4.5.1"}}