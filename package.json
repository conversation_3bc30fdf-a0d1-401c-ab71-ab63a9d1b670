{"name": "@rk/unitPark", "version": "0.0.11-beta.46", "description": "A vue library developed with dumi", "repository": {"type": "git"}, "license": "MIT", "sideEffects": ["**/*.css", "**/*.less", "**/*.vue"], "type": "module", "exports": {".": {"types": "./dist/typings/index.d.ts", "import": "./dist/index.js"}, "./dist/*": {"types": "./dist/typings/*", "import": "./dist/*", "require": "./dist/*"}, "./dist/assets/style.css": "./dist/assets/style.css"}, "module": "./dist/index.js", "types": "./dist/typings/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "build:watch": "vite build --watch", "cz": "npx cz", "dev": "dumi dev", "docs:build": "dumi build", "docs:preview": "dumi preview", "lint": "npm run lint:es && npm run lint:css", "lint:css": "stylelint \"{src,test}/**/*.{css,less}\"", "lint:es": "eslint \"{src,test}/**/*.{js,jsx,ts,tsx,vue}\"", "prepare": "husky install", "prepare(暂不使用)": "husky install && dumi setup", "prepublishOnly(暂不使用)": "npm run test && npm run build", "start": "npm run dev", "test": "vitest", "test:cov": "vitest --coverage", "typecheck": "vue-tsc --noEmit", "v:beta": "npm version prerelease --preid=beta --no-git-tag-version", "v:major": "npm version major", "v:minor": "npm version minor", "v:patch": "npm version patch", "version": "npx conventional-changelog -p angular -i ./docs/changelog.md -s && git add docs/changelog.md"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{md,json}": ["prettier --write --no-error-on-unmatched-pattern", "git add"], "src/*.{css,less}": ["stylelint --fix", "prettier --write", "git add"], "*.{js,jsx}": ["eslint --fix", "prettier --write", "git add"], "*.{ts,tsx}": ["eslint --fix", "prettier --parser=typescript --write", "git add"]}, "dependencies": {"@amap/amap-jsapi-types": "^0.0.15", "@lynker-desktop/web": "^0.0.18", "@types/sortablejs": "^1.15.8", "@vueuse/core": "^12.7.0", "ali-oss": "^6.22.0", "await-to-js": "^3.0.0", "axios": "^1.7.9", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "lodash": "^4.17.21", "p-limit": "^6.2.0", "sortablejs": "^1.15.6", "tdesign-vue-next": "^1.10.6", "vue-tippy": "^6.6.0", "vue3-baidu-map-gl": "^2.6.5"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@commitlint/cz-commitlint": "^19.8.1", "@dumijs/preset-vue": "^2.4.12", "@eslint/js": "^9.11.1", "@rk/editor": "0.4.3", "@rollup/plugin-babel": "^6.0.4", "@types/ali-oss": "^6.16.11", "@types/lodash": "^4.17.16", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "@umijs/lint": "^4.3.24", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vitest/coverage-v8": "^2.1.1", "@vue/test-utils": "^2.4.6", "chalk": "^5.4.1", "commitizen": "^4.3.1", "dumi": "^2.4.13", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.17.0", "happy-dom": "^15.7.4", "husky": "^8.0.1", "less": "^4.2.0", "lint-staged": "^15.5.0", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^3.0.0", "prettier-plugin-packagejson": "^2.2.18", "react": "^18.0.0", "react-dom": "^18.0.0", "stylelint": "^14.9.1", "typescript": "~5.5.4", "uuid": "^11.1.0", "vite": "^5.4.8", "vitest": "^2.1.1", "vue": "^3.5.10", "vue-tsc": "^2.1.6"}, "peerDependencies": {"vue": ">=3.3.0"}, "publishConfig": {"access": "public"}, "authors": []}