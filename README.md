# electron-vue-template

![GitHub Repo stars](https://img.shields.io/github/stars/umbrella22/electron-vue-template)
[![vue](https://img.shields.io/badge/vue-3.5.8-brightgreen.svg)](https://github.com/vuejs/vue-next)
[![rspack](https://img.shields.io/badge/rspack-1.0.5-brightgreen.svg)](https://rspack.dev/index)
[![electron](https://img.shields.io/badge/electron-32.1.2-brightgreen.svg)](https://github.com/electron/electron)
[![license](https://img.shields.io/github/license/mashape/apistatus.svg)](https://github.com/umbrella22/electron-vue-template/blob/master/LICENSE)

# Installation

You can choose to clone the project or fork repository, or download the zip file directly. It is recommended to clone the repository so that you can receive the latest patches.

To run a project, you need to have **node version 20** or higher and **use npm as your dependency management tool**

[Document (Chinese only)](https://umbrella22.github.io/electron-vue-template-doc/)

[For Chinese Developers](/README_ZH.md)

# Build Setup

```bash
# Clone this repository
$ git clone https://github.com/umbrella22/electron-vite-template.git
# Go into the repository
$ cd electron-vue-template
# install dependencies
$ npm ci

# serve with hot reload at localhost:9080
$ npm run dev

# build electron application for production
$ npm run build


```

---

# Function list

[x] Auto update
[x] Incremental update
[x] Loading animation before startup
[x] i18n

# Built-in

- [vue-router](https://next.router.vuejs.org/index.html)
- [pinia](https://pinia.esm.dev/)
- [electron](http://www.electronjs.org/docs)
- [typescript](https://www.typescriptlang.org/)
- [rspack](https://rspack.dev/index)

# Note

- [gitee](https://gitee.com/Zh-Sky/electron-vue-template) is only for domestic users to pull code，from github to synchronize，please visit github for PR
- **Welcome to Issues and PR**
