import { multipartUpload } from '../../../utils/upload';

export interface UploadOptions {
  rootDir: string;
  enableCrop?: boolean;
  cropperProps?: any;
  onProgress?: (progress: number) => void;
}

export interface UploadResult {
  status: 'success' | 'fail';
  response?: { url: string };
  error?: any;
}

/**
 * 文件上传处理器
 */
export function useFileUploadHandler() {
  // 直接上传文件
  const uploadFile = async (
    file: any,
    options: UploadOptions,
  ): Promise<UploadResult> => {
    try {
      const uploadFile = Array.isArray(file) ? file[0] : file;

      const result = await multipartUpload(uploadFile, {
        rename: true,
        rootDir: options.rootDir,
        partSize: 102400,
        onProgress: options.onProgress,
      });

      return {
        status: 'success',
        response: { url: result.url },
      };
    } catch (error) {
      return {
        status: 'fail',
        error: String(error) || '上传失败',
      };
    }
  };

  // 处理自定义上传（支持裁剪）
  const handleCustomUpload = async (
    file: any,
    enableCrop: boolean,
    uploadFn: (file: any) => Promise<UploadResult>,
    cropperProps: any = {},
    currentImagesLength: number = 0,
  ): Promise<UploadResult> => {
    // 不启用裁剪，直接上传
    if (!enableCrop) {
      return await uploadFn(file);
    }

    // 启用裁剪的情况下，这里应该集成裁剪逻辑
    // 暂时先直接上传，后续可以扩展
    return await uploadFn(file);
  };

  // 创建进度更新函数
  const createProgressUpdater = (
    updateProgress: (progress: number) => void,
    interval: number = 200,
  ) => {
    let currentProgress = 0;

    const progressInterval = setInterval(() => {
      currentProgress = Math.min(currentProgress + 10, 90);
      updateProgress(currentProgress);
    }, interval);

    return () => clearInterval(progressInterval);
  };

  return {
    uploadFile,
    handleCustomUpload,
    createProgressUpdater,
  };
}
