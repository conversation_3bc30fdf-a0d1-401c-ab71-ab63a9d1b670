import { MessagePlugin, type RequestMethodResponse } from 'tdesign-vue-next';
import { computed, type Ref } from 'vue';
import type { UploadImageEvents, UploadImageProps } from '../type';
import { validateImageFile } from '../utils';
import { useFileUploadHandler } from './useFileUploadHandler';
import { useImageCrop } from './useImageCrop';
import { useImageSort } from './useImageSort';
import { useImageUploadState } from './useImageUploadState';

/**
 * 上传图片的完整业务逻辑
 */
export function useUploadImageLogic(
  modelVal: Ref<string | string[]>,
  props: UploadImageProps,
  emit: (event: keyof UploadImageEvents, ...args: any[]) => void,
) {
  // 为每个组件实例生成一个稳定的唯一ID
  const instanceId = Math.random().toString(36).substr(2, 9);

  // 使用图片上传状态管理
  const uploadState = useImageUploadState(modelVal, {
    maxCount: props.maxCount || 1,
    onStateChange: (urls) => emit('change', urls),
  });

  // 使用文件上传处理器
  const { uploadFile, createProgressUpdater } = useFileUploadHandler();

  // 使用图片裁剪功能
  const cropLogic = useImageCrop();

  // 计算实际使用的容器类名，确保排序功能时类名唯一
  const actualContainerClass = computed(
    () =>
      `${props.containerClass || 'rk-upload-image-container'}-${instanceId}`,
  );

  // 是否可以上传（考虑只读状态）
  const canActuallyUpload = computed(
    () => !props.readonly && uploadState.canUpload.value,
  );

  // 自定义上传方法
  const customUploadImage = async (
    file: any,
  ): Promise<RequestMethodResponse> => {
    const validation = validateImageFile(file, props.accept);
    if (!validation.valid) {
      MessagePlugin.warning(validation.error);
      return {
        status: 'fail' as const,
        response: { error: validation.error },
      };
    }

    // 检查数量限制
    if (!uploadState.canUpload.value) {
      const error = '已达到最大上传数量';
      MessagePlugin.warning(error);
      return {
        status: 'fail' as const,
        response: { error },
      };
    }

    // 添加上传占位符
    const { item: uploadingItem, index: itemIndex } =
      uploadState.addUploadingItem();

    // 创建进度更新器
    const stopProgress = createProgressUpdater((progress) => {
      uploadState.updateProgress(itemIndex, progress);
    });

    try {
      let result: any;

      // 启用裁剪：使用裁剪处理逻辑
      if (props.enableCrop) {
        const directUploadFn = async (uploadFile: any) => {
          return await uploadFile(uploadFile, {
            rootDir: props.rootDir,
            onProgress: (progress: number) =>
              uploadState.updateProgress(itemIndex, progress),
          });
        };

        result = await cropLogic.handleCustomUpload(
          file,
          props.enableCrop,
          directUploadFn,
          props.cropperProps,
          uploadState.images.value.length - 1,
        );

        // 如果是等待裁剪状态，移除占位符但不报错
        if (
          result.status === 'fail' &&
          result.response?.error === 'waiting_for_crop'
        ) {
          uploadState.removeUploadingItem(itemIndex);
          return result;
        }
      } else {
        // 不启用裁剪：直接上传
        result = await uploadFile(file, {
          rootDir: props.rootDir,
          onProgress: (progress: number) =>
            uploadState.updateProgress(itemIndex, progress),
        });
      }

      stopProgress();

      if (result.status === 'success' && result.response?.url) {
        // 上传成功，更新占位符
        const success = uploadState.updateUploadingItem(
          itemIndex,
          uploadingItem.tempId!,
          result.response.url,
        );

        // 如果更新失败，移除占位符
        if (!success) {
          uploadState.removeUploadingItem(itemIndex);
        }

        return {
          status: 'success' as const,
          response: { url: result.response.url },
        };
      } else {
        // 上传失败，移除占位符
        uploadState.removeUploadingItem(itemIndex);

        return {
          status: 'fail' as const,
          response: { error: result.error || '上传失败' },
        };
      }
    } catch (error) {
      stopProgress();
      uploadState.removeUploadingItem(itemIndex);

      return {
        status: 'fail' as const,
        response: { error: String(error) || '上传异常' },
      };
    }
  };

  // 处理裁剪确认的业务逻辑
  const onCropConfirm = (croppedUrl: string) => {
    // 添加裁剪后的图片到列表
    const { item, index } = uploadState.addUploadingItem();
    uploadState.updateUploadingItem(index, item.tempId!, croppedUrl);
  };

  // 创建裁剪处理函数
  const handleCropConfirm = cropLogic.createCropConfirmHandler(
    onCropConfirm,
    emit,
  );
  const handleCropCancel = cropLogic.createCropCancelHandler();

  // 创建响应式的排序配置
  const sortProps = computed(() => ({
    ...props,
    containerClass: actualContainerClass.value,
  }));

  // 内部排序处理函数
  const handleInternalSort = (oldIndex: number, newIndex: number) => {
    if (
      props.autoSort &&
      uploadState.images.value &&
      Array.isArray(uploadState.images.value)
    ) {
      // 自动排序：创建新的数组来确保响应性
      const newImages = [...uploadState.images.value];
      const [removed] = newImages.splice(oldIndex, 1);
      newImages.splice(newIndex, 0, removed);
      uploadState.images.value = newImages;

      const newUrls = newImages.map((v) => v?.url).filter(Boolean);
      modelVal.value = newUrls;
      emit('change', newUrls);
    }

    emit('sort', oldIndex, newIndex);
  };

  // 初始化排序功能
  useImageSort(sortProps, handleInternalSort);

  // 处理图片点击
  const handleImgClick = (index: number, url: string) => {
    const payload = {
      images: uploadState.images.value.map((v) => v?.url),
      index,
      url,
    };

    // @deprecated 实际上是图片点击事件，保留兼容
    emit('preview', payload);
    emit('click', payload);
  };

  return {
    // 状态
    images: uploadState.images,
    canActuallyUpload,
    actualContainerClass,

    // 裁剪相关
    cropperDialogRef: cropLogic.cropperDialogRef,
    handleCropConfirm,
    handleCropCancel,

    // 方法
    customUploadImage,
    removeImage: uploadState.removeImage,
    handleImgClick,
  };
}
