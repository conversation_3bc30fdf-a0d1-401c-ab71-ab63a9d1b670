import { computed, readonly, ref } from 'vue';

/**
 * 图片裁剪 Hook
 * 用于处理图片上传时的裁剪功能
 */
export function useImageCrop() {
  const cropperDialogRef = ref();

  // 当前待裁剪的图片信息
  const pendingCropImage = ref<{
    file?: File;
    uploadFile?: any;
    url?: string;
    index: number;
  } | null>(null);

  // 是否有待裁剪的图片
  const hasPendingCrop = computed(() => !!pendingCropImage.value);

  /**
   * 打开裁剪对话框
   * @param file - 文件对象或文件 URL
   * @param cropperProps - 裁剪器配置
   */
  const openCropDialog = async (
    file: File | string,
    cropperProps: any = {},
  ) => {
    if (file instanceof File) {
      // 将 File 对象转换为 DataURL 以便裁剪器显示
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        if (dataUrl) {
          cropperDialogRef.value?.open(dataUrl, cropperProps);
        }
      };

      reader.onerror = () => {
        console.error('Failed to read file for cropping');
      };
      reader.readAsDataURL(file);
    } else {
      cropperDialogRef.value?.open(file, cropperProps);
    }
  };

  /**
   * 设置待裁剪的图片信息
   * @param imageInfo - 图片信息
   */
  const setPendingCropImage = (
    imageInfo: {
      file?: File;
      uploadFile?: any;
      url?: string;
      index: number;
    } | null,
  ) => {
    pendingCropImage.value = imageInfo;
  };

  /**
   * 清除待裁剪的图片信息
   */
  const clearPendingCropImage = () => {
    pendingCropImage.value = null;
  };

  /**
   * 处理自定义上传（支持裁剪前处理）
   * @param file - 上传的文件
   * @param enableCrop - 是否启用裁剪
   * @param uploadFn - 实际上传函数
   * @param cropperProps - 裁剪器配置
   * @param currentImagesLength - 当前图片列表长度
   */
  const handleCustomUpload = async (
    file: any,
    enableCrop: boolean,
    uploadFn: (file: any) => Promise<any>,
    cropperProps: any = {},
    currentImagesLength: number = 0,
  ) => {
    // 不启用裁剪，直接上传
    if (!enableCrop) {
      return await uploadFn(file);
    }

    // 如果启用裁剪，先保存原始文件，打开裁剪对话框
    setPendingCropImage({
      file: file.raw || file,
      uploadFile: file,
      index: currentImagesLength,
    });

    // 打开裁剪对话框，传入原始文件
    await openCropDialog(file.raw || file, cropperProps);

    // 返回失败状态，阻止 TDesign Upload 组件更新列表
    return {
      status: 'fail' as const,
      response: {
        error: 'waiting_for_crop',
      },
    };
  };

  /**
   * 创建裁剪确认处理函数
   * @param onConfirm - 裁剪确认回调
   * @param emit - 组件emit函数
   */
  const createCropConfirmHandler = (
    onConfirm: (croppedUrl: string, originalUrl: string) => void,
    emit: (
      event: 'crop-confirm',
      croppedUrl: string,
      originalUrl: string,
    ) => void,
  ) => {
    return (croppedUrl: string, originalResponse: any) => {
      if (!pendingCropImage.value) return;

      // 执行确认回调
      onConfirm(croppedUrl, pendingCropImage.value.uploadFile?.url || '');

      // 触发裁剪确认事件
      emit(
        'crop-confirm',
        croppedUrl,
        pendingCropImage.value.uploadFile?.url || '',
      );

      // 清除待裁剪信息
      clearPendingCropImage();
    };
  };

  /**
   * 创建裁剪取消处理函数
   */
  const createCropCancelHandler = () => {
    return () => {
      if (!pendingCropImage.value) return;

      // 清除待裁剪信息，不更新图片列表
      clearPendingCropImage();
    };
  };

  return {
    cropperDialogRef,
    pendingCropImage: readonly(pendingCropImage),
    hasPendingCrop,

    openCropDialog,
    setPendingCropImage,
    clearPendingCropImage,
    handleCustomUpload,
    createCropConfirmHandler,
    createCropCancelHandler,
  };
}
