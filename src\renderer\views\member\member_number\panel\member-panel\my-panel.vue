<template>
  <div ref="containerFlets" class="home">
    <!-- v-show="scrolledDistance > 1000" :class="{isOpacity: scrolledDistance > 1200}" -->
    <!-- <div class="backTop cursor" v-show="scrolledDistance > 180" :class="{isOpacity: scrolledDistance > 200}" @click="scrollToTop">
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>

    </div> -->

    <!-- <div class="backTop cursor" :class="{isOpacity: scrolledDistance > 300}"   @click="scrollToTop">
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>
    </div> -->
    <div class="scroll">
      <div class="area">
        <div class="combine">
          <div class="bg" :class="{ noExchange: memberCards?.length > 1 }">
            <div class="team-head">
              <div class="left">
                <div
                  class="logo-box"
                  @click="callingOpen"
                  @mouseenter="showCalling = true"
                  @mouseleave="showCalling = false"
                >
                  <!-- 单位会员 -->
                  <template v-if="currentMemberCard">
                    <t-image class="logo mlogo" :src="currentMemberCard?.logo" fit="cover">
                      <template #loading>
                        <img src="@renderer/assets/member/svg/avatar_default.svg" />
                      </template>
                      <template #error>
                        <img src="@renderer/assets/member/svg/avatar_default.svg" />
                      </template>
                    </t-image>
                  </template>
                  <template v-else>
                    <img class="logo" src="@renderer/assets/member/svg/avatar_default.svg" />
                  </template>
                  <div v-show="showCalling">
                    <calling @confirm="confirmLogo" ref="callingRef" />
                  </div>
                </div>

                <div class="info">
                  <span class="line-1 max-w-232px tiName">
                    <MyTooltipComp :text="$filters.isPeriodEmpty(currentMemberCard?.staff_name)" />
                  </span>
                  <!-- 所在单位岗位 level_name-->
                  <span class="levelt line-1 max-w-430px" v-if="currentMemberCard?.type === 1">{{
                    currentMemberCard?.is_contact === 1 ? "联系人" : currentMemberCard?.job_name
                  }}</span>
                  <span class="levelt line-1" v-else>
                    {{ t("order.geren") }}
                  </span>
                  <span class="levelt mt-4px line-1" v-if="currentMemberCard?.type === 1">{{
                    currentMemberCard?.name
                  }}</span>
                  <span class="tag mt-8px">
                    <span class="tag-unit line-1 max-w-400px" v-show="currentMemberCard?.level_name">
                      <!-- {{
                        currentMemberCard?.level_name
                      }} -->
                      <MyTooltipComp :text="$filters.isPeriodEmpty(currentMemberCard?.level_name)"/>
                    </span>
                    <template v-if="currentMemberCard?.type === 1">
                      <span v-if="currentMemberCard?.is_connect" class="tag-connect">已连接</span>
                      <span v-else class="tag-unconnect cursor" @click="onConnect(currentMemberCard)">未连接</span>
                    </template>
                  </span>
                </div>
              </div>
              <div class="right">
                <div class="btInvite cursor" @click="onInviteJobClub">
                  <iconpark-icon class="icon" name="iconpeopleadd"></iconpark-icon>
                  <span class="text">
                    {{ $t("member.squarek.f") }}
                  </span>
                </div>
              </div>
            </div>
            <!-- @click="onOpenCardsModal" -->
            <CardsModal
              :accountLists="memberCards"
              :placement="'left-top'"
              :current-card="currentMemberCard"
              @onSetCard="onSetCard"
            >
              <div class="team-info cursor" v-show="memberCards?.length > 1">
                <iconpark-icon name="iconstanding" class="iconstanding"></iconpark-icon>

                <span class="text">
                  {{ $t("member.eb.h") }}
                </span>
              </div>
            </CardsModal>
          </div>
          <template v-if="proxy.$i18n.locale === 'zh-cn'">
            <img class="logoI" src="@/assets/member/icon/my_product_cn.jpg" />
          </template>
          <template v-else>
            <img class="logoI" src="@/assets/member/icon/my_product_hk.jpg" />
          </template>
        </div>
        <div class="bodyk mt-12px 222">
          <div class="sticky">
            <div class="tabs">
              <div
                class="item"
                :class="{
                  active: 1 === currentTab,
                }"
                @click="setTab(1)"
              >
                <!-- 会员资料 -->
                {{ $t("member.sv17.s_4") }}
              </div>
              <div
                v-if="currentMemberCard?.type === 1"
                class="item"
                :class="{
                  active: 2 === currentTab,
                }"
                @click="setTab(2)"
              >
                <!-- 联系人 -->
                全部成员
              </div>
              <div
                class="item"
                :class="{
                  active: 3 === currentTab,
                }"
                @click="setTab(3)"
              >
                {{ $t("member.eb.g") }}
              </div>
            </div>
          </div>

          <div v-if="currentTab === 1" class="comp-box mt-16px">
            <t-loading size="medium" class="memberLoading" :loading="isLoading" show-overlay text="加载中...">
              <div v-if="resData" class="regular mb-80px">
                <LookRegularComp :isHiddenSystem="true" ref="lookRegularModalRef" :platform="platform" />
              </div>
            </t-loading>
            <template v-if="isNetworkError && !resData">
              <div class="noEmpty">
                <Empty name="offline">
                  <template #tip>
                    <div class="tipEmpty">
                      <span class="text">网络链接失败，请检查网络后重试</span>
                      <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                    </div>
                  </template>
                </Empty>
              </div>
            </template>
            <div v-if="!isNetworkError" class="edit-btn">
              <div class="cen">
                <t-button theme="primary" class="b" @click="onEditMember(currentMemberCard)">
                  {{ $t("member.sv17.s_6") }}
                </t-button>
              </div>
            </div>
          </div>
          <div v-else-if="currentTab === 2" class="contact-box">
            <contact-comp :current-row="currentMemberCard" :setting-info="settingInfo" :platform="platformCpt" @reload="onInitData"/>
          </div>
          <div v-else-if="currentTab === 3">
            <!-- 我的广告 -->
            <AdtableList :currentMemberCard="currentMemberCard"></AdtableList>
          </div>
          <!-- <div v-else-if="currentTab === 3">
            <square-comp />
          </div>
          <div v-else-if="currentTab === 4">
            <rich-comp />
          </div>
          <div v-else-if="currentTab === 5">
            <name-comp />
          </div>
          <div v-else-if="currentTab === 7">
            <ebook-comp />
          </div>
          <div v-else-if="currentTab === 6">
            <ActiveComp />
          </div> -->

          <!-- <t-tabs v-if="false" :value="currentTab" @change="onChangeCurrentTab">
            <t-tab-panel :value="1" label="会员资料" :destroy-on-hide="false">
              <LookRegularComp ref="lookRegularModalRef" class="mt-20px" />
              <div class="btn flex-justify-center">
                <t-button
                  theme="primary"
                  @click="onEditMember(currentMemberCard)"
                >     {{ $t('member.sv17.s_6') }}</t-button>
              </div>
            </t-tab-panel>
            <t-tab-panel
              v-if="
                !(currentMemberCard?.is_contact || currentMemberCard.type === 2)
              "
              :value="2"
              :label="$t('member.regular.contact')"
            >
              <contact-comp
                :current-row="currentMemberCard"
                :setting-info="settingInfo"
              />
            </t-tab-panel>
          </t-tabs> -->
        </div>
      </div>
    </div>
  </div>

  <!-- @reload="onSearch" -->
  <AddMemberModal
    ref="addMemberModalRef"
    :is-hidden-arr="['memberLevel', 'joinTime', 'expireTime', 'department', 'relateRespector']"
    :is-member="1"
    @reload="onInitData"
    :platform="platformCpt"
    @on-show-member-flow="onShowMemberFlow"
  />
  <AddInMemberModal ref="addInMemberModalRef" />
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="'邀请入会'"
    :way-tips="$t('member.squarek.e')"
    :member="currentMemberCard"
    :activeAccount="activeAccount"
  />
  <!-- <CardsModal ref="cardsModalRef" @onSetCard="onSetCard"/> -->
  <AnnualConnect
    ref="annualConnectRef"
    :activeAccount="activeAccount"
    @backType="onBackType"
    @refresh="onInitData"
  ></AnnualConnect>
  <Tricks :offset="{ x: '-32', y: '-40' }" :uuid="uuidCpt" :key="uuidCpt"/>
</template>

<script lang="ts" setup>
import {
  getMemberApplyLinkAxios,
  getMemberCardsAxios,
  getMemberSettingAxios,
  getRegularDetailAxios,
  checkIsAdminAxios,
  editSpecifiedFields,
} from "@renderer/api/member/api/businessApi";
import InviteQrcodeModal from "@renderer/views/member/member_number/modal/invite-qrcode-modal.vue";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import AddInMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-in-member-modal.vue";
import Empty from "@renderer/components/common/Empty.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
import {
  ref,
  onMounted,
  onActivated,
  watch,
  Ref,
  toRaw,
  computed,
  getCurrentInstance,
  defineAsyncComponent,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { useMemberStore } from "@renderer/views/member/store/member";
import lodash from "lodash";
import LookRegularComp from "@renderer/views/member/member_number/components/look-regular-comp.vue";
import ContactComp from "@renderer/views/member/member_number/components/contact-comp.vue";
// import SquareComp from "@renderer/views/member/member_number/components/square-comp.vue";
// import ActiveComp from "@renderer/views/member/member_number/components/active-comp.vue";
import calling from "@renderer/views/digital-platform/components/calling.vue";

// import NameComp from "@renderer/views/member/member_number/components/name-comp.vue";
// import EbookComp from "@renderer/views/member/member_number/components/ebook.vue";
// import RichComp from "@renderer/views/member/member_number/components/rich-comp.vue";
import AdtableList from "@/views/member/member_home/panel/mark-advertising/components/AdtableList.vue";

import AddMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-member-modal.vue";
import MyTooltipComp from "@renderer/components/engineer/components/MyTooltipComp.vue";
import { getProfilesInfo } from "@renderer/utils/auth";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { getMemberCurrentCardID, setMemberCurrentCardID } from "@renderer/views/member/utils/auth.ts";
import CardsModal from "@renderer/views/member/member_number/modal/cards-modal.vue";
// const RichComp = defineAsyncComponent(() => import("@renderer/views/member/member_number/components/rich-comp.vue"));
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { TeamAnnualTypeError, onConnectPlatformOrConfirm } from "@renderer/views/digital-platform/utils/auth";
import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";

import AnnualConnect from "@renderer/views/digital-platform/components/AnnualConnect.vue";
// import { Tricks } from '@rk/unitPark';
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const { menuList, routeList, roleFilter } = useRouterHelper("memberIndex");

const router = useRouter();
const { proxy } = getCurrentInstance() as any;

const currentTab = ref(0); // 1 会员资料 2联系人、3会员广场、4会员商机
const memberCards: Ref<any> = ref([]);
const currentMemberCard: Ref<any> = ref(null);
// const store = useMemberStore();
const profile = getProfilesInfo();

const isAdminValue = ref(null);

const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});

const uuidCpt = computed(()=> {
  if(currentTab.value === 1) {
    return '数字商协-会员中心-会员资料'
  } else if(currentTab.value === 2) {
    return '数字商协-会员中心-全部成员'
  } else if(currentTab.value === 3) {
    return '数字商协-会员中心-我的广告'
  }
  return '';
})

const digitalPlatformStore = useDigitalPlatformStore();
let route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt: any = computed(() => {
  return props.platform || route.query?.platform;
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else {
    return getMemberTeamID();
  }
});

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  } else {
    return store.activeAccount;
  }
});

const store: any = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore;
  } else {
    return useMemberStore();
  }
});

// 判断当前用户是否为管理员
const onCheckIsAdmin = async (idStaff) => {
  let res: any = null;
  try {
    res = await checkIsAdminAxios({ idStaff }, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === "Network Error") {
    } else {
      MessagePlugin.error(errMsg);
    }
  }
};

// const scrolledDistance = ref(0); // 滚动距离
// const containerFlets = ref(null);
// const handleScroll = (event) => {
//   console.log(event.target.scrollTop, 'e');
//   scrolledDistance.value = event.target.scrollTop;
//   console.log(scrolledDistance.value, 'few')
//   // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
// };
// let animationId = null;
// const scrollToTop = () => {
//   console.log(containerFlets.value.scrollTop);
//   // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
//   cancelAnimationFrame(animationId); // 取消之前的动画

//       const scrollTop = containerFlets.value.scrollTop;
//       console.log(containerFlets.value.scrollTop);
//       const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
//       console.log(step);
//       const animate = () => {
//         if (containerFlets.value.scrollTop > 0) {
//           containerFlets.value.scrollTop -= step;
//           animationId = requestAnimationFrame(animate); // 请求下一帧动画
//         } else {
//           cancelAnimationFrame(animationId); // 动画结束，取消请求
//         }
//       };

//       animationId = requestAnimationFrame(animate); // 开始动画
// };

const goLeaf = () => {
  // router.push({
  //   path: "/memberIndex/member_manage",
  //   query: {
  //     // projectId: props.projectId
  //   }
  // });
  const searchMenu = routeList.find((v) => v.name === "member_leaflets");
  // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
  router.push({ path: searchMenu.fullPath, query: {} });
  store.addTab(toRaw(searchMenu));
};

const isManage = computed(
  () =>
    store.activeAccount && (isAdminValue.value?.super || isAdminValue.value?.isAdmin || isAdminValue.value?.superAdmin),
);

const emits = defineEmits(["selectPlatform"]);
const onSelectPlatform = (type) => {
  selectApp(type);
  emits("selectPlatform", type);
};

const selectApp = (type) => {
  const arr = store.getStorePlatforms;
  const result = arr.find((v) => v.openId === profile.openid && v.teamId === activeAccount.value?.teamId);
  if (result) {
    result.apply = type;
  } else {
    arr.push({ openId: profile.openid, teamId: activeAccount?.value.teamId, apply: type });
  }
  store.setStorePlatforms(arr);
};

// const onChangeCurrentMember = (e) => {
//   const result = memberCards.value.find((v) => v.id === e);
//   console.log(e, result, memberCards.value, '内容');
//   setMemberOraganizetion(e);
//   if (result) {
//     currentMemberCard.value = result;
//     // onLookDetail(result);
//     onSearch();
//   }
// };

const cardsModalRef = ref(null);
const onOpenCardsModal = () => {
  console.log(memberCards.value);
  console.log(cardsModalRef.value);
  // onGetMemberCardsAxios().then(
  //   (res: any) => {
  //     memberCards.value = res;
  //     currentMemberCard.value = res[0];
  //     cardsModalRef.value.onOpen();
  //   },
  //   () => {
  //     MessagePlugin.error('获取会员卡信息失败');
  //   }
  // );
  if (memberCards.value.length > 1) {
    cardsModalRef.value?.onOpen(memberCards.value, currentMemberCard.value);
  }
};

const onSetCard = (row) => {
  currentMemberCard.value = row;
  // if(!currentMemberCard.value?.is_connect) {
  //   onConnect(currentMemberCard.value);
  // }
  // digitalPlatformStore.setRecordTeamsSelectCardId({teamId: currentTeamId.value, cardId: currentMemberCard.value?.id})
  onInitData(true);
};

// 邀请入会
const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = () => {
  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== "Network Error") MessagePlugin.error(errMsg);
    }
  });
};

const onSearch = () => {
  onLookDetail(toRaw(currentMemberCard.value));
};

const onChangeCurrentTab = (e) => {
  console.log(e);
  currentTab.value = e;

  if (e === 1) {
    onSearch();
  }
};
const addMemberModalRef = ref(null);
// 编辑会员
const onEditMember = (row) => {
  onGetRegularDetailAxios(row).then((res) => {
    addMemberModalRef.value.onOpen(res, currentMemberCard.value);
  });
};

const lookRegularModalRef = ref(null);
const settingInfo = ref(null);
const resData = ref(null); // 用来判定有无数据

const isNetworkError = ref(false);
const isLoading = ref(false);
const onLookDetail = (row) => {
  // onGetRegularDetailAxios(row).then((res) => {
  //   console.log(res);
  //   lookRegularModalRef.value.onOpen(res);
  // });
  resData.value = null;
  console.log(row);
  // 缓存信息存储
  const caches = store.getStorageDatas || [];
  const cache = caches.find((v) => v.teamId === currentTeamId.value);
  if (!cache) {
    isLoading.value = true;
  }
  Promise.all([onGetRegularDetailAxios(row), onGetMemberSetting()])
    .then((res) => {
      console.log(res);
      resData.value = res;
      isLoading.value = false;

      isNetworkError.value = false;

      // 缓存处理 start
      const memberMeterials = {
        items: res || [],
      };
      if (cache) {
        cache.memberMeterials = memberMeterials;
      } else {
        caches.push({ teamId: currentTeamId.value, memberMeterials });
      }
      // 缓存处理end

      settingInfo.value = res[1];
      // 这里要座一层逻辑
      let detailItem: any = res[0];
      // 激活状态，1：已激活，2：未激活
      if (detailItem.activate === 2) {
        // 未激活，读取data数据
        detailItem.submit_data = lodash.cloneDeep(detailItem.data);
        console.log(detailItem.data);
        res[0] = detailItem;
      }
      // 特殊处理一下名录照片赋值问题2024-09-12 start-------------------------------
      if(detailItem?.submit_data?.free_form?.length > 0) {
        const freeForm = detailItem?.submit_data?.free_form;
        const baseList = freeForm.filter((v:any)=> {
          return ['BaseInfoMember'].includes(v.type)
        })
        console.log(baseList)
        baseList.map(async (v: any) => {
          // 会员级别的设置
          let origin = null;
          const origins_arr = v.origin;
          origin = origins_arr.find((or: any) => or.vModel === "nameLogo");
          if (origin) {
            origin.value =  detailItem?.submit_data?.directory_image_values  ||  origin.value  || [];
            // console.log(detailItem?.submit_data?.directory_image_values)
          }
          // console.log(origin);
        })
      }

      lookRegularModalRef.value?.onClose();
      setTimeout(() => {
        lookRegularModalRef.value?.onOpen(res);
      });
    })
    .catch((error) => {
      console.log("memberInfo: ", error);
      isLoading.value = false;

      if (error === "Network Error") {
        isNetworkError.value = true;
        if (!cache) {
          resData.value = null;
          return;
        }
        const res = cache.memberMeterials?.items || [];
        if (res.length < 1) {
          resData.value = null;
          return;
        }
        resData.value = res;
        settingInfo.value = res[1];

        // 这里要座一层逻辑
        let detailItem: any = res[0];
        // 激活状态，1：已激活，2：未激活
        if (detailItem.activate === 2) {
          // 未激活，读取data数据
          detailItem.submit_data = lodash.cloneDeep(detailItem.data);
          console.log(detailItem.data);
          res[0] = detailItem;
        }
        lookRegularModalRef.value?.onClose();
        setTimeout(() => {
          lookRegularModalRef.value?.onOpen(res);
        });
      }
    });
};

const addInMemberModalRef = ref(null);
const onShowMemberFlow = () => {
  console.log("点击了吗");
  addInMemberModalRef.value?.onOpen();
};

const onGetRegularDetailAxios = async (row) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getRegularDetailAxios(row?.id, {}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};

// 获取设置
const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};

const onGetMemberCardsAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberCardsAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      result.data?.map((v) => {
        // v.label = v.type === 1 ? v.team_name : v.name;
        v.logo = v.logo ? getSrcThumbnail(v.logo) : "";
        v.label = v.name;
        return v;
      });
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== "Network Error") MessagePlugin.error(errMsg);
    }
  });
};

let isWaiting = false;
const onInitData = (isCheckConnect = false) => {
  if (isWaiting) return;
  console.log("onInitData", digitalPlatformStore.inputeMemberCenter);
  console.log("route.query?.tab", route, route.query);
  const { query } = route;

  console.log("route.query", query);

  if (route.query?.tab === "meterials") {
    currentTab.value = 1;
  } else if (route.query?.tab === "advertisement") {
    currentTab.value = 3;
  } else if (route.query?.tab === "contact") {
    currentTab.value = 2;

    if(currentMemberCard.value?.type !== 1) {
      currentTab.value = 1;
    }
  } else {
    currentTab.value = 1;
  }


  onGetMemberCardsAxios().then(
    (res: any) => {
      console.log(res);
      if (res && res.length > 0) {
        memberCards.value = res.map((v) => {
          // v.label = v.type === 1 ? v.team_name : v.name;
          v.label = v.name;
          return v;
        });
        console.log(res);

        if (digitalPlatformStore.inputeMemberCenter) {
          digitalPlatformStore.setInputeMemberCenter(false);
          const id = getMemberCurrentCardID();
          currentMemberCard.value = memberCards.value.find((v) => v.id === Number(id));
        } else if (currentMemberCard.value) {
          console.log(currentMemberCard.value);
          currentMemberCard.value = memberCards.value.find((v) => v.id === currentMemberCard.value?.id);
          console.log(currentMemberCard.value);
        } else if (digitalPlatformStore.recordTeamsSelectMyPanelCardIds && digitalPlatformStore.recordTeamsSelectMyPanelCardIds.length > 0) {
          const result = digitalPlatformStore.recordTeamsSelectMyPanelCardIds.find((v) => v.teamId === currentTeamId.value);
          if (result) {
            currentMemberCard.value = memberCards.value.find((v) => v.id === result.cardId);
          } else {
            currentMemberCard.value = memberCards.value[0];
          }
        } else {
          currentMemberCard.value = res[0];
          console.log(currentMemberCard.value);
        }
        console.log(currentMemberCard.value);
        // currentMemberCard.value = currentMemberCard.value
        //   ? currentMemberCard.value
        //   : res[0];
        // onLookDetail(res[0]);

        // 这段代码绑定切换组织 pzy这个傻逼写的 别删
        // if (localStorageMemberOraganizetion()) {
        //   const a = JSON.parse(localStorageMemberOraganizetion());
        //   for (let aKey in a) {
        //     if (activeAccount.value.idTeam == aKey) {
        //       console.log('执行了吗', a[aKey], memberCards.value);
        //       onChangeCurrentMember(Number(a[aKey]));
        //     }
        //   }
        // }
        digitalPlatformStore.setRecordTeamsSelectMyPanelCardId({
          teamId: currentTeamId.value,
          cardId: currentMemberCard.value?.id,
        });

        if(isCheckConnect) {
          if(currentMemberCard.value?.type === 1 && !currentMemberCard.value?.is_connect) {
            onConnect(currentMemberCard.value);
            console.log('my-pandel')
          }
        }

        onSearch();
      } else {
        currentMemberCard.value = null;
      }
      isWaiting = false;
    },
    (err) => {
      currentMemberCard.value = null;
      isWaiting = false;
    },
  );
};

// watch(()=> digitalPlatformStore.inputeMemberCenter, async (val)=> {
//   if(val){
//     console.log('setInputeMemberCenter', route.query)

//     await onInitData();
//     // digitalPlatformStore.setInputeMemberCenter(false);
//   }
// })

const annualConnectRef = ref(null);

const onBackType = (res) => {
  if (res?.type === TeamAnnualTypeError.Success) {
    // MessagePlugin.success("连接成功");
    // 更新详情里面的数据
    // if(isShowNameDetail.value) {
    //   onGetMemberNameDetail(detailData.value).then((res:any) => {
    //     console.log(res)
    //     nameDetailModelRef.value?.onOpen(res.data);
    //   });
    // }

    // onSearch();
    onInitData();
  }
};
const onConnect = async (row) => {
  annualConnectRef.value.onConnectPlatform(row);
  // if(row.relation_team_id) {
  //   const params = {
  //     ...row,
  //     consume_team_id: row.relation_team_id,
  //     belong_team_id: currentTeamId.value,
  //   }
  //   try {
  //     const type = await onConnectPlatformOrConfirm(params, currentTeamId.value);
  //     if(type === TeamAnnualTypeError.OfficialPackageRenewal) { // 续费
  //       console.log('续费')
  //       annualFeeDialogRenewVisible.value = true;

  //     } else if(type === TeamAnnualTypeError.PurchaseOfficialPackage) { // 购买
  //       console.log('购买')
  //       openConnectVisible.value = true;
  //     } else if(type === TeamAnnualTypeError.UpgradeOfficialPackage) { // 升级
  //       console.log('升级')
  //       annualFeeDialogUpgradeConnectVisible.value = true;
  //     }
  //   } catch (error) {
  //     if(error) {
  //       MessagePlugin.error(error);
  //     }
  //   }

  // } else {
  //   // 若关联组织id为空，则需要确认连接组织，弹窗显示确认连接组织，如标记2，详细见>>确认连接组织
  //   // 确认连接弹窗，选择组织
  //   // console.log(activeAccount.value)
  //   selectOrganizeModalRef.value?.onOpen(row, toRaw(activeAccount.value));

  // }
};

onMounted(() => {
  // containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件

  // currentMemberCard.value = null;
  setTimeout(async () => {
    // onCheckIsAdmin(val?.staffId);
    // await onInitData();
    if (!digitalPlatformStore.inputeMemberCenter) {
      await onInitData(true);
    }
  });

  setTimeout(() => {
    // if(platformCpt.value === platform.digitalPlatform) {
    //   onCheckIsAdmin(digitalPlatformStore.activeAccount?.staffId);
    // } else {
    //   onCheckIsAdmin(store.activeAccount?.staffId);
    // }
    onCheckIsAdmin(store.value.activeAccount?.staffId);
  });
});

const setTab = (index) => {
  currentTab.value = index;
  if (currentTab.value === 1) {
    // currentTab.value = 1;
    route.query.tab = "meterials";
  } else if (currentTab.value === 3) {
    // currentTab.value = 3;
    route.query.tab = "advertisement";
  } else if (currentTab.value === 2) {
    route.query.tab = "contact";
  } else {
    route.query.tab = "meterials";
  }

  if (index === 1) {
    onSearch();
  }
};

/*
watch(
  () => store.activeAccount,
  (val) => {
    if (val) {
      currentMemberCard.value = null;
      setTimeout(async () => {
        // onCheckIsAdmin(val?.staffId);
        await onInitData();

      });

      setTimeout(() => {
        onCheckIsAdmin(val?.staffId);
      });

      console.log('内容，store.activeAccount', val, localStorageMemberOraganizetion());
    }
  },
  {
    deep: true,
    immediate: true
  }
);
*/

onMountedOrActivated(async () => {
  if (digitalPlatformStore.inputeMemberCenter) {
    await onInitData(true);
  }
});
const showCalling = ref(false);
const callingRef = ref(null);
const callingOpen = () => {
  callingRef.value.some();
};
const confirmLogo = (data) => {
  const params = {
    id: currentMemberCard.value?.id,
    directory_image_values: [data],
    is_contact: currentMemberCard.value?.is_contact,
  };
  editSpecifiedFields(params, currentTeamId.value)
    .then((res) => {
      if (res) {
        MessagePlugin.success("修改成功");
        onInitData();
      }
    })
    .catch((err) => {
      console.log(err);
      MessagePlugin.error(err?.message);
    });
};
</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_number/panel/member-panel/less/my-panel.less";
.logo-box {
  position: relative;
  cursor: pointer;
}
</style>
