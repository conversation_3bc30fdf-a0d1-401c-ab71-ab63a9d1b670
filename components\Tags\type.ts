export interface TagItemType {
  type?: TagTypeEnum.square | TagTypeEnum.round;
  name: keyof typeof TagNameEnum;
  label: string;
}

export interface TagsType {
  attrs?: Record<string, any>;
  list: TagItemType[];
}

export const enum TagTypeEnum {
  'square' = 'square',
  'round' = 'round',
}

export const enum TagNameEnum {
  'gray' = 'gray',
  'purple' = 'purple',
  'cyan' = 'cyan',
  'success' = 'success',
  'wrong' = 'wrong',
  'warning' = 'warning',
  'brand' = 'brand',
  'magenta' = 'magenta',
}
