# Tricks

使用技巧组件

```jsx
import { Tricks } from '@rk/unitPark';
import { getPageQuery } from '../../utils';
// 使用的时候要引入样式
// import '@rk/unitPark/dist/style.css';
const offset = { x: '-200', y: '-540' };
const token = getPageQuery('token');

export default () => (
  <>
    {/* <!-- 不能拖拽 --> */}
    <Tricks
      size="small"
      isDrag={false}
      uuid="内部身份"
      token={token}
      scene="3"
      noAutoCheck
    />
    {/* <!-- 可以拖拽 --> */}
    <Tricks
      offset={offset}
      uuid="内部身份"
      token={token}
      scene="3"
      noAutoCheck
    />
  </>
);
```

### Props

<API id="Tricks" type="props"></API>

### Events

<API id="Tricks" type="events"></API>
