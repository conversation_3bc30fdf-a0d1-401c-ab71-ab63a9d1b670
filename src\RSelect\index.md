# RSelect

选择器

```jsx
import { ref } from 'vue';
import { RSelect } from '@rk/unitPark';
import { NameEnum } from './type.ts';
import './style.css';
import 'tdesign-vue-next/es/style/index.css';

const list = ref([
  {
    name: NameEnum.success,
    label: '自提',
    value: '1',
  },
  {
    name: NameEnum.brand,
    label: '同城配送',
    value: '2',
  },
  {
    name: NameEnum.warning,
    label: '全国送',
    value: '3',
  },
  {
    label: '没有theme-color',
    value: '4',
  },
]);
const change = (info) => {
  // console.log(info.data.label)
  console.log('info', info);
};
const defaultInfo = ref({
  value: '1',
});

const tabList = ref({
  defaultInfo: {
    value: '1',
  },
  list: [
    {
      name: NameEnum.success,
      label: '自提',
      value: '1',
    },
    {
      name: NameEnum.brand,
      label: '同城配送',
      value: '2',
    },
    {
      name: NameEnum.warning,
      label: '全国送',
      value: '3',
    },
    {
      label: '没有theme-color',
      value: '4',
    },
  ],
});
setTimeout(() => {
  tabList.value.defaultInfo.value = '2';
}, 2000);

export default () => (
  <>
    <RSelect
      defaultInfo={{ value: '1' }}
      list={list.value}
      onChange={change}
    ></RSelect>
    <RSelect
      defaultInfo={tabList.value.defaultInfo}
      list={tabList.value.list}
      onChange={change}
    ></RSelect>
    <RSelect
      attrs={{ placeholder: '重选配送方式' }}
      defaultInfo={{ value: '' }}
      list={list}
      onChange={change}
    ></RSelect>
  </>
);
```

### Props

<API id="RSelect" type="props"></API>

### Events

<API id="RSelect" type="events"></API>
