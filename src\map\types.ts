/**
 * Location，位置
 */
export interface Location {
  /**
   * 详细地址
   */
  address?: string;
  latLng?: LatLng;
  /**
   * 名称
   */
  name?: string;
}

/**
 * LatLng，经纬度
 */
export interface LatLng {
  /**
   * 纬度
   */
  latitude?: number;
  /**
   * 经度
   */
  longitude?: number;
}

/**
 * 地图点坐标
 */
export interface MapPoint {
  lat: number;
  lng: number;
}

/**
 * 地图点击事件参数
 */
export interface MapClickEvent {
  latlng: MapPoint;
}

/**
 * 地图确认事件数据
 */
export interface MapConfirmData {
  location: MapPoint;
  name: string;
  address?: string;
  [key: string]: any;
}

/**
 * 搜索结果项
 */
export interface SearchResultItem {
  uid: string;
  name: string;
  address: string;
  location: MapPoint;
  [key: string]: any;
}

/**
 * 百度地图搜索API响应
 */
export interface BaiduMapSearchResponse {
  status: number;
  result: SearchResultItem[];
}

/**
 * 标记点信息
 */
export interface MarkerInfo {
  name: string;
  address: string;
  location: MapPoint;
  addressComponent?: any;
  surroundingPois?: any[];
  [key: string]: any;
}
