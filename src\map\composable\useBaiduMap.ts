import { onBeforeMount, ref, Ref } from 'vue';
import {
  Point,
  useIpLocation,
  usePointGeocoder,
  type PointGeocoderResult,
} from 'vue3-baidu-map-gl';
import { covertData, getDefaultLocation } from '../utils';

interface Location {
  point: Point;
  code: number;
  name: string;
}

interface BaiduMapOptions {
  // 由坐标点解析地址信息成功后的回调函数
  onPointGeocoder?: (
    result: Ref<PointGeocoderResult | PointGeocoderResult[] | null>,
  ) => void;
  // IP定位成功后的回调函数
  onIPLocation?: (location: Ref<Location>) => void;
}

/**
 * 使用百度地图 API 进行定位
 * @param options 配置
 * @param initPoint 初始化坐标点
 * @returns
 */
export const useBaiduMap = (
  options: BaiduMapOptions = {},
  initPoint: Ref<Point> = null,
) => {
  // HACK: md5 is not defined
  // https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
  let moduleObject: any;
  onBeforeMount(() => {
    moduleObject = (window as any).module;
    // eslint-disable-next-line no-global-assign
    (window as any).module = undefined;
  });
  const onBMapInitdHandler = () => {
    // eslint-disable-next-line no-global-assign
    (window as any).module = moduleObject;
  };

  const { onPointGeocoder, onIPLocation } = options;

  const city = ref('全国');
  const markerInfo = ref();

  // 添加地图初始化状态
  const mapInitialized = ref(false);

  // 由坐标点解析地址信息（逆向地理编码）
  const { get: getPointGeocoder, isEmpty } = usePointGeocoder({}, (res) => {
    if (isEmpty.value) return;

    markerInfo.value = covertData(res.value);
    onPointGeocoder?.(res);
  });

  // IP 定位（用于获取用户所在的城市位置信息，根据用户 IP 自动定位到城市）
  const { get: getIPLocation, location } = useIpLocation(() => {
    if (!location.value || !mapInitialized.value) return;

    // 定位失败时，point 为 undefined
    if (!location.value?.point) {
      location.value = getDefaultLocation();
    }
    const { point, name } = location.value;
    city.value = name;

    onIPLocation?.(location);
    getPointGeocoder(point);
  });

  // 地图初始化
  const onMapInit = () => {
    onBMapInitdHandler();
    mapInitialized.value = true;

    if (initPoint?.value) {
      getPointGeocoder(initPoint.value);
      return;
    }

    // 不指定目标地址时，定位到当前城市中心
    getIPLocation();
  };

  return {
    city,
    location,
    markerInfo,
    onMapInit,
    getIPLocation,
    getPointGeocoder,
    mapInitialized,
  };
};
