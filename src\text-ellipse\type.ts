export interface TextEllipsisProps {
  /** 需要展示的文本内容 */
  content?: string;
  /** 展示的行数 */
  rows?: number;
  /** 展开操作的文案 */
  expandText?: string;
  /** 收起操作的文案 */
  collapseText?: string;
  /** 省略符号 */
  dots?: string;
  /** 省略位置 */
  position?: 'start' | 'middle' | 'end';
}

export interface TextEllipsisEvents {
  'click-action': (event: MouseEvent) => void;
}

export interface TextEllipsisExpose {
  /** 切换展开/收起状态 */
  toggle: (expanded?: boolean) => void;
}
