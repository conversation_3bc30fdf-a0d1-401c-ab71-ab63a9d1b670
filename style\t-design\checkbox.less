// 单选
.t-radio.t-is-disabled .t-radio__input {
	background-color: #fff;
	border: 1px solid #D5DBE4;
}

.t-radio__label {
	color: #1A2139 !important;
}

//选中禁用状态
.t-radio.t-is-disabled.t-is-checked .t-radio__input {
	border-color: #C9CFFF;
	background: #C9CFFF;
}

.t-radio.t-is-disabled.t-is-checked .t-radio__input::after {
	background-color: #C9CFFF;
}

.t-radio__input {
	border: 1px solid #828DA5
}

//多选
//选中禁用状态
.t-radio.t-is-disabled.t-is-checked .t-radio__input {
	border-color: #C9CFFF;
	background: #C9CFFF;
}

.t-checkbox.t-is-disabled.t-is-checked .t-checkbox__input {
	border-color: var(--td-border-level-2-color);
	border-color: #C9CFFF !important;
}

//多选
.t-checkbox.t-is-checked .t-checkbox__input::after {
	border-color: var(--kyy_color_checkbox_icon_active, #FFF) !important;

}

.t-checkbox.t-is-indeterminate .t-checkbox__input::after {
	background-color: var(--color-button-primary-kyy-color-button-primary-bg-default, #fff) !important;

}

.t-is-checked {
	.t-checkbox__input {
		background:#4D5EFF !important;
		border-color: var(--kyy_color_checkbox_icon_active, #4D5EFF) !important;
	}

}

.t-checkbox__input {
	// background: #fff !important;
	// border-color: var(--kyy_color_checkbox_icon_active, #4D5EFF) !important;
}

.t-radio.t-is-checked .t-radio__input {
	background-color: var(--kyy_color_radioButton_icon_active, #4D5EFF);
}

.t-radio.t-is-checked .t-radio__input::after {
	content: "";
	position: absolute;
	box-sizing: border-box;
	opacity: 1;
	top: 15px;
	left: 11px;
	width: 5px;
	height: 9px;
	border: 2px solid var(--td-text-color-anti);
	border-radius: 0 0 1px;
	border-top: 0;
	border-left: 0;
	transform: rotate(45deg) scale(1) translate(-50%, -50%);
	background: transparent;
	border-color: #fff;
}