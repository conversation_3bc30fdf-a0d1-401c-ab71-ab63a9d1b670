<template>
  <div class="RK-Filter">
    <TInput
      v-model="searchVal"
      class="filter-input"
      clearable
      placeholder="请输入内容"
      v-bind="props.attrs"
      @change="searchChange"
    >
      <template #prefixIcon>
        <img
          style="width: 20px;height: 20px;"
          :src="iconSearchSvg"
          alt=""
        >
      </template>
    </TInput>
    <template v-if="advanced">
      <div
        class="filter-btn"
        :class="{ active: tags && tags.length > 0 }"
      >
        <t-button
          shape="rectangle"
          variant="outline"
          @click="onTriggerAdvanced"
        >
          <div class="filter-icon" />
        </t-button>
      </div>
      <TDrawer
        v-model:visible="advancedDrawer"
        class="advancedDrawer"
        size="472"
        v-bind="advanced.attrs"
        @close="handleClose"
      >
        <template #header>
          <div class="advance-filter-header">
            <div>{{ advanced.title || '高级筛选' }}</div>
            <img
              style="width: 24px;height: 24px;"
              src="../../assets/icon_close.png"
              alt=""
              @click="handleClose"
            >
          </div>
        </template>
        <div
          v-if="advanced.form"
          class="advanced-form"
        >
          <Form
            ref="advancedForm"
            :list="advanced.form.list"
            :submit="advanced.form.submit"
            :default-info="advancedDefault"
            @change="onAdvancedFormChange"
          />
        </div>
        <template #footer>
          <div class="advanced-box-footer">
            <t-button
              variant="outline"
              @click="resetFilterForm"
            >
              {{ advanced.resetText || '重置' }}
            </t-button>
            <t-button @click="onSubmit">
              {{ advanced.submitText || '搜索' }}
            </t-button>
          </div>
        </template>
      </TDrawer>
      <div
        v-if="tags && tags.length > 0"
        class="filter-result"
      >
        <div class="filter-result-title">
          高级筛选结果:
        </div>
        <TTag
          v-for="(tag, index) in tags"
          :key="index"
          :closable="true"
          @close="tagHandleClose(tag, index)"
        >
          {{ tag.content }}
        </TTag>
        <div
          class="clear"
          @click="onClearTag('all')"
        >
          <img
            class="clearIcon"
            src="../../assets/common/clear.svg"
          >
          清空
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts" name="RK-Filter">
import { ref, watch, computed } from 'vue';
import {
  Drawer as TDrawer,
  Input as TInput,
  Tag as TTag,
  Button as TButton,
} from 'tdesign-vue-next';
import Form from '../Form/index.vue';
import iconSearchSvg from '../../assets/icon_search.svg';

const props = defineProps({
  attrs: {
    type: Object,
    default: {}
  },
  defaultInfo: {
    type: Object,
    default: {}
  },
  advanced: {
    type: Object,
    default: () => {}
  },
});
const emit = defineEmits(['change', 'submit']);
const advanced = ref(props.advanced);
const advancedForm = ref({});
const searchVal = ref(props.defaultInfo?.searchVal || '');
const advancedDefault = ref(props.advanced?.form?.defaultInfo || {});
const advancedDrawer = ref(false);
const tags = ref([]);

const filterItems = ref({});

const formData = ref({});
// 表单值变化
const onAdvancedFormChange = ({ name }, changedItem) => {
  console.log('高级筛选表单变化', name, changedItem);
  // 记录表单值变化，用于动态选项的组件回显
  formData.value[name] = changedItem;
}

const setTags = () => {
  const data = advancedForm.value?.formData;
  console.log('setTags', data);
  if (!data || Object.keys(data)?.length <= 0){
    return []
  }
  const list = []
  advanced.value?.form?.list.forEach(v => {
    if (data[v.name] !== undefined && data[v.name] !== '' && (Array.isArray(data[v.name]) ? data[v.name].length > 0 : true)) {
      if (v.type === 'dateRange') {
        if (!data[v.name] || !data[v.name]?.[0] || !data[v.name]?.[1]) {
          return;
        }
      }
      const resultText = ref(null);
      // 设置数据回显
      if(typeof v.attrs?.resultText === 'function') {
        resultText.value = v.attrs.resultText(data, filterItems.value);
      } else if (v.type === 'select') {
        const opt = v.attrs.options
        let selected;

        if(v.attrs?.multiple){
          selected = opt.filter(item => data[v.name].includes(item.value));
          resultText.value = selected.map(item => item.label).join('、');
        } else {
          selected = opt.find(item => item.value === data[v.name]);
          resultText.value = selected?.label;
        }
      } else if (v.type === 'date') {
        resultText.value = data[v.name];
      } else if (v.type === 'dateRange') {
        resultText.value = data[v.name] ? `${data[v.name]?.[0]} ~  ${data[v.name]?.[1]}` : [];
      } else {
        resultText.value = data[v.name];
      }

      const label = v.attrs?.tagLabel ? v.attrs?.tagLabel: v.label;
      const tag = {
        content: `${label}: ${resultText.value}`,
        name: v.name,
        value: data[v.name]
      }
      list.push(tag)
    }
  })
  tags.value = list;
}

watch(() => props.advanced, (val) => {
  if (val) {
    advanced.value = val;
  }
});

const tagHandleClose = (tag, index) => {
  onClearTag(tag, index);
}

const handleClose = () => {
  const data = {};
  advanced.value?.form?.list.forEach(v => {
    const val = filterItems.value[v.name];
    if (val) {
      data[v.name] = val;
    } else {
      if (v.attrs?.multiple || v.type === 'dateRange'){
        data[v.name] = [];
      } else {
        data[v.name] = ''
      }
    }
  });
  advancedForm.value.setformData(data);
  advancedDrawer.value = false;
}

const resetFilterForm = () => {
  const data = {} // 默认是all
  advanced.value?.form?.list.map(v => {
    if(v.attrs?.multiple || v.type === 'dateRange'){
      data[v.name] = [];
    } else {
      data[v.name] = ''
    }
  });
  clear(data, false);
}

const onClearTag = (tag, index) => {
  const data = {} // 默认是all
  if (tag !== 'all') {
    advanced.value?.form?.list.map(v => {
      if (tag.name === v.name) {
        if(v.attrs?.multiple || v.type === 'dateRange'){
          data[v.name] = [];
        } else {
          data[v.name] = ''
        }
      }
    })
  } else {
    advanced.value?.form?.list.map(v => {
      if(v.attrs?.multiple || v.type === 'dateRange'){
        data[v.name] = [];
      } else {
        data[v.name] = ''
      }
    })
  }
  clear(data)
}
const clear = (data, isTriggerChange = true) => {
  console.log('clear', data);
  if (!data) {
    searchVal.value = '';
  }
  advancedForm.value.setformData(data || {});
  filterItems.value = {};
  if(isTriggerChange){
    setTags();
    onChange();
  }
}
const onTriggerAdvanced = () => {
  advancedDrawer.value = !advancedDrawer.value
};
const searchChange = (item) => {
  console.log('searchChange', item);
  searchVal.value = item;
  onChange();
};
const onSubmit = () => {
  console.log('onSubmit')
  advancedDrawer.value = false;
  filterItems.value = { ...formData.value };
  setTags();
  onChange();
};
const onChange = () => {
  let data:any = {};
  if (advanced.value && advanced.value?.form) {
    data = { ...advancedForm.value?.formData }
  };
  data.searchVal = searchVal.value
  emit('change', data);
};

defineExpose({
  clear
})

</script>

<style lang="less">
.RK-Filter {
  .filter-input{
    width: 304px;
    .t-input__inner{
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
  .filter-input,
  .filter-btn{
    display: inline-block;
    vertical-align: middle;
    margin-right: 8px;
  }
  .filter-btn {
    height: 32px;
    font-size: 32px;
    width: 32px;
    text-align: center;
    padding: 0;
    > .t-button {
      width: 100%;
    }

    &:hover {
      .filter-icon{
        background-image: url('../../assets/common/filter_active.svg');
      }
    }

    &.active {
      > .t-button {
        border-color: #707EFF !important;
        color: #707EFF !important;
        background-color: rgba(76, 94, 255, 0.12) !important;
      }

     .filter-icon{
        background-image: url('../../assets/common/filter_active.svg');
      }
    }
  }
  .filter-icon{
    background: url("../../assets/common/filter.svg") no-repeat center / 100% 100%;
    height: 20px;
    width: 20px;
  }
  .filter-result{
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .t-tag,
    .t-tag .t-tag--text,
    .clear,
    .filter-result-title{
      display: inline-block;
      vertical-align: middle;
      font-size: 14px;
    }
    .t-tag,
    .clear{
      flex-shrink: 0;
    }
    .filter-result-title{
      color: #516082;
      font-weight: 600;
      flex-shrink: 0;
    }
    .t-tag{
      color: #1A2139;
      font-weight: 400;
      height: 24px;
      padding: 0 8px!important;
    }
    .clear{
      // background-image: url("../../assets/common/clear.svg");
      // background-repeat: no-repeat;
      color: #4D5EFF;
      font-size: 14px;
      //padding-left: 20px;
      background-position: 0 center;
      cursor: pointer;
      .clearIcon{
        display: inline-block;
        vertical-align: sub;
      }
    }
  }
}

.advance-filter-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  img{
    cursor: pointer;
  }
}

.advanced-form {
  .t-form__label {
    float: none;
    width: 0!important;
  }
  .t-form__controls {
    margin-left: 0!important;
  }
}
</style>
<style lang="less" scoped>
:deep(.advancedDrawer){
  .t-drawer__body{
    padding: 12px 24px;
  }
  .advanced-box-footer{
    text-align: right;
    .t-button {
      width: 80px;
      font-weight: 600;
    }
  }
}
</style>

